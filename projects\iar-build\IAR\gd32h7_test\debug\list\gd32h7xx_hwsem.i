














 















































































































































































































































 


























 






 


























 







 


















 

 




 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
typedef enum IRQn {
     
    NonMaskableInt_IRQn         = -14,     
    HardFault_IRQn              = -13,     
    MemoryManagement_IRQn       = -12,     
    BusFault_IRQn               = -11,     
    UsageFault_IRQn             = -10,     
    SVCall_IRQn                 = -5,      
    DebugMonitor_IRQn           = -4,      
    PendSV_IRQn                 = -2,      
    SysTick_IRQn                = -1,      
     
    WWDGT_IRQn                  = 0,       
    VAVD_LVD_VOVD_IRQn          = 1,       
    TAMPER_STAMP_LXTAL_IRQn     = 2,       
    RTC_WKUP_IRQn               = 3,       
    FMC_IRQn                    = 4,       
    RCU_IRQn                    = 5,       
    EXTI0_IRQn                  = 6,       
    EXTI1_IRQn                  = 7,       
    EXTI2_IRQn                  = 8,       
    EXTI3_IRQn                  = 9,       
    EXTI4_IRQn                  = 10,      
    DMA0_Channel0_IRQn          = 11,      
    DMA0_Channel1_IRQn          = 12,      
    DMA0_Channel2_IRQn          = 13,      
    DMA0_Channel3_IRQn          = 14,      
    DMA0_Channel4_IRQn          = 15,      
    DMA0_Channel5_IRQn          = 16,      
    DMA0_Channel6_IRQn          = 17,      
    ADC0_1_IRQn                 = 18,      
    EXTI5_9_IRQn                = 23,      
    TIMER0_BRK_IRQn             = 24,      
    TIMER0_UP_IRQn              = 25,      
    TIMER0_TRG_CMT_IRQn         = 26,      
    TIMER0_Channel_IRQn         = 27,      
    TIMER1_IRQn                 = 28,      
    TIMER2_IRQn                 = 29,      
    TIMER3_IRQn                 = 30,      
    I2C0_EV_IRQn                = 31,      
    I2C0_ER_IRQn                = 32,      
    I2C1_EV_IRQn                = 33,      
    I2C1_ER_IRQn                = 34,      
    SPI0_IRQn                   = 35,      
    SPI1_IRQn                   = 36,      
    USART0_IRQn                 = 37,      
    USART1_IRQn                 = 38,      
    USART2_IRQn                 = 39,      
    EXTI10_15_IRQn              = 40,      
    RTC_Alarm_IRQn              = 41,      
    TIMER7_BRK_IRQn             = 43,      
    TIMER7_UP_IRQn              = 44,      
    TIMER7_TRG_CMT_IRQn         = 45,      
    TIMER7_Channel_IRQn         = 46,      
    DMA0_Channel7_IRQn          = 47,      
    EXMC_IRQn                   = 48,      
    SDIO0_IRQn                  = 49,      
    TIMER4_IRQn                 = 50,      
    SPI2_IRQn                   = 51,      
    UART3_IRQn                  = 52,      
    UART4_IRQn                  = 53,      
    TIMER5_DAC_UDR_IRQn         = 54,      
    TIMER6_IRQn                 = 55,      
    DMA1_Channel0_IRQn          = 56,      
    DMA1_Channel1_IRQn          = 57,      
    DMA1_Channel2_IRQn          = 58,      
    DMA1_Channel3_IRQn          = 59,      
    DMA1_Channel4_IRQn          = 60,      
    ENET0_IRQn                  = 61,      
    ENET0_WKUP_IRQn             = 62,      
    DMA1_Channel5_IRQn          = 68,      
    DMA1_Channel6_IRQn          = 69,      
    DMA1_Channel7_IRQn          = 70,      
    USART5_IRQn                 = 71,      
    I2C2_EV_IRQn                = 72,      
    I2C2_ER_IRQn                = 73,      
    USBHS0_EP1_OUT_IRQn         = 74,      
    USBHS0_EP1_IN_IRQn          = 75,      
    USBHS0_WKUP_IRQn            = 76,      
    USBHS0_IRQn                 = 77,      
    DCI_IRQn                    = 78,      
    CAU_IRQn                    = 79,      
    HAU_TRNG_IRQn               = 80,      
    FPU_IRQn                    = 81,      
    UART6_IRQn                  = 82,      
    UART7_IRQn                  = 83,      
    SPI3_IRQn                   = 84,      
    SPI4_IRQn                   = 85,      
    SPI5_IRQn                   = 86,      
    SAI0_IRQn                   = 87,      
    TLI_IRQn                    = 88,      
    TLI_ER_IRQn                 = 89,      
    IPA_IRQn                    = 90,      
    SAI1_IRQn                   = 91,      
    OSPI0_IRQn                  = 92,      
    I2C3_EV_IRQn                = 95,      
    I2C3_ER_IRQn                = 96,      
    RSPDIF_IRQn                 = 97,      
    DMAMUX_OVR_IRQn             = 102,     
    HPDF_INT0_IRQn              = 110,     
    HPDF_INT1_IRQn              = 111,     
    HPDF_INT2_IRQn              = 112,     
    HPDF_INT3_IRQn              = 113,     
    SAI2_IRQn                   = 114,     
    TIMER14_IRQn                = 116,     
    TIMER15_IRQn                = 117,     
    TIMER16_IRQn                = 118,     
    MDIO_IRQn                   = 120,     
    MDMA_IRQn                   = 122,     
    SDIO1_IRQn                  = 124,     
    HWSEM_IRQn                  = 125,     
    ADC2_IRQn                   = 127,     
    CMP0_1_IRQn                 = 137,     
    CTC_IRQn                    = 144,     
    RAMECCMU_IRQn               = 145,     
    OSPI1_IRQn                  = 150,     
    RTDEC0_IRQn                 = 151,     
    RTDEC1_IRQn                 = 152,     
    FAC_IRQn                    = 153,     
    TMU_IRQn                    = 154,     
    TIMER22_IRQn                = 161,     
    TIMER23_IRQn                = 162,     
    TIMER30_IRQn                = 163,     
    TIMER31_IRQn                = 164,     
    TIMER40_IRQn                = 165,     
    TIMER41_IRQn                = 166,     
    TIMER42_IRQn                = 167,     
    TIMER43_IRQn                = 168,     
    TIMER44_IRQn                = 169,     
    TIMER50_IRQn                = 170,     
    TIMER51_IRQn                = 171,     
    USBHS1_EP1_OUT_IRQn         = 172,     
    USBHS1_EP1_IN_IRQn          = 173,     
    USBHS1_WKUP_IRQn            = 174,     
    USBHS1_IRQn                 = 175,     
    ENET1_IRQn                  = 176,     
    ENET1_WKUP_IRQn             = 177,     
    CAN0_WKUP_IRQn              = 179,     
    CAN0_Message_IRQn           = 180,     
    CAN0_Busoff_IRQn            = 181,     
    CAN0_Error_IRQn             = 182,     
    CAN0_FastError_IRQn         = 183,     
    CAN0_TEC_IRQn               = 184,     
    CAN0_REC_IRQn               = 185,     
    CAN1_WKUP_IRQn              = 186,     
    CAN1_Message_IRQn           = 187,     
    CAN1_Busoff_IRQn            = 188,     
    CAN1_Error_IRQn             = 189,     
    CAN1_FastError_IRQn         = 190,     
    CAN1_TEC_IRQn               = 191,     
    CAN1_REC_IRQn               = 192,     
    CAN2_WKUP_IRQn              = 193,     
    CAN2_Message_IRQn           = 194,     
    CAN2_Busoff_IRQn            = 195,     
    CAN2_Error_IRQn             = 196,     
    CAN2_FastError_IRQn         = 197,     
    CAN2_TEC_IRQn               = 198,     
    CAN2_REC_IRQn               = 199,     
    EFUSE_IRQn                  = 200,     
    I2C0_WKUP_IRQn              = 201,     
    I2C1_WKUP_IRQn              = 202,     
    I2C2_WKUP_IRQn              = 203,     
    I2C3_WKUP_IRQn              = 204,     
    LPDTS_IRQn                  = 205,     
    LPDTS_WKUP_IRQn             = 206,     
    TIMER0_DEC_IRQn             = 207,     
    TIMER7_DEC_IRQn             = 208,     
    TIMER1_DEC_IRQn             = 209,     
    TIMER2_DEC_IRQn             = 210,     
    TIMER3_DEC_IRQn             = 211,     
    TIMER4_DEC_IRQn             = 212,     
    TIMER22_DEC_IRQn            = 213,     
    TIMER23_DEC_IRQn            = 214,     
    TIMER30_DEC_IRQn            = 215,     
    TIMER31_DEC_IRQn            = 216,     
} IRQn_Type;

 
 




 
















 

  #pragma system_include          


 
 

  #pragma system_include

 
 

 

  #pragma system_include














 


 
 


  #pragma system_include

 



 

 

 

 
#pragma rtmodel = "__dlib_version", "6"

 


 



























 


  #pragma system_include

 
 
 


  #pragma system_include

 

 

 

 

   

 
 


   #pragma system_include






 




 


 


 


 

 


 

 

 

 

 

 

 

 

 















 



















 











 























 





 



 










 














 













 








 













 













 















 











 








 








 






 





 












 





 













 






 


   


  







 







 




 






 




 




 













 

   




 







 







 







 










 





 

















 


 


 













 

   


 


 



 

 

 
  typedef unsigned int _Wchart;
  typedef unsigned int _Wintt;

 

 
typedef unsigned int     _Sizet;

 
typedef signed char   __int8_t;
typedef unsigned char  __uint8_t;
typedef signed short int   __int16_t;
typedef unsigned short int  __uint16_t;
typedef signed int   __int32_t;
typedef unsigned int  __uint32_t;
   typedef signed long long int   __int64_t;
   typedef unsigned long long int  __uint64_t;
typedef signed int   __intptr_t;
typedef unsigned int  __uintptr_t;

 
typedef struct _Mbstatet
{  
    unsigned int _Wchar;   
    unsigned int _State;   

} _Mbstatet;

 

 
  typedef struct __va_list __Va_list;


    typedef struct __FILE _Filet;

 
typedef struct
{
    long long _Off;     
  _Mbstatet _Wstate;
} _Fpost;


 

 
  
   
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_StaticGuard(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_StaticGuard(void);

      _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockfilelock(_Filet *);
      _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockfilelock(_Filet *);

  typedef void *__iar_Rmtx;

  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Initdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Dstdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockdynamiclock(__iar_Rmtx *);

  






 


 
  typedef signed char          int8_t;
  typedef unsigned char        uint8_t;

  typedef signed short int         int16_t;
  typedef unsigned short int       uint16_t;

  typedef signed int         int32_t;
  typedef unsigned int       uint32_t;

  typedef signed long long int         int64_t;
  typedef unsigned long long int       uint64_t;


 
typedef signed char      int_least8_t;
typedef unsigned char    uint_least8_t;

typedef signed short int     int_least16_t;
typedef unsigned short int   uint_least16_t;

typedef signed int     int_least32_t;
typedef unsigned int   uint_least32_t;

 
  typedef signed long long int   int_least64_t;
  typedef unsigned long long int uint_least64_t;



 
typedef signed int       int_fast8_t;
typedef unsigned int     uint_fast8_t;

typedef signed int      int_fast16_t;
typedef unsigned int    uint_fast16_t;

typedef signed int      int_fast32_t;
typedef unsigned int    uint_fast32_t;

  typedef signed long long int    int_fast64_t;
  typedef unsigned long long int  uint_fast64_t;

 
typedef signed long long int          intmax_t;
typedef unsigned long long int        uintmax_t;


 
typedef signed int          intptr_t;
typedef unsigned int        uintptr_t;

 
typedef int __data_intptr_t; typedef unsigned int __data_uintptr_t;

 






















 











 














 




 



 

 




 
















 

  #pragma system_include          


 

 




 


 




 
















 





 
 




 
























#pragma system_include






 

 
















#pragma language=save
#pragma language=extended
_Pragma("inline=forced") __intrinsic uint16_t __iar_uint16_read(void const *ptr)
{
  return *(__packed uint16_t*)(ptr);
}
#pragma language=restore


#pragma language=save
#pragma language=extended
_Pragma("inline=forced") __intrinsic void __iar_uint16_write(void const *ptr, uint16_t val)
{
  *(__packed uint16_t*)(ptr) = val;;
}
#pragma language=restore

#pragma language=save
#pragma language=extended
_Pragma("inline=forced") __intrinsic uint32_t __iar_uint32_read(void const *ptr)
{
  return *(__packed uint32_t*)(ptr);
}
#pragma language=restore

#pragma language=save
#pragma language=extended
_Pragma("inline=forced") __intrinsic void __iar_uint32_write(void const *ptr, uint32_t val)
{
  *(__packed uint32_t*)(ptr) = val;;
}
#pragma language=restore

#pragma language=save
#pragma language=extended
__packed struct  __iar_u32 { uint32_t v; };
#pragma language=restore



















 



  #pragma system_include



 


 


#pragma language=save
#pragma language=extended

__intrinsic __nounwind void    __iar_builtin_no_operation(void);

__intrinsic __nounwind void    __iar_builtin_disable_interrupt(void);
__intrinsic __nounwind void    __iar_builtin_enable_interrupt(void);

typedef unsigned int __istate_t;

__intrinsic __nounwind __istate_t __iar_builtin_get_interrupt_state(void);
__intrinsic __nounwind void __iar_builtin_set_interrupt_state(__istate_t);

 
__intrinsic __nounwind unsigned int __iar_builtin_get_PSR( void );
__intrinsic __nounwind unsigned int __iar_builtin_get_IPSR( void );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_get_MSP( void );
__intrinsic __nounwind void         __iar_builtin_set_MSP( unsigned int );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_get_PSP( void );
__intrinsic __nounwind void         __iar_builtin_set_PSP( unsigned int );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_get_PRIMASK( void );
__intrinsic __nounwind void         __iar_builtin_set_PRIMASK( unsigned int );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_get_CONTROL( void );
__intrinsic __nounwind void         __iar_builtin_set_CONTROL( unsigned int );

 
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_get_FAULTMASK( void );
__intrinsic __nounwind void         __iar_builtin_set_FAULTMASK(unsigned int);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_get_BASEPRI( void );
__intrinsic __nounwind void         __iar_builtin_set_BASEPRI( unsigned int );

 
__intrinsic __nounwind void __iar_builtin_disable_irq(void);
__intrinsic __nounwind void __iar_builtin_enable_irq(void);

__intrinsic __nounwind void __iar_builtin_disable_fiq(void);
__intrinsic __nounwind void __iar_builtin_enable_fiq(void);


 

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SWP( unsigned int, volatile unsigned int * );
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned char __iar_builtin_SWPB( unsigned char, volatile unsigned char * );

typedef unsigned int __ul;
typedef unsigned int __iar_builtin_uint;


 

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind void __iar_builtin_CDP (unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) opc1, unsigned __constrange(0,15) CRd, unsigned __constrange(0,15) CRn, unsigned __constrange(0,15) CRm, unsigned __constrange(0,8) opc2) ;
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind void __iar_builtin_CDP2(unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) opc1, unsigned __constrange(0,15) CRd, unsigned __constrange(0,15) CRn, unsigned __constrange(0,15) CRm, unsigned __constrange(0,8) opc2) ;

 
__intrinsic __nounwind void          __iar_builtin_MCR( unsigned __constrange(0,15) coproc, unsigned __constrange(0,8) opcode_1, __iar_builtin_uint src,
                                unsigned __constrange(0,15) CRn, unsigned __constrange(0,15) CRm, unsigned __constrange(0,8) opcode_2 )  ;
__intrinsic __nounwind unsigned int __iar_builtin_MRC( unsigned __constrange(0,15) coproc, unsigned __constrange(0,8) opcode_1,
                                unsigned __constrange(0,15) CRn, unsigned __constrange(0,15) CRm, unsigned __constrange(0,8) opcode_2 )  ;
__intrinsic __nounwind void          __iar_builtin_MCR2( unsigned __constrange(0,15) coproc, unsigned __constrange(0,8) opcode_1, __iar_builtin_uint src,
                                 unsigned __constrange(0,15) CRn, unsigned __constrange(0,15) CRm, unsigned __constrange(0,8) opcode_2 ) ;
__intrinsic __nounwind unsigned int __iar_builtin_MRC2( unsigned __constrange(0,15) coproc, unsigned __constrange(0,8) opcode_1,
                                 unsigned __constrange(0,15) CRn, unsigned __constrange(0,15) CRm, unsigned __constrange(0,8) opcode_2 ) ;

__intrinsic __nounwind void __iar_builtin_MCRR (unsigned __constrange(0,15) coproc, unsigned __constrange(0,8) opc1, unsigned long long src, unsigned __constrange(0,15) CRm) ;
__intrinsic __nounwind void __iar_builtin_MCRR2(unsigned __constrange(0,15) coproc, unsigned __constrange(0,8) opc1, unsigned long long src, unsigned __constrange(0,15) CRm) ;

__intrinsic __nounwind unsigned long long __iar_builtin_MRRC (unsigned __constrange(0,15) coproc, unsigned __constrange(0,8) opc1, unsigned __constrange(0,15) CRm) ;
__intrinsic __nounwind unsigned long long __iar_builtin_MRRC2(unsigned __constrange(0,15) coproc, unsigned __constrange(0,8) opc1, unsigned __constrange(0,15) CRm) ;

 
__intrinsic __nounwind void __iar_builtin_LDC  ( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint const *src) ;
__intrinsic __nounwind void __iar_builtin_LDCL ( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint const *src) ;
__intrinsic __nounwind void __iar_builtin_LDC2 ( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint const *src) ;
__intrinsic __nounwind void __iar_builtin_LDC2L( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint const *src) ;

 
__intrinsic __nounwind void __iar_builtin_STC  ( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint *dst) ;
__intrinsic __nounwind void __iar_builtin_STCL ( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint *dst) ;
__intrinsic __nounwind void __iar_builtin_STC2 ( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint *dst) ;
__intrinsic __nounwind void __iar_builtin_STC2L( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint *dst) ;

 
__intrinsic __nounwind void __iar_builtin_LDC_noidx( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint const *src,
                              unsigned __constrange(0,255) option);

__intrinsic __nounwind void __iar_builtin_LDCL_noidx( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint const *src,
                               unsigned __constrange(0,255) option);

__intrinsic __nounwind void __iar_builtin_LDC2_noidx( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint const *src,
                               unsigned __constrange(0,255) option);

__intrinsic __nounwind void __iar_builtin_LDC2L_noidx( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint const *src,
                                unsigned __constrange(0,255) option);

 
__intrinsic __nounwind void __iar_builtin_STC_noidx( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint *dst,
                              unsigned __constrange(0,255) option);

__intrinsic __nounwind void __iar_builtin_STCL_noidx( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint *dst,
                               unsigned __constrange(0,255) option);

__intrinsic __nounwind void __iar_builtin_STC2_noidx( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint *dst,
                               unsigned __constrange(0,255) option);

__intrinsic __nounwind void __iar_builtin_STC2L_noidx( unsigned __constrange(0,15) coproc, unsigned __constrange(0,15) CRn, volatile __iar_builtin_uint *dst,
                                unsigned __constrange(0,255) option);


 
__intrinsic __nounwind unsigned int       __iar_builtin_rsr(__spec_string const char * special_register)   ;
__intrinsic __nounwind unsigned long long __iar_builtin_rsr64(__spec_string const char * special_register) ;
__intrinsic __nounwind void*              __iar_builtin_rsrp(__spec_string const char * special_register)  ;

 
__intrinsic __nounwind void __iar_builtin_wsr(__spec_string const char * special_register, unsigned int value)         ;
__intrinsic __nounwind void __iar_builtin_wsr64(__spec_string const char * special_register, unsigned long long value) ;
__intrinsic __nounwind void __iar_builtin_wsrp(__spec_string const char * special_register, const void *value)         ;

 
__intrinsic __nounwind unsigned int __iar_builtin_get_APSR( void );
__intrinsic __nounwind void         __iar_builtin_set_APSR( unsigned int );

 
__intrinsic __nounwind unsigned int __iar_builtin_get_CPSR( void );
__intrinsic __nounwind void         __iar_builtin_set_CPSR( unsigned int );

 
__intrinsic __nounwind unsigned int __iar_builtin_get_FPSCR( void );
__intrinsic __nounwind void __iar_builtin_set_FPSCR( unsigned int );

 
 
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_CLZ(unsigned int) ;
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_ROR(unsigned int, unsigned int) ;
__intrinsic __nounwind unsigned int __iar_builtin_RRX(unsigned int);

 
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind signed int __iar_builtin_QADD( signed int, signed int );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind signed int __iar_builtin_QDADD( signed int, signed int );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind signed int __iar_builtin_QSUB( signed int, signed int );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind signed int __iar_builtin_QDSUB( signed int, signed int );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind signed int __iar_builtin_QDOUBLE( signed int );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int        __iar_builtin_QFlag( void );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int   __iar_builtin_acle_QFlag(void);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind void  __iar_builtin_set_QFlag(int);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind void  __iar_builtin_ignore_QFlag(void);

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int         __iar_builtin_QCFlag( void );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind void __iar_builtin_reset_QC_flag( void );

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind signed int __iar_builtin_SMUL( signed short, signed short );

 
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_REV( unsigned int );
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind signed int __iar_builtin_REVSH( short );

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_REV16( unsigned int );
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_RBIT( unsigned int );

__intrinsic __nounwind unsigned char  __iar_builtin_LDREXB( volatile unsigned char const * );
__intrinsic __nounwind unsigned short __iar_builtin_LDREXH( volatile unsigned short const * );
__intrinsic __nounwind unsigned int  __iar_builtin_LDREX ( volatile unsigned int const * );
__intrinsic __nounwind unsigned long long __iar_builtin_LDREXD( volatile unsigned long long const * );

__intrinsic __nounwind unsigned int  __iar_builtin_STREXB( unsigned char, volatile unsigned char * );
__intrinsic __nounwind unsigned int  __iar_builtin_STREXH( unsigned short, volatile unsigned short * );
__intrinsic __nounwind unsigned int  __iar_builtin_STREX ( unsigned int, volatile unsigned int * );
__intrinsic __nounwind unsigned int  __iar_builtin_STREXD( unsigned long long, volatile unsigned long long * );

__intrinsic __nounwind void __iar_builtin_CLREX( void );

__intrinsic __nounwind void __iar_builtin_SEV( void );
__intrinsic __nounwind void __iar_builtin_WFE( void );
__intrinsic __nounwind void __iar_builtin_WFI( void );
__intrinsic __nounwind void __iar_builtin_YIELD( void );

__intrinsic __nounwind void __iar_builtin_PLI( volatile void const * );
__intrinsic __nounwind void __iar_builtin_PLD( volatile void const * );

__intrinsic __nounwind void __iar_builtin_PLIx( volatile void const *, unsigned int __constrange(0,2), unsigned int __constrange(0,1));
__intrinsic __nounwind void __iar_builtin_PLDx( volatile void const *, unsigned int __constrange(0, 1), unsigned int __constrange(0, 2), unsigned int __constrange(0, 1));
__intrinsic __nounwind void __iar_builtin_PLDW( volatile void const * );

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind signed int   __iar_builtin_SSAT     (signed int val, unsigned int __constrange( 1, 32 ) sat );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_USAT     (signed int val, unsigned int __constrange( 0, 31 ) sat );

 
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SEL( unsigned int op1, unsigned int op2 );

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SADD8    (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SADD16   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SSUB8    (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SSUB16   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SADDSUBX (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SSUBADDX (unsigned int pair1, unsigned int pair2);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SHADD8   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SHADD16  (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SHSUB8   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SHSUB16  (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SHADDSUBX(unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SHSUBADDX(unsigned int pair1, unsigned int pair2);

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_QADD8    (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_QADD16   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_QSUB8    (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_QSUB16   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_QADDSUBX (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_QSUBADDX (unsigned int pair1, unsigned int pair2);

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UADD8    (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UADD16   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_USUB8    (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_USUB16   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UADDSUBX (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_USUBADDX (unsigned int pair1, unsigned int pair2);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UHADD8   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UHADD16  (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UHSUB8   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UHSUB16  (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UHADDSUBX(unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UHSUBADDX(unsigned int pair1, unsigned int pair2);

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UQADD8   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UQADD16  (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UQSUB8   (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UQSUB16  (unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UQADDSUBX(unsigned int pair1, unsigned int pair2);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UQSUBADDX(unsigned int pair1, unsigned int pair2);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_USAD8(unsigned int x, unsigned int y );
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_USADA8(unsigned int x, unsigned int y,
                                   unsigned int acc );

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SSAT16   (unsigned int pair,
                                      unsigned int __constrange( 1, 16 ) sat );
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_USAT16   (unsigned int pair,
                                      unsigned int __constrange( 0, 15 ) sat );

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMUAD (unsigned int x, unsigned int y);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMUSD (unsigned int x, unsigned int y);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMUADX(unsigned int x, unsigned int y);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMUSDX(unsigned int x, unsigned int y);

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMLAD (unsigned int x, unsigned int y, int sum);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMLSD (unsigned int x, unsigned int y, int sum);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMLADX(unsigned int x, unsigned int y, int sum);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMLSDX(unsigned int x, unsigned int y, int sum);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind long long __iar_builtin_SMLALD (unsigned int pair1,
                                 unsigned int pair2,
                                 long long acc);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind long long __iar_builtin_SMLALDX(unsigned int pair1,
                                 unsigned int pair2,
                                 long long acc);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind long long __iar_builtin_SMLSLD (unsigned int pair1,
                                 unsigned int pair2,
                                 long long acc);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind long long __iar_builtin_SMLSLDX(unsigned int pair1,
                                 unsigned int pair2,
                                 long long acc);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_PKHBT(unsigned int x,
                                  unsigned int y,
                                  unsigned __constrange(0,31) count);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_PKHTB(unsigned int x,
                                  unsigned int y,
                                  unsigned __constrange(0,32) count);

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMLABB(unsigned int x, unsigned int y, int acc);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMLABT(unsigned int x, unsigned int y, int acc);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMLATB(unsigned int x, unsigned int y, int acc);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMLATT(unsigned int x, unsigned int y, int acc);

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMLAWB(int x, unsigned int y, int acc);
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMLAWT(int x, unsigned int y, int acc);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMMLA (int x, int y, int acc);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMMLAR(int x, int y, int acc);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMMLS (int x, int y, int acc);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMMLSR(int x, int y, int acc);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMMUL (int x, int y);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMMULR(int x, int y);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMULBB(unsigned int x, unsigned int y);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMULBT(unsigned int x, unsigned int y);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMULTB(unsigned int x, unsigned int y);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMULTT(unsigned int x, unsigned int y);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMULWB(int x, unsigned int y);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SMULWT(int x, unsigned int y);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SXTAB (int x, unsigned int y);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind int __iar_builtin_SXTAH (int x, unsigned int y);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UXTAB (unsigned int x, unsigned int y);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UXTAH (unsigned int x, unsigned int y);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned long long __iar_builtin_UMAAL(unsigned int x,
                                       unsigned int y,
                                       unsigned int a,
                                       unsigned int b);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind long long __iar_builtin_SMLALBB(unsigned int x,
                                 unsigned int y,
                                 long long acc);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind long long __iar_builtin_SMLALBT(unsigned int x,
                                 unsigned int y,
                                 long long acc);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind long long __iar_builtin_SMLALTB(unsigned int x,
                                 unsigned int y,
                                 long long acc);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind long long __iar_builtin_SMLALTT(unsigned int x,
                                 unsigned int y,
                                 long long acc);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UXTB16(unsigned int x);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UXTAB16(unsigned int acc, unsigned int x);

_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SXTB16(unsigned int x);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SXTAB16(unsigned int acc, unsigned int x);

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SASX(unsigned int, unsigned int) ;
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SSAX(unsigned int, unsigned int) ;
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SHASX(unsigned int, unsigned int) ;
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_SHSAX(unsigned int, unsigned int) ;
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_QASX(unsigned int, unsigned int) ;
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_QSAX(unsigned int, unsigned int) ;

_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UASX(unsigned int, unsigned int) ;
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_USAX(unsigned int, unsigned int) ;
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UHASX(unsigned int, unsigned int) ;
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UHSAX(unsigned int, unsigned int) ;
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UQASX(unsigned int, unsigned int) ;
_Pragma("function_effects = hidden_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_UQSAX(unsigned int, unsigned int) ;

 
__intrinsic __nounwind void __iar_builtin_DMB(void);
__intrinsic __nounwind void __iar_builtin_DSB(void);
__intrinsic __nounwind void __iar_builtin_ISB(void);
__intrinsic __nounwind void __iar_builtin_DMBx(unsigned int __constrange(1, 15)) ;
__intrinsic __nounwind void __iar_builtin_DSBx(unsigned int __constrange(1, 15)) ;
__intrinsic __nounwind void __iar_builtin_ISBx(unsigned int __constrange(1, 15)) ;

 
__intrinsic __nounwind unsigned int __iar_builtin_TT(unsigned int);
__intrinsic __nounwind unsigned int __iar_builtin_TTT(unsigned int);
__intrinsic __nounwind unsigned int __iar_builtin_TTA(unsigned int);
__intrinsic __nounwind unsigned int __iar_builtin_TTAT(unsigned int);

__intrinsic __nounwind unsigned int __get_LR(void);
__intrinsic __nounwind void __set_LR(unsigned int);

__intrinsic __nounwind unsigned int __get_SP(void);
__intrinsic __nounwind void __set_SP(unsigned int);

 
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VSQRT_F32(float x);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VSQRT_F64(double x);

 
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VFMA_F32(float x, float y, float z);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VFMS_F32(float x, float y, float z);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VFNMA_F32(float x, float y, float z);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VFNMS_F32(float x, float y, float z);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VFMA_F64(double x, double y, double z);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VFMS_F64(double x, double y, double z);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VFNMA_F64(double x, double y, double z);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VFNMS_F64(double x, double y, double z);

 
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_CRC32B(unsigned int crc, unsigned char data);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_CRC32H(unsigned int crc, unsigned short data);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_CRC32W(unsigned int crc, unsigned int data);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_CRC32CB(unsigned int crc, unsigned char data);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_CRC32CH(unsigned int crc, unsigned short data);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind unsigned int __iar_builtin_CRC32CW(unsigned int crc, unsigned int data);

 
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VMAXNM_F32(float a, float b);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VMINNM_F32(float a, float b);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VMAXNM_F64(double a, double b);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VMINNM_F64(double a, double b);

 
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VRINTA_F32(float a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VRINTM_F32(float a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VRINTN_F32(float a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VRINTP_F32(float a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VRINTX_F32(float a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VRINTR_F32(float a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind float __iar_builtin_VRINTZ_F32(float a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VRINTA_F64(double a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VRINTM_F64(double a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VRINTN_F64(double a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VRINTP_F64(double a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VRINTX_F64(double a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VRINTR_F64(double a);
_Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __iar_builtin_VRINTZ_F64(double a);

#pragma language=restore









    

    



    
    


    







  _Pragma("inline=forced") __intrinsic int16_t __REVSH(int16_t val)
  {
    return (int16_t) __iar_builtin_REVSH(val);
  }












  _Pragma("inline=forced") __intrinsic uint8_t __LDRBT(volatile uint8_t *addr)
  {
    uint32_t res;
    __asm volatile ("LDRBT %0, [%1]" : "=r" (res) : "r" (addr) : "memory");
    return ((uint8_t)res);
  }

  _Pragma("inline=forced") __intrinsic uint16_t __LDRHT(volatile uint16_t *addr)
  {
    uint32_t res;
    __asm volatile ("LDRHT %0, [%1]" : "=r" (res) : "r" (addr) : "memory");
    return ((uint16_t)res);
  }

  _Pragma("inline=forced") __intrinsic uint32_t __LDRT(volatile uint32_t *addr)
  {
    uint32_t res;
    __asm volatile ("LDRT %0, [%1]" : "=r" (res) : "r" (addr) : "memory");
    return res;
  }

  _Pragma("inline=forced") __intrinsic void __STRBT(uint8_t value, volatile uint8_t *addr)
  {
    __asm volatile ("STRBT %1, [%0]" : : "r" (addr), "r" ((uint32_t)value) : "memory");
  }

  _Pragma("inline=forced") __intrinsic void __STRHT(uint16_t value, volatile uint16_t *addr)
  {
    __asm volatile ("STRHT %1, [%0]" : : "r" (addr), "r" ((uint32_t)value) : "memory");
  }

  _Pragma("inline=forced") __intrinsic void __STRT(uint32_t value, volatile uint32_t *addr)
  {
    __asm volatile ("STRT %1, [%0]" : : "r" (addr), "r" (value) : "memory");
  }




#pragma diag_default=Pe940
#pragma diag_default=Pe177






 










 

 






 

 

 













 



 






 



 
typedef union
{
  struct
  {
    uint32_t _reserved0:16;               
    uint32_t GE:4;                        
    uint32_t _reserved1:7;                
    uint32_t Q:1;                         
    uint32_t V:1;                         
    uint32_t C:1;                         
    uint32_t Z:1;                         
    uint32_t N:1;                         
  } b;                                    
  uint32_t w;                             
} APSR_Type;

 









 
typedef union
{
  struct
  {
    uint32_t ISR:9;                       
    uint32_t _reserved0:23;               
  } b;                                    
  uint32_t w;                             
} IPSR_Type;

 




 
typedef union
{
  struct
  {
    uint32_t ISR:9;                       
    uint32_t _reserved0:1;                
    uint32_t ICI_IT_1:6;                  
    uint32_t GE:4;                        
    uint32_t _reserved1:4;                
    uint32_t T:1;                         
    uint32_t ICI_IT_2:2;                  
    uint32_t Q:1;                         
    uint32_t V:1;                         
    uint32_t C:1;                         
    uint32_t Z:1;                         
    uint32_t N:1;                         
  } b;                                    
  uint32_t w;                             
} xPSR_Type;

 













 
typedef union
{
  struct
  {
    uint32_t nPRIV:1;                     
    uint32_t SPSEL:1;                     
    uint32_t FPCA:1;                      
    uint32_t _reserved0:29;               
  } b;                                    
  uint32_t w;                             
} CONTROL_Type;

 



 







 



 
typedef struct
{
  volatile uint32_t ISER[8U];                
        uint32_t RESERVED0[24U];
  volatile uint32_t ICER[8U];                
        uint32_t RESERVED1[24U];
  volatile uint32_t ISPR[8U];                
        uint32_t RESERVED2[24U];
  volatile uint32_t ICPR[8U];                
        uint32_t RESERVED3[24U];
  volatile uint32_t IABR[8U];                
        uint32_t RESERVED4[56U];
  volatile uint8_t  IP[240U];                
        uint32_t RESERVED5[644U];
  volatile  uint32_t STIR;                    
}  NVIC_Type;

 

 







 



 
typedef struct
{
  volatile const  uint32_t CPUID;                   
  volatile uint32_t ICSR;                    
  volatile uint32_t VTOR;                    
  volatile uint32_t AIRCR;                   
  volatile uint32_t SCR;                     
  volatile uint32_t CCR;                     
  volatile uint8_t  SHPR[12U];               
  volatile uint32_t SHCSR;                   
  volatile uint32_t CFSR;                    
  volatile uint32_t HFSR;                    
  volatile uint32_t DFSR;                    
  volatile uint32_t MMFAR;                   
  volatile uint32_t BFAR;                    
  volatile uint32_t AFSR;                    
  volatile const  uint32_t ID_PFR[2U];              
  volatile const  uint32_t ID_DFR;                  
  volatile const  uint32_t ID_AFR;                  
  volatile const  uint32_t ID_MFR[4U];              
  volatile const  uint32_t ID_ISAR[5U];             
        uint32_t RESERVED0[1U];
  volatile const  uint32_t CLIDR;                   
  volatile const  uint32_t CTR;                     
  volatile const  uint32_t CCSIDR;                  
  volatile uint32_t CSSELR;                  
  volatile uint32_t CPACR;                   
        uint32_t RESERVED3[93U];
  volatile  uint32_t STIR;                    
        uint32_t RESERVED4[15U];
  volatile const  uint32_t MVFR0;                   
  volatile const  uint32_t MVFR1;                   
  volatile const  uint32_t MVFR2;                   
        uint32_t RESERVED5[1U];
  volatile  uint32_t ICIALLU;                 
        uint32_t RESERVED6[1U];
  volatile  uint32_t ICIMVAU;                 
  volatile  uint32_t DCIMVAC;                 
  volatile  uint32_t DCISW;                   
  volatile  uint32_t DCCMVAU;                 
  volatile  uint32_t DCCMVAC;                 
  volatile  uint32_t DCCSW;                   
  volatile  uint32_t DCCIMVAC;                
  volatile  uint32_t DCCISW;                  
        uint32_t RESERVED7[6U];
  volatile uint32_t ITCMCR;                  
  volatile uint32_t DTCMCR;                  
  volatile uint32_t AHBPCR;                  
  volatile uint32_t CACR;                    
  volatile uint32_t AHBSCR;                  
        uint32_t RESERVED8[1U];
  volatile uint32_t ABFSR;                   
} SCB_Type;

 





 










 

 







 



 









 














 



 






 







 






 



 





 


 





 







 


 

 


 


 


 




 




 


 



 



 






 







 



 
typedef struct
{
        uint32_t RESERVED0[1U];
  volatile const  uint32_t ICTR;                    
  volatile uint32_t ACTLR;                   
} SCnSCB_Type;

 

 











 







 



 
typedef struct
{
  volatile uint32_t CTRL;                    
  volatile uint32_t LOAD;                    
  volatile uint32_t VAL;                     
  volatile const  uint32_t CALIB;                   
} SysTick_Type;

 




 

 

 



 







 



 
typedef struct
{
  volatile  union
  {
    volatile  uint8_t    u8;                  
    volatile  uint16_t   u16;                 
    volatile  uint32_t   u32;                 
  }  PORT [32U];                          
        uint32_t RESERVED0[864U];
  volatile uint32_t TER;                     
        uint32_t RESERVED1[15U];
  volatile uint32_t TPR;                     
        uint32_t RESERVED2[15U];
  volatile uint32_t TCR;                     
        uint32_t RESERVED3[32U];
        uint32_t RESERVED4[43U];
  volatile  uint32_t LAR;                     
  volatile const  uint32_t LSR;                     
        uint32_t RESERVED5[6U];
  volatile const  uint32_t PID4;                    
  volatile const  uint32_t PID5;                    
  volatile const  uint32_t PID6;                    
  volatile const  uint32_t PID7;                    
  volatile const  uint32_t PID0;                    
  volatile const  uint32_t PID1;                    
  volatile const  uint32_t PID2;                    
  volatile const  uint32_t PID3;                    
  volatile const  uint32_t CID0;                    
  volatile const  uint32_t CID1;                    
  volatile const  uint32_t CID2;                    
  volatile const  uint32_t CID3;                    
} ITM_Type;

 

 









 



   







 



 
typedef struct
{
  volatile uint32_t CTRL;                    
  volatile uint32_t CYCCNT;                  
  volatile uint32_t CPICNT;                  
  volatile uint32_t EXCCNT;                  
  volatile uint32_t SLEEPCNT;                
  volatile uint32_t LSUCNT;                  
  volatile uint32_t FOLDCNT;                 
  volatile const  uint32_t PCSR;                    
  volatile uint32_t COMP0;                   
  volatile uint32_t MASK0;                   
  volatile uint32_t FUNCTION0;               
        uint32_t RESERVED0[1U];
  volatile uint32_t COMP1;                   
  volatile uint32_t MASK1;                   
  volatile uint32_t FUNCTION1;               
        uint32_t RESERVED1[1U];
  volatile uint32_t COMP2;                   
  volatile uint32_t MASK2;                   
  volatile uint32_t FUNCTION2;               
        uint32_t RESERVED2[1U];
  volatile uint32_t COMP3;                   
  volatile uint32_t MASK3;                   
  volatile uint32_t FUNCTION3;               
        uint32_t RESERVED3[981U];
  volatile  uint32_t LAR;                     
  volatile const  uint32_t LSR;                     
} DWT_Type;

 


















 

 

 

 

 

 

 









   







 



 
typedef struct
{
  volatile const  uint32_t SSPSR;                   
  volatile uint32_t CSPSR;                   
        uint32_t RESERVED0[2U];
  volatile uint32_t ACPR;                    
        uint32_t RESERVED1[55U];
  volatile uint32_t SPPR;                    
        uint32_t RESERVED2[131U];
  volatile const  uint32_t FFSR;                    
  volatile uint32_t FFCR;                    
  volatile const  uint32_t FSCR;                    
        uint32_t RESERVED3[759U];
  volatile const  uint32_t TRIGGER;                 
  volatile const  uint32_t FIFO0;                   
  volatile const  uint32_t ITATBCTR2;               
        uint32_t RESERVED4[1U];
  volatile const  uint32_t ITATBCTR0;               
  volatile const  uint32_t FIFO1;                   
  volatile uint32_t ITCTRL;                  
        uint32_t RESERVED5[39U];
  volatile uint32_t CLAIMSET;                
  volatile uint32_t CLAIMCLR;                
        uint32_t RESERVED7[8U];
  volatile const  uint32_t DEVID;                   
  volatile const  uint32_t DEVTYPE;                 
} TPI_Type;

 

 

 




 


 

 







 


 







 


 

 






 


   







 



 
typedef struct
{
  volatile const  uint32_t TYPE;                    
  volatile uint32_t CTRL;                    
  volatile uint32_t RNR;                     
  volatile uint32_t RBAR;                    
  volatile uint32_t RASR;                    
  volatile uint32_t RBAR_A1;                 
  volatile uint32_t RASR_A1;                 
  volatile uint32_t RBAR_A2;                 
  volatile uint32_t RASR_A2;                 
  volatile uint32_t RBAR_A3;                 
  volatile uint32_t RASR_A3;                 
} MPU_Type;


 



 



 

 



 










 







 



 
typedef struct
{
        uint32_t RESERVED0[1U];
  volatile uint32_t FPCCR;                   
  volatile uint32_t FPCAR;                   
  volatile uint32_t FPDSCR;                  
  volatile const  uint32_t MVFR0;                   
  volatile const  uint32_t MVFR1;                   
  volatile const  uint32_t MVFR2;                   
} FPU_Type;

 









 

 




 








 




 


 







 



 
typedef struct
{
  volatile uint32_t DHCSR;                   
  volatile  uint32_t DCRSR;                   
  volatile uint32_t DCRDR;                   
  volatile uint32_t DEMCR;                   
} CoreDebug_Type;

 












 


 













 







 






 






 

 







 

 




 










 


 



 





 





 










 
static inline void __NVIC_SetPriorityGrouping(uint32_t PriorityGroup)
{
  uint32_t reg_value;
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);              

  reg_value  =  ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->AIRCR;                                                    
  reg_value &= ~((uint32_t)((0xFFFFUL << 16U) | (7UL << 8U)));  
  reg_value  =  (reg_value                                   |
                ((uint32_t)0x5FAUL << 16U) |
                (PriorityGroupTmp << 8U)  );               
  ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->AIRCR =  reg_value;
}






 
static inline uint32_t __NVIC_GetPriorityGrouping(void)
{
  return ((uint32_t)((((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->AIRCR & (7UL << 8U)) >> 8U));
}







 
static inline void __NVIC_EnableIRQ(IRQn_Type IRQn)
{
  if ((int32_t)(IRQn) >= 0)
  {
    __asm volatile("":::"memory");
    ((NVIC_Type *) ((0xE000E000UL) + 0x0100UL) )->ISER[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
    __asm volatile("":::"memory");
  }
}









 
static inline uint32_t __NVIC_GetEnableIRQ(IRQn_Type IRQn)
{
  if ((int32_t)(IRQn) >= 0)
  {
    return((uint32_t)(((((NVIC_Type *) ((0xE000E000UL) + 0x0100UL) )->ISER[(((uint32_t)IRQn) >> 5UL)] & (1UL << (((uint32_t)IRQn) & 0x1FUL))) != 0UL) ? 1UL : 0UL));
  }
  else
  {
    return(0U);
  }
}







 
static inline void __NVIC_DisableIRQ(IRQn_Type IRQn)
{
  if ((int32_t)(IRQn) >= 0)
  {
    ((NVIC_Type *) ((0xE000E000UL) + 0x0100UL) )->ICER[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
    __iar_builtin_DSB();
    __iar_builtin_ISB();
  }
}









 
static inline uint32_t __NVIC_GetPendingIRQ(IRQn_Type IRQn)
{
  if ((int32_t)(IRQn) >= 0)
  {
    return((uint32_t)(((((NVIC_Type *) ((0xE000E000UL) + 0x0100UL) )->ISPR[(((uint32_t)IRQn) >> 5UL)] & (1UL << (((uint32_t)IRQn) & 0x1FUL))) != 0UL) ? 1UL : 0UL));
  }
  else
  {
    return(0U);
  }
}







 
static inline void __NVIC_SetPendingIRQ(IRQn_Type IRQn)
{
  if ((int32_t)(IRQn) >= 0)
  {
    ((NVIC_Type *) ((0xE000E000UL) + 0x0100UL) )->ISPR[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
  }
}







 
static inline void __NVIC_ClearPendingIRQ(IRQn_Type IRQn)
{
  if ((int32_t)(IRQn) >= 0)
  {
    ((NVIC_Type *) ((0xE000E000UL) + 0x0100UL) )->ICPR[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
  }
}









 
static inline uint32_t __NVIC_GetActive(IRQn_Type IRQn)
{
  if ((int32_t)(IRQn) >= 0)
  {
    return((uint32_t)(((((NVIC_Type *) ((0xE000E000UL) + 0x0100UL) )->IABR[(((uint32_t)IRQn) >> 5UL)] & (1UL << (((uint32_t)IRQn) & 0x1FUL))) != 0UL) ? 1UL : 0UL));
  }
  else
  {
    return(0U);
  }
}










 
static inline void __NVIC_SetPriority(IRQn_Type IRQn, uint32_t priority)
{
  if ((int32_t)(IRQn) >= 0)
  {
    ((NVIC_Type *) ((0xE000E000UL) + 0x0100UL) )->IP[((uint32_t)IRQn)]                = (uint8_t)((priority << (8U - 4)) & (uint32_t)0xFFUL);
  }
  else
  {
    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->SHPR[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - 4)) & (uint32_t)0xFFUL);
  }
}










 
static inline uint32_t __NVIC_GetPriority(IRQn_Type IRQn)
{

  if ((int32_t)(IRQn) >= 0)
  {
    return(((uint32_t)((NVIC_Type *) ((0xE000E000UL) + 0x0100UL) )->IP[((uint32_t)IRQn)]                >> (8U - 4)));
  }
  else
  {
    return(((uint32_t)((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->SHPR[(((uint32_t)IRQn) & 0xFUL)-4UL] >> (8U - 4)));
  }
}












 
static inline uint32_t NVIC_EncodePriority (uint32_t PriorityGroup, uint32_t PreemptPriority, uint32_t SubPriority)
{
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);    
  uint32_t PreemptPriorityBits;
  uint32_t SubPriorityBits;

  PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(4)) ? (uint32_t)(4) : (uint32_t)(7UL - PriorityGroupTmp);
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(4)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(4));

  return (
           ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits) |
           ((SubPriority     & (uint32_t)((1UL << (SubPriorityBits    )) - 1UL)))
         );
}












 
static inline void NVIC_DecodePriority (uint32_t Priority, uint32_t PriorityGroup, uint32_t* const pPreemptPriority, uint32_t* const pSubPriority)
{
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);    
  uint32_t PreemptPriorityBits;
  uint32_t SubPriorityBits;

  PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(4)) ? (uint32_t)(4) : (uint32_t)(7UL - PriorityGroupTmp);
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(4)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(4));

  *pPreemptPriority = (Priority >> SubPriorityBits) & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL);
  *pSubPriority     = (Priority                   ) & (uint32_t)((1UL << (SubPriorityBits    )) - 1UL);
}










 
static inline void __NVIC_SetVector(IRQn_Type IRQn, uint32_t vector)
{
  uint32_t *vectors = (uint32_t *)((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->VTOR;
  vectors[(int32_t)IRQn + 16] = vector;
  __iar_builtin_DSB();
}









 
static inline uint32_t __NVIC_GetVector(IRQn_Type IRQn)
{
  uint32_t *vectors = (uint32_t *)((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->VTOR;
  return vectors[(int32_t)IRQn + 16];
}





 
__attribute__((__noreturn__)) static inline void __NVIC_SystemReset(void)
{
  __iar_builtin_DSB();                                                          
 
  ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->AIRCR  = (uint32_t)((0x5FAUL << 16U)    |
                           (((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->AIRCR & (7UL << 8U)) |
                            (1UL << 2U)    );          
  __iar_builtin_DSB();                                                           

  for(;;)                                                            
  {
    __iar_builtin_no_operation();
  }
}

 


 







 
















 
 
  #pragma system_include          
 







 








   









 












                          







  









  











  



 



 



 



 




 
typedef struct {
  uint32_t RBAR; 
  uint32_t RASR; 
} ARM_MPU_Region_t;
    


 
static inline void ARM_MPU_Enable(uint32_t MPU_Control)
{
  __iar_builtin_DMB();
  ((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->CTRL = MPU_Control | (1UL );
  ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->SHCSR |= (1UL << 16U);
  __iar_builtin_DSB();
  __iar_builtin_ISB();
}


 
static inline void ARM_MPU_Disable(void)
{
  __iar_builtin_DMB();
  ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->SHCSR &= ~(1UL << 16U);
  ((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->CTRL  &= ~(1UL );
  __iar_builtin_DSB();
  __iar_builtin_ISB();
}



 
static inline void ARM_MPU_ClrRegion(uint32_t rnr)
{
  ((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->RNR = rnr;
  ((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->RASR = 0U;
}




    
static inline void ARM_MPU_SetRegion(uint32_t rbar, uint32_t rasr)
{
  ((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->RBAR = rbar;
  ((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->RASR = rasr;
}





    
static inline void ARM_MPU_SetRegionEx(uint32_t rnr, uint32_t rbar, uint32_t rasr)
{
  ((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->RNR = rnr;
  ((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->RBAR = rbar;
  ((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->RASR = rasr;
}





 
static inline void ARM_MPU_OrderedMemcpy(volatile uint32_t* dst, const uint32_t* __restrict src, uint32_t len)
{
  uint32_t i;
  for (i = 0U; i < len; ++i) 
  {
    dst[i] = src[i];
  }
}




 
static inline void ARM_MPU_Load(ARM_MPU_Region_t const* table, uint32_t cnt) 
{
  const uint32_t rowWordSize = sizeof(ARM_MPU_Region_t)/4U;
  while (cnt > 4U) {
    ARM_MPU_OrderedMemcpy(&(((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->RBAR), &(table->RBAR), 4U*rowWordSize);
    table += 4U;
    cnt -= 4U;
  }
  ARM_MPU_OrderedMemcpy(&(((MPU_Type *) ((0xE000E000UL) + 0x0D90UL) )->RBAR), &(table->RBAR), cnt*rowWordSize);
}




 





 








 
static inline uint32_t SCB_GetFPUType(void)
{
  uint32_t mvfr0;

  mvfr0 = ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->MVFR0;
  if      ((mvfr0 & ((0xFUL << 4U) | (0xFUL << 8U))) == 0x220U)
  {
    return 2U;            
  }
  else if ((mvfr0 & ((0xFUL << 4U) | (0xFUL << 8U))) == 0x020U)
  {
    return 1U;            
  }
  else
  {
    return 0U;            
  }
}

 


 






 
















 

  #pragma system_include          







 

 






 
_Pragma("inline=forced") static inline void SCB_EnableICache (void)
{
    if (((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCR & (1UL << 17U)) return;   

    __iar_builtin_DSB();
    __iar_builtin_ISB();
    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->ICIALLU = 0UL;                      
    __iar_builtin_DSB();
    __iar_builtin_ISB();
    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCR |=  (uint32_t)(1UL << 17U);   
    __iar_builtin_DSB();
    __iar_builtin_ISB();
}





 
_Pragma("inline=forced") static inline void SCB_DisableICache (void)
{
    __iar_builtin_DSB();
    __iar_builtin_ISB();
    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCR &= ~(uint32_t)(1UL << 17U);   
    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->ICIALLU = 0UL;                      
    __iar_builtin_DSB();
    __iar_builtin_ISB();
}





 
_Pragma("inline=forced") static inline void SCB_InvalidateICache (void)
{
    __iar_builtin_DSB();
    __iar_builtin_ISB();
    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->ICIALLU = 0UL;
    __iar_builtin_DSB();
    __iar_builtin_ISB();
}









 
_Pragma("inline=forced") static inline void SCB_InvalidateICache_by_Addr (void *addr, int32_t isize)
{
    if ( isize > 0 ) {
       int32_t op_size = isize + (((uint32_t)addr) & (32U - 1U));
      uint32_t op_addr = (uint32_t)addr  ;

      __iar_builtin_DSB();

      do {
        ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->ICIMVAU = op_addr;              
        op_addr += 32U;
        op_size -= 32U;
      } while ( op_size > 0 );

      __iar_builtin_DSB();
      __iar_builtin_ISB();
    }
}





 
_Pragma("inline=forced") static inline void SCB_EnableDCache (void)
{
    uint32_t ccsidr;
    uint32_t sets;
    uint32_t ways;

    if (((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCR & (1UL << 16U)) return;   

    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CSSELR = 0U;                        
    __iar_builtin_DSB();

    ccsidr = ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCSIDR;

                                             
    sets = (uint32_t)((((ccsidr) & (0x7FFFUL << 13U) ) >> 13U ));
    do {
      ways = (uint32_t)((((ccsidr) & (0x3FFUL << 3U)) >> 3U));
      do {
        ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->DCISW = (((sets << 5U) & (0x1FFUL << 5U)) |
                      ((ways << 30U) & (3UL << 30U))  );
      } while (ways-- != 0U);
    } while(sets-- != 0U);
    __iar_builtin_DSB();

    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCR |=  (uint32_t)(1UL << 16U);   

    __iar_builtin_DSB();
    __iar_builtin_ISB();
}





 
_Pragma("inline=forced") static inline void SCB_DisableDCache (void)
{
    uint32_t ccsidr;
    uint32_t sets;
    uint32_t ways;

    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CSSELR = 0U;                        
    __iar_builtin_DSB();

    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCR &= ~(uint32_t)(1UL << 16U);   
    __iar_builtin_DSB();

    ccsidr = ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCSIDR;

                                             
    sets = (uint32_t)((((ccsidr) & (0x7FFFUL << 13U) ) >> 13U ));
    do {
      ways = (uint32_t)((((ccsidr) & (0x3FFUL << 3U)) >> 3U));
      do {
        ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->DCCISW = (((sets << 5U) & (0x1FFUL << 5U)) |
                       ((ways << 30U) & (3UL << 30U))  );
      } while (ways-- != 0U);
    } while(sets-- != 0U);

    __iar_builtin_DSB();
    __iar_builtin_ISB();
}





 
_Pragma("inline=forced") static inline void SCB_InvalidateDCache (void)
{
    uint32_t ccsidr;
    uint32_t sets;
    uint32_t ways;

    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CSSELR = 0U;                        
    __iar_builtin_DSB();

    ccsidr = ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCSIDR;

                                             
    sets = (uint32_t)((((ccsidr) & (0x7FFFUL << 13U) ) >> 13U ));
    do {
      ways = (uint32_t)((((ccsidr) & (0x3FFUL << 3U)) >> 3U));
      do {
        ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->DCISW = (((sets << 5U) & (0x1FFUL << 5U)) |
                      ((ways << 30U) & (3UL << 30U))  );
      } while (ways-- != 0U);
    } while(sets-- != 0U);

    __iar_builtin_DSB();
    __iar_builtin_ISB();
}





 
_Pragma("inline=forced") static inline void SCB_CleanDCache (void)
{
    uint32_t ccsidr;
    uint32_t sets;
    uint32_t ways;

    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CSSELR = 0U;                        
    __iar_builtin_DSB();

    ccsidr = ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCSIDR;

                                             
    sets = (uint32_t)((((ccsidr) & (0x7FFFUL << 13U) ) >> 13U ));
    do {
      ways = (uint32_t)((((ccsidr) & (0x3FFUL << 3U)) >> 3U));
      do {
        ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->DCCSW = (((sets << 5U) & (0x1FFUL << 5U)) |
                      ((ways << 30U) & (3UL << 30U))  );
      } while (ways-- != 0U);
    } while(sets-- != 0U);

    __iar_builtin_DSB();
    __iar_builtin_ISB();
}





 
_Pragma("inline=forced") static inline void SCB_CleanInvalidateDCache (void)
{
    uint32_t ccsidr;
    uint32_t sets;
    uint32_t ways;

    ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CSSELR = 0U;                        
    __iar_builtin_DSB();

    ccsidr = ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->CCSIDR;

                                             
    sets = (uint32_t)((((ccsidr) & (0x7FFFUL << 13U) ) >> 13U ));
    do {
      ways = (uint32_t)((((ccsidr) & (0x3FFUL << 3U)) >> 3U));
      do {
        ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->DCCISW = (((sets << 5U) & (0x1FFUL << 5U)) |
                       ((ways << 30U) & (3UL << 30U))  );
      } while (ways-- != 0U);
    } while(sets-- != 0U);

    __iar_builtin_DSB();
    __iar_builtin_ISB();
}









 
_Pragma("inline=forced") static inline void SCB_InvalidateDCache_by_Addr (void *addr, int32_t dsize)
{
    if ( dsize > 0 ) { 
       int32_t op_size = dsize + (((uint32_t)addr) & (32U - 1U));
      uint32_t op_addr = (uint32_t)addr  ;
    
      __iar_builtin_DSB();

      do {
        ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->DCIMVAC = op_addr;              
        op_addr += 32U;
        op_size -= 32U;
      } while ( op_size > 0 );

      __iar_builtin_DSB();
      __iar_builtin_ISB();
    }
}









 
_Pragma("inline=forced") static inline void SCB_CleanDCache_by_Addr (uint32_t *addr, int32_t dsize)
{
    if ( dsize > 0 ) { 
       int32_t op_size = dsize + (((uint32_t)addr) & (32U - 1U));
      uint32_t op_addr = (uint32_t)addr  ;
    
      __iar_builtin_DSB();

      do {
        ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->DCCMVAC = op_addr;              
        op_addr += 32U;
        op_size -= 32U;
      } while ( op_size > 0 );

      __iar_builtin_DSB();
      __iar_builtin_ISB();
    }
}









 
_Pragma("inline=forced") static inline void SCB_CleanInvalidateDCache_by_Addr (uint32_t *addr, int32_t dsize)
{
    if ( dsize > 0 ) { 
       int32_t op_size = dsize + (((uint32_t)addr) & (32U - 1U));
      uint32_t op_addr = (uint32_t)addr  ;
    
      __iar_builtin_DSB();

      do {
        ((SCB_Type *) ((0xE000E000UL) + 0x0D00UL) )->DCCIMVAC = op_addr;             
        op_addr +=          32U;
        op_size -=          32U;
      } while ( op_size > 0 );

      __iar_builtin_DSB();
      __iar_builtin_ISB();
    }
}

 



 





 












 
static inline uint32_t SysTick_Config(uint32_t ticks)
{
  if ((ticks - 1UL) > (0xFFFFFFUL ))
  {
    return (1UL);                                                    
  }

  ((SysTick_Type *) ((0xE000E000UL) + 0x0010UL) )->LOAD  = (uint32_t)(ticks - 1UL);                          
  __NVIC_SetPriority (SysTick_IRQn, (1UL << 4) - 1UL);  
  ((SysTick_Type *) ((0xE000E000UL) + 0x0010UL) )->VAL   = 0UL;                                              
  ((SysTick_Type *) ((0xE000E000UL) + 0x0010UL) )->CTRL  = (1UL << 2U) |
                   (1UL << 1U)   |
                   (1UL );                          
  return (0UL);                                                      
}


 



 





 

extern volatile int32_t ITM_RxBuffer;                               









 
static inline uint32_t ITM_SendChar (uint32_t ch)
{
  if (((((ITM_Type *) (0xE0000000UL) )->TCR & (1UL )) != 0UL) &&       
      ((((ITM_Type *) (0xE0000000UL) )->TER & 1UL               ) != 0UL)   )      
  {
    while (((ITM_Type *) (0xE0000000UL) )->PORT[0U].u32 == 0UL)
    {
      __iar_builtin_no_operation();
    }
    ((ITM_Type *) (0xE0000000UL) )->PORT[0U].u8 = (uint8_t)ch;
  }
  return (ch);
}







 
static inline int32_t ITM_ReceiveChar (void)
{
  int32_t ch = -1;                            

  if (ITM_RxBuffer != ((int32_t)0x5AA55AA5U))
  {
    ch = ITM_RxBuffer;
    ITM_RxBuffer = ((int32_t)0x5AA55AA5U);        
  }

  return (ch);
}







 
static inline int32_t ITM_CheckChar (void)
{

  if (ITM_RxBuffer == ((int32_t)0x5AA55AA5U))
  {
    return (0);                               
  }
  else
  {
    return (1);                               
  }
}

 










 


















 

 



 

 
extern uint32_t SystemCoreClock;

 
 
extern void SystemInit (void);
 
extern void SystemCoreClockUpdate (void);
 
extern uint32_t gd32h7xx_firmware_version_get(void);




typedef enum {
    GD_OK       = 0x00U,
    GD_ERROR    = 0x01U,
    GD_BUSY     = 0x02U,
    GD_TIMEOUT  = 0x03U
} gd_status_enum;

typedef enum {
    OP_STATE_RESET             = 0x00U,
    OP_STATE_READY             = 0x01U,
    OP_STATE_BUSY              = 0x02U,
    OP_STATE_TIMEOUT           = 0x03U,
    OP_STATE_ERROR             = 0x04U,
    OP_STATE_ABORT             = 0x05U,
    OP_STATE_LISTEN            = 0x06U,

    OP_STATE_BUSY_TX           = 0x21U,  
    OP_STATE_BUSY_RX           = 0x22U,  

    OP_STATE_BUSY_TX_LISTEN    = 0x61U,  
    OP_STATE_BUSY_RX_LISTEN    = 0x62U,  

    OP_STATE_BUTT
} operation_state_enum;

 
typedef enum {DISABLE = 0, ENABLE = !DISABLE} EventStatus, ControlStatus;
typedef enum {RESET = 0, SET = !RESET} FlagStatus;
typedef enum {ERROR = 0, SUCCESS = !ERROR} ErrStatus;

 

 
 
 
 
 
 
 
 
 
 
 
 

 





 


























 







 


























 







 


















 

 


 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
 
void adc_deinit(uint32_t adc_periph);
 
void adc_clock_config(uint32_t adc_periph, uint32_t prescaler);
 
void adc_special_function_config(uint32_t adc_periph, uint32_t function, ControlStatus newvalue);
 
void adc_data_alignment_config(uint32_t adc_periph, uint32_t data_alignment);
 
void adc_enable(uint32_t adc_periph);
 
void adc_disable(uint32_t adc_periph);
 
void adc_calibration_mode_config(uint32_t adc_periph, uint32_t clb_mode);
 
void adc_calibration_number(uint32_t adc_periph, uint32_t clb_num);
 
void adc_calibration_enable(uint32_t adc_periph);
 
void adc_resolution_config(uint32_t adc_periph, uint32_t resolution);
 
void adc_internal_channel_config(uint32_t internal_channel, ControlStatus newvalue);

 
 
void adc_dma_mode_enable(uint32_t adc_periph);
 
void adc_dma_mode_disable(uint32_t adc_periph);
 
void adc_dma_request_after_last_enable(uint32_t adc_periph);
 
void adc_dma_request_after_last_disable(uint32_t adc_periph);
 
void adc_hpdf_mode_enable(uint32_t adc_periph);
 
void adc_hpdf_mode_disable(uint32_t adc_periph);

 
 
void adc_discontinuous_mode_config(uint32_t adc_periph, uint8_t adc_channel_group, uint32_t length);
 
void adc_channel_length_config(uint32_t adc_periph, uint8_t adc_channel_group, uint32_t length);
 
void adc_regular_channel_config(uint32_t adc_periph, uint8_t rank, uint8_t adc_channel, uint32_t sample_time);
 
void adc_inserted_channel_config(uint32_t adc_periph, uint8_t rank, uint8_t adc_channel, uint32_t sample_time);
 
void adc_inserted_channel_offset_config(uint32_t adc_periph, uint8_t inserted_channel, uint32_t offset);
 
void adc_channel_differential_mode_config(uint32_t adc_periph, uint32_t adc_channel, ControlStatus newvalue);
 
void adc_external_trigger_config(uint32_t adc_periph, uint8_t adc_channel_group, uint32_t trigger_mode);
 
void adc_software_trigger_enable(uint32_t adc_periph, uint8_t adc_channel_group);
 
void adc_end_of_conversion_config(uint32_t adc_periph, uint32_t end_selection);

 
 
uint32_t adc_regular_data_read(uint32_t adc_periph);
 
uint32_t adc_inserted_data_read(uint32_t adc_periph, uint8_t inserted_channel);

 
 
void adc_watchdog0_single_channel_enable(uint32_t adc_periph, uint8_t adc_channel);
 
void adc_watchdog0_group_channel_enable(uint32_t adc_periph, uint8_t adc_channel_group);
 
void adc_watchdog0_disable(uint32_t adc_periph);
 
void adc_watchdog1_channel_config(uint32_t adc_periph, uint32_t selection_channel, ControlStatus newvalue);
 
void adc_watchdog2_channel_config(uint32_t adc_periph, uint32_t selection_channel, ControlStatus newvalue);
 
void adc_watchdog1_disable(uint32_t adc_periph);
 
void adc_watchdog2_disable(uint32_t adc_periph);
 
void adc_watchdog0_threshold_config(uint32_t adc_periph, uint32_t low_threshold, uint32_t high_threshold);
 
void adc_watchdog1_threshold_config(uint32_t adc_periph, uint32_t low_threshold, uint32_t high_threshold);
 
void adc_watchdog2_threshold_config(uint32_t adc_periph, uint32_t low_threshold, uint32_t high_threshold);

 
 
void adc_oversample_mode_config(uint32_t adc_periph, uint32_t mode, uint16_t shift, uint16_t ratio);
 
void adc_oversample_mode_enable(uint32_t adc_periph);
 
void adc_oversample_mode_disable(uint32_t adc_periph);

 
 
FlagStatus adc_flag_get(uint32_t adc_periph, uint32_t flag);
 
void adc_flag_clear(uint32_t adc_periph, uint32_t flag);
 
void adc_interrupt_enable(uint32_t adc_periph, uint32_t interrupt);
 
void adc_interrupt_disable(uint32_t adc_periph, uint32_t interrupt);
 
FlagStatus adc_interrupt_flag_get(uint32_t adc_periph, uint32_t int_flag);
 
void adc_interrupt_flag_clear(uint32_t adc_periph, uint32_t int_flag);

 
 
void adc_sync_mode_config(uint32_t sync_mode);
 
void adc_sync_delay_config(uint32_t sample_delay);
 
void adc_sync_dma_config(uint32_t dma_mode);
 
void adc_sync_dma_request_after_last_enable(void);
 
void adc_sync_dma_request_after_last_disable(void);
 
uint32_t adc_sync_master_adc_regular_data0_read(void);
 
uint32_t adc_sync_slave_adc_regular_data0_read(void);
 
uint32_t adc_sync_regular_data1_read(void);






 


























 



 

 








 





    













 
typedef enum 
{
    MASTER_PORT0 = 0U,                                    
    MASTER_PORT1,                                         
    MASTER_PORT2,                                         
    MASTER_PORT3,                                         
    MASTER_PORT4,                                         
    MASTER_PORT5,                                         
    MASTER_PORT6,                                         
    MASTER_PORT7                                          
} master_port_enum;

 
typedef enum 
{
    SLAVE_PORT0 = 0U,                                     
    SLAVE_PORT1,                                          
    SLAVE_PORT2,                                          
    SLAVE_PORT3,                                          
    SLAVE_PORT4,                                          
    SLAVE_PORT5                                           
} slave_port_enum;

 
 

 
 

 
 

 
 

 
 

 
 

 
 

 
 

 
 

 

 

 
 

 
 






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 
typedef enum {
     
    CAN_INT_RX_WARNING            = (((uint32_t)(((uint32_t)0x00000004U)) << 6U) | (uint32_t)(10U)),                 
    CAN_INT_TX_WARNING            = (((uint32_t)(((uint32_t)0x00000004U)) << 6U) | (uint32_t)(11U)),                 
    CAN_INT_ERR_SUMMARY           = (((uint32_t)(((uint32_t)0x00000004U)) << 6U) | (uint32_t)(14U)),                 
    CAN_INT_BUSOFF                = (((uint32_t)(((uint32_t)0x00000004U)) << 6U) | (uint32_t)(15U)),                 
     
    CAN_INT_BUSOFF_RECOVERY       = (((uint32_t)(((uint32_t)0x00000034U)) << 6U) | (uint32_t)(30U)),                 
    CAN_INT_ERR_SUMMARY_FD        = (((uint32_t)(((uint32_t)0x00000034U)) << 6U) | (uint32_t)(31U)),                 
     
    CAN_INT_MB0                   = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(0U)),                 
    CAN_INT_MB1                   = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(1U)),                 
    CAN_INT_MB2                   = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(2U)),                 
    CAN_INT_MB3                   = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(3U)),                 
    CAN_INT_MB4                   = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(4U)),                 
    CAN_INT_MB5                   = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(5U)),                 
    CAN_INT_MB6                   = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(6U)),                 
    CAN_INT_MB7                   = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(7U)),                 
    CAN_INT_MB8                   = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(8U)),                 
    CAN_INT_MB9                   = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(9U)),                 
    CAN_INT_MB10                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(10U)),                
    CAN_INT_MB11                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(11U)),                
    CAN_INT_MB12                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(12U)),                
    CAN_INT_MB13                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(13U)),                
    CAN_INT_MB14                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(14U)),                
    CAN_INT_MB15                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(15U)),                
    CAN_INT_MB16                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(16U)),                
    CAN_INT_MB17                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(17U)),                
    CAN_INT_MB18                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(18U)),                
    CAN_INT_MB19                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(19U)),                
    CAN_INT_MB20                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(20U)),                
    CAN_INT_MB21                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(21U)),                
    CAN_INT_MB22                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(22U)),                
    CAN_INT_MB23                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(23U)),                
    CAN_INT_MB24                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(24U)),                
    CAN_INT_MB25                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(25U)),                
    CAN_INT_MB26                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(26U)),                
    CAN_INT_MB27                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(27U)),                
    CAN_INT_MB28                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(28U)),                
    CAN_INT_MB29                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(29U)),                
    CAN_INT_MB30                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(30U)),                
    CAN_INT_MB31                  = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(31U)),                
    CAN_INT_FIFO_AVAILABLE        = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(5U)),                 
    CAN_INT_FIFO_WARNING          = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(6U)),                 
    CAN_INT_FIFO_OVERFLOW         = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(7U)),                 
     
    CAN_INT_WAKEUP_MATCH          = (((uint32_t)(((uint32_t)0x00000B00U)) << 6U) | (uint32_t)(16U)),              
    CAN_INT_WAKEUP_TIMEOUT        = (((uint32_t)(((uint32_t)0x00000B00U)) << 6U) | (uint32_t)(17U))               
} can_interrupt_enum;

 
typedef enum {
     
    CAN_FLAG_CAN_PN            = (((uint32_t)(((uint32_t)0x00000000U)) << 6U) | (uint32_t)(18U)),                    
    CAN_FLAG_SOFT_RST             = (((uint32_t)(((uint32_t)0x00000000U)) << 6U) | (uint32_t)(25U)),                 
     
    CAN_FLAG_ERR_SUMMARY          = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(1U)),                  
    CAN_FLAG_BUSOFF               = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(2U)),                  
    CAN_FLAG_RECEIVING            = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(3U)),                  
    CAN_FLAG_TRANSMITTING         = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(6U)),                  
    CAN_FLAG_IDLE                 = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(7U)),                  
    CAN_FLAG_RX_WARNING           = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(8U)),                  
    CAN_FLAG_TX_WARNING           = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(9U)),                  
    CAN_FLAG_STUFF_ERR            = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(10U)),                 
    CAN_FLAG_FORM_ERR             = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(11U)),                 
    CAN_FLAG_CRC_ERR              = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(12U)),                 
    CAN_FLAG_ACK_ERR              = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(13U)),                 
    CAN_FLAG_BIT_DOMINANT_ERR     = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(14U)),                 
    CAN_FLAG_BIT_RECESSIVE_ERR    = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(15U)),                 
    CAN_FLAG_SYNC_ERR             = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(18U)),                 
    CAN_FLAG_BUSOFF_RECOVERY      = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(19U)),                 
    CAN_FLAG_ERR_SUMMARY_FD       = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(20U)),                 
    CAN_FLAG_ERR_OVERRUN          = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(21U)),                 
    CAN_FLAG_STUFF_ERR_FD         = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(26U)),                 
    CAN_FLAG_FORM_ERR_FD          = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(27U)),                 
    CAN_FLAG_CRC_ERR_FD           = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(28U)),                 
    CAN_FLAG_BIT_DOMINANT_ERR_FD  = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(30U)),                 
    CAN_FLAG_BIT_RECESSIVE_ERR_FD = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(31U)),                 
     
    CAN_FLAG_MB0                  = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(0U)),                  
    CAN_FLAG_MB1                  = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(1U)),                  
    CAN_FLAG_MB2                  = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(2U)),                  
    CAN_FLAG_MB3                  = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(3U)),                  
    CAN_FLAG_MB4                  = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(4U)),                  
    CAN_FLAG_MB5                  = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(5U)),                  
    CAN_FLAG_MB6                  = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(6U)),                  
    CAN_FLAG_MB7                  = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(7U)),                  
    CAN_FLAG_MB8                  = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(8U)),                  
    CAN_FLAG_MB9                  = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(9U)),                  
    CAN_FLAG_MB10                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(10U)),                 
    CAN_FLAG_MB11                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(11U)),                 
    CAN_FLAG_MB12                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(12U)),                 
    CAN_FLAG_MB13                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(13U)),                 
    CAN_FLAG_MB14                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(14U)),                 
    CAN_FLAG_MB15                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(15U)),                 
    CAN_FLAG_MB16                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(16U)),                 
    CAN_FLAG_MB17                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(17U)),                 
    CAN_FLAG_MB18                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(18U)),                 
    CAN_FLAG_MB19                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(19U)),                 
    CAN_FLAG_MB20                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(20U)),                 
    CAN_FLAG_MB21                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(21U)),                 
    CAN_FLAG_MB22                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(22U)),                 
    CAN_FLAG_MB23                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(23U)),                 
    CAN_FLAG_MB24                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(24U)),                 
    CAN_FLAG_MB25                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(25U)),                 
    CAN_FLAG_MB26                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(26U)),                 
    CAN_FLAG_MB27                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(27U)),                 
    CAN_FLAG_MB28                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(28U)),                 
    CAN_FLAG_MB29                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(29U)),                 
    CAN_FLAG_MB30                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(30U)),                 
    CAN_FLAG_MB31                 = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(31U)),                 
    CAN_FLAG_FIFO_AVAILABLE       = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(5U)),                  
    CAN_FLAG_FIFO_WARNING         = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(6U)),                  
    CAN_FLAG_FIFO_OVERFLOW        = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(7U)),                  
     
    CAN_FLAG_WAKEUP_MATCH         = (((uint32_t)(((uint32_t)0x00000B08U)) << 6U) | (uint32_t)(16U)),              
    CAN_FLAG_WAKEUP_TIMEOUT       = (((uint32_t)(((uint32_t)0x00000B08U)) << 6U) | (uint32_t)(17U)),              
     
    CAN_FLAG_TDC_OUT_OF_RANGE     = (((uint32_t)(((uint32_t)0x00000C00U)) << 6U) | (uint32_t)(14U)),                
} can_flag_enum;

 
typedef enum {
     
    CAN_INT_FLAG_ERR_SUMMARY      = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(1U)),                  
    CAN_INT_FLAG_BUSOFF           = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(2U)),                  
    CAN_INT_FLAG_RX_WARNING       = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(16U)),                 
    CAN_INT_FLAG_TX_WARNING       = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(17U)),                 
    CAN_INT_FLAG_BUSOFF_RECOVERY  = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(19U)),                 
    CAN_INT_FLAG_ERR_SUMMARY_FD   = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(20U)),                 
     
    CAN_INT_FLAG_MB0              = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(0U)),                  
    CAN_INT_FLAG_MB1              = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(1U)),                  
    CAN_INT_FLAG_MB2              = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(2U)),                  
    CAN_INT_FLAG_MB3              = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(3U)),                  
    CAN_INT_FLAG_MB4              = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(4U)),                  
    CAN_INT_FLAG_MB5              = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(5U)),                  
    CAN_INT_FLAG_MB6              = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(6U)),                  
    CAN_INT_FLAG_MB7              = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(7U)),                  
    CAN_INT_FLAG_MB8              = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(8U)),                  
    CAN_INT_FLAG_MB9              = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(9U)),                  
    CAN_INT_FLAG_MB10             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(10U)),                 
    CAN_INT_FLAG_MB11             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(11U)),                 
    CAN_INT_FLAG_MB12             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(12U)),                 
    CAN_INT_FLAG_MB13             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(13U)),                 
    CAN_INT_FLAG_MB14             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(14U)),                 
    CAN_INT_FLAG_MB15             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(15U)),                 
    CAN_INT_FLAG_MB16             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(16U)),                 
    CAN_INT_FLAG_MB17             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(17U)),                 
    CAN_INT_FLAG_MB18             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(18U)),                 
    CAN_INT_FLAG_MB19             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(19U)),                 
    CAN_INT_FLAG_MB20             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(20U)),                 
    CAN_INT_FLAG_MB21             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(21U)),                 
    CAN_INT_FLAG_MB22             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(22U)),                 
    CAN_INT_FLAG_MB23             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(23U)),                 
    CAN_INT_FLAG_MB24             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(24U)),                 
    CAN_INT_FLAG_MB25             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(25U)),                 
    CAN_INT_FLAG_MB26             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(26U)),                 
    CAN_INT_FLAG_MB27             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(27U)),                 
    CAN_INT_FLAG_MB28             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(28U)),                 
    CAN_INT_FLAG_MB29             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(29U)),                 
    CAN_INT_FLAG_MB30             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(30U)),                 
    CAN_INT_FLAG_MB31             = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(31U)),                 
    CAN_INT_FLAG_FIFO_AVAILABLE   = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(5U)),                  
    CAN_INT_FLAG_FIFO_WARNING     = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(6U)),                  
    CAN_INT_FLAG_FIFO_OVERFLOW    = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(7U)),                  
     
    CAN_INT_FLAG_WAKEUP_MATCH     = (((uint32_t)(((uint32_t)0x00000B08U)) << 6U) | (uint32_t)(16U)),              
    CAN_INT_FLAG_WAKEUP_TIMEOUT   = (((uint32_t)(((uint32_t)0x00000B08U)) << 6U) | (uint32_t)(17U))               
} can_interrupt_flag_enum;

 
typedef enum {
    CAN_NORMAL_MODE               = 0U,                                                   
    CAN_MONITOR_MODE              = 1U,                                                   
    CAN_LOOPBACK_SILENT_MODE      = 2U,                                                   
    CAN_INACTIVE_MODE             = 3U,                                                   
    CAN_DISABLE_MODE              = 4U,                                                   
    CAN_PN_MODE                   = 5U                                                    
} can_operation_modes_enum;

 
typedef enum {
    CAN_INIT_STRUCT               = 0U,                                                   
    CAN_FD_INIT_STRUCT            = 1U,                                                   
    CAN_FIFO_INIT_STRUCT          = 2U,                                                   
    CAN_PN_MODE_INIT_STRUCT       = 3U,                                                   
    CAN_PN_MODE_FILTER_STRUCT     = 4U,                                                   
    CAN_MDSC_STRUCT               = 5U,                                                   
    CAN_FDES_STRUCT               = 6U,                                                   
    CAN_FIFO_ID_FILTER_STRUCT     = 7U,                                                   
    CAN_CRC_STRUCT                = 8U,                                                   
    CAN_ERRCNT_STRUCT             = 9U,                                                   
} can_struct_type_enum;

 
typedef enum {
    CAN_ERROR_STATE_ACTIVE        = 0U,                                                   
    CAN_ERROR_STATE_PASSIVE       = 1U,                                                   
    CAN_ERROR_STATE_BUS_OFF       = 2U                                                    
} can_error_state_enum;

 
typedef struct {
    uint8_t fd_data_phase_rx_errcnt;                                                      
    uint8_t fd_data_phase_tx_errcnt;                                                      
    uint8_t rx_errcnt;                                                                    
    uint8_t tx_errcnt;                                                                    
} can_error_counter_struct;

 
typedef struct {
    uint32_t internal_counter_source;                                                     
    uint32_t mb_tx_order;                                                                 
    uint32_t mb_rx_ide_rtr_type;                                                          
    uint32_t mb_remote_frame;                                                             
    uint8_t self_reception;                                                               
    uint8_t mb_tx_abort_enable;                                                           
    uint8_t local_priority_enable;                                                        
    uint8_t rx_private_filter_queue_enable;                                               
    uint32_t edge_filter_enable;                                                          
    uint32_t protocol_exception_enable;                                                   
    uint32_t rx_filter_order;                                                             
    uint32_t memory_size;                                                                 
    uint32_t mb_public_filter;                                                            
    uint32_t prescaler;                                                                   
    uint8_t resync_jump_width;                                                            
    uint8_t prop_time_segment;                                                            
    uint8_t time_segment_1;                                                               
    uint8_t time_segment_2;                                                               
} can_parameter_struct;

 
typedef struct {
    uint32_t timestamp : 16;                                                              
    uint32_t dlc : 4;                                                                     
    uint32_t rtr : 1;                                                                     
    uint32_t ide : 1;                                                                     
    uint32_t srr : 1;                                                                     
    uint32_t reserve1 : 1;                                                                
    uint32_t code : 4;                                                                    
    uint32_t reserve2 : 1;                                                                
    uint32_t esi : 1;                                                                     
    uint32_t brs : 1;                                                                     
    uint32_t fdf : 1;                                                                     
    uint32_t id : 29;                                                                     
    uint32_t prio : 3;                                                                    
    uint32_t *data;                                                                       
    uint32_t data_bytes;                                                                  
    uint8_t padding;                                                                      
} can_mailbox_descriptor_struct;

 
typedef struct {
    uint32_t timestamp : 16;                                                              
    uint32_t dlc : 4;                                                                     
    uint32_t rtr : 1;                                                                     
    uint32_t ide : 1;                                                                     
    uint32_t srr : 1;                                                                     
    uint32_t idhit : 9;                                                                   
    uint32_t id;                                                                          
    uint32_t data[2];                                                                     
} can_rx_fifo_struct;

 
typedef struct {
    uint32_t iso_can_fd_enable;                                                           
    uint32_t bitrate_switch_enable;                                                       
    uint32_t mailbox_data_size;                                                           
    uint32_t tdc_enable;                                                                  
    uint32_t tdc_offset;                                                                  
    uint32_t prescaler;                                                                   
    uint8_t resync_jump_width;                                                            
    uint8_t prop_time_segment;                                                            
    uint8_t time_segment_1;                                                               
    uint8_t time_segment_2;                                                               
} can_fd_parameter_struct;

 
typedef struct {
    uint32_t remote_frame;                                                                
    uint32_t extended_frame;                                                              
    uint32_t id;                                                                          
} can_rx_fifo_id_filter_struct;

 
typedef struct {
    uint8_t dma_enable;                                                                   
    uint32_t filter_format_and_number;                                                    
    uint32_t fifo_public_filter;                                                          
} can_fifo_parameter_struct;

 
typedef struct {
    uint32_t rtr;                                                                         
    uint32_t ide;                                                                         
    uint32_t id;                                                                          
    uint32_t dlc_high_threshold;                                                          
    uint32_t dlc_low_threshold;                                                           
    uint32_t payload[2];                                                                  
} can_pn_mode_filter_struct;

 
typedef struct {
    uint32_t timeout_int;                                                                 
    uint32_t match_int;                                                                   
    uint32_t num_matches;                                                                 
    uint32_t match_timeout;                                                               
    uint32_t frame_filter;                                                                
    uint32_t id_filter;                                                                   
    uint32_t data_filter;                                                                 
} can_pn_mode_config_struct;

 
typedef struct {
    uint32_t classical_frm_mb_number;                                                     
    uint32_t classical_frm_transmitted_crc;                                               
    uint32_t classical_fd_frm_mb_number;                                                  
    uint32_t classical_fd_frm_transmitted_crc;                                            
} can_crc_struct;

 



 


 

 

 

 






 

 

 

 

 






 

 

 

 

 

 



 

 

 




 



 


 



 

 
 
 
void can_deinit(uint32_t can_periph);
 
ErrStatus can_software_reset(uint32_t can_periph);
 
ErrStatus can_para_init(uint32_t can_periph, can_parameter_struct *can_parameter_init);
 
void can_struct_para_init(can_struct_type_enum type, void *p_struct);
 
void can_private_filter_config(uint32_t can_periph, uint32_t index, uint32_t filter_data);

 
 
ErrStatus can_operation_mode_enter(uint32_t can_periph, can_operation_modes_enum mode);
 
can_operation_modes_enum can_operation_mode_get(uint32_t can_periph);
 
ErrStatus can_inactive_mode_exit(uint32_t can_periph);
 
ErrStatus can_pn_mode_exit(uint32_t can_periph);

 
 
void can_fd_config(uint32_t can_periph, can_fd_parameter_struct *can_fd_para_init);
 
void can_bitrate_switch_enable(uint32_t can_periph);
 
void can_bitrate_switch_disable(uint32_t can_periph);
 
uint32_t can_tdc_get(uint32_t can_periph);
 
void can_tdc_enable(uint32_t can_periph);
 
void can_tdc_disable(uint32_t can_periph);

 
 
void can_rx_fifo_config(uint32_t can_periph, can_fifo_parameter_struct *can_fifo_para_init);
 
void can_rx_fifo_filter_table_config(uint32_t can_periph, can_rx_fifo_id_filter_struct id_filter_table[]);
 
void can_rx_fifo_read(uint32_t can_periph, can_rx_fifo_struct *rx_fifo);
 
uint32_t can_rx_fifo_filter_matching_number_get(uint32_t can_periph);
 
void can_rx_fifo_clear(uint32_t can_periph);

 
 
uint32_t *can_ram_address_get(uint32_t can_periph, uint32_t index);
 
void can_mailbox_config(uint32_t can_periph, uint32_t index, can_mailbox_descriptor_struct *mdpara);
 
void can_mailbox_transmit_abort(uint32_t can_periph, uint32_t index);
 
void can_mailbox_transmit_inactive(uint32_t can_periph, uint32_t index);
 
ErrStatus can_mailbox_receive_data_read(uint32_t can_periph, uint32_t index, can_mailbox_descriptor_struct *mdpara);
 
void can_mailbox_receive_lock(uint32_t can_periph, uint32_t index);
 
void can_mailbox_receive_unlock(uint32_t can_periph);
 
void can_mailbox_receive_inactive(uint32_t can_periph, uint32_t index);
 
uint32_t can_mailbox_code_get(uint32_t can_periph, uint32_t index);

 
 
void can_error_counter_config(uint32_t can_periph, can_error_counter_struct *errcnt_struct);
 
void can_error_counter_get(uint32_t can_periph, can_error_counter_struct *errcnt_struct);
 
can_error_state_enum can_error_state_get(uint32_t can_periph);
 
void can_crc_get(uint32_t can_periph, can_crc_struct *crc_struct);

 
 
void can_pn_mode_config(uint32_t can_periph, can_pn_mode_config_struct *pnmod_config);
 
void can_pn_mode_filter_config(uint32_t can_periph, can_pn_mode_filter_struct *expect, can_pn_mode_filter_struct *filter);
 
int32_t can_pn_mode_num_of_match_get(uint32_t can_periph);
 
void can_pn_mode_data_read(uint32_t can_periph, uint32_t index, can_mailbox_descriptor_struct *mdpara);

 
 
void can_self_reception_enable(uint32_t can_periph);
 
void can_self_reception_disable(uint32_t can_periph);
 
void can_transmit_abort_enable(uint32_t can_periph);
 
void can_transmit_abort_disable(uint32_t can_periph);
 
void can_auto_busoff_recovery_enable(uint32_t can_periph);
 
void can_auto_busoff_recovery_disable(uint32_t can_periph);
 
void can_time_sync_enable(uint32_t can_periph);
 
void can_time_sync_disable(uint32_t can_periph);
 
void can_edge_filter_mode_enable(uint32_t can_periph);
 
void can_edge_filter_mode_disable(uint32_t can_periph);
 
void can_ped_mode_enable(uint32_t can_periph);
 
void can_ped_mode_disable(uint32_t can_periph);
 
void can_arbitration_delay_bits_config(uint32_t can_periph, uint32_t delay_bits);
 
void can_bsp_mode_config(uint32_t can_periph, uint32_t sampling_mode);

 
 
FlagStatus can_flag_get(uint32_t can_periph, can_flag_enum flag);
 
void can_flag_clear(uint32_t can_periph, can_flag_enum flag);
 
ErrStatus can_interrupt_enable(uint32_t can_periph, can_interrupt_enum interrupt);
 
ErrStatus can_interrupt_disable(uint32_t can_periph, can_interrupt_enum interrupt);
 
FlagStatus can_interrupt_flag_get(uint32_t can_periph, can_interrupt_flag_enum int_flag);
 
void can_interrupt_flag_clear(uint32_t can_periph, can_interrupt_flag_enum int_flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 
 
typedef struct {
    uint32_t key_0_high;    
    uint32_t key_0_low;     
    uint32_t key_1_high;    
    uint32_t key_1_low;     
    uint32_t key_2_high;    
    uint32_t key_2_low;     
    uint32_t key_3_high;    
    uint32_t key_3_low;     
} cau_key_parameter_struct;

 
typedef struct {
    uint32_t iv_0_high;     
    uint32_t iv_0_low;      
    uint32_t iv_1_high;     
    uint32_t iv_1_low;      
} cau_iv_parameter_struct;

 
typedef struct {
    uint32_t ctl_config;      
    uint32_t iv_0_high;       
    uint32_t iv_0_low;        
    uint32_t iv_1_high;       
    uint32_t iv_1_low;        
    uint32_t key_0_high;      
    uint32_t key_0_low;       
    uint32_t key_1_high;      
    uint32_t key_1_low;       
    uint32_t key_2_high;      
    uint32_t key_2_low;       
    uint32_t key_3_high;      
    uint32_t key_3_low;       
    uint32_t gcmccmctxs[8];   
    uint32_t gcmctxs[8];      
} cau_context_parameter_struct;

 
typedef struct {
    uint32_t alg_dir;       
    uint8_t *key;           
    uint32_t key_size;      
    uint8_t *iv;            
    uint32_t iv_size;       
    uint8_t *input;         
    uint32_t in_length;     
    uint8_t *aad;           
    uint32_t aad_size;      
} cau_parameter_struct;

 







 

 

 

 

 

 
 
 
void cau_deinit(void);
 
void cau_struct_para_init(cau_parameter_struct *cau_parameter);
 
void cau_key_struct_para_init(cau_key_parameter_struct *key_initpara);
 
void cau_iv_struct_para_init(cau_iv_parameter_struct *iv_initpara);
 
void cau_context_struct_para_init(cau_context_parameter_struct *cau_context);

 
 
void cau_enable(void);
 
void cau_disable(void);
 
void cau_dma_enable(uint32_t dma_req);
 
void cau_dma_disable(uint32_t dma_req);
 
void cau_init(uint32_t alg_dir, uint32_t algo_mode, uint32_t swapping);
 
void cau_aes_key_select(uint32_t key_selection);
 
void cau_aes_keysize_config(uint32_t key_size);
 
void cau_key_init(cau_key_parameter_struct *key_initpara);
 
void cau_iv_init(cau_iv_parameter_struct *iv_initpara);
 
void cau_phase_config(uint32_t phase);
 
void cau_fifo_flush(void);
 
ControlStatus cau_enable_state_get(void);

 
 
void cau_data_write(uint32_t data);
 
uint32_t cau_data_read(void);

 
 
void cau_context_save(cau_context_parameter_struct *cau_context, cau_key_parameter_struct *key_initpara);
 
void cau_context_restore(cau_context_parameter_struct *cau_context);

 
 
ErrStatus cau_aes_ecb(cau_parameter_struct *cau_parameter, uint8_t *output);
 
ErrStatus cau_aes_cbc(cau_parameter_struct *cau_parameter, uint8_t *output);
 
ErrStatus cau_aes_ctr(cau_parameter_struct *cau_parameter, uint8_t *output);
 
ErrStatus cau_aes_cfb(cau_parameter_struct *cau_parameter, uint8_t *output);
 
ErrStatus cau_aes_ofb(cau_parameter_struct *cau_parameter, uint8_t *output);
 
ErrStatus cau_aes_gcm(cau_parameter_struct *cau_parameter, uint8_t *output, uint8_t *tag);
 
ErrStatus cau_aes_ccm(cau_parameter_struct *cau_parameter, uint8_t *output, uint8_t tag[], uint32_t tag_size, uint8_t aad_buf[]);
 
ErrStatus cau_tdes_ecb(cau_parameter_struct *cau_parameter, uint8_t *output);
 
ErrStatus cau_tdes_cbc(cau_parameter_struct *cau_parameter, uint8_t *output);
 
ErrStatus cau_des_ecb(cau_parameter_struct *cau_parameter, uint8_t *output);
 
ErrStatus cau_des_cbc(cau_parameter_struct *cau_parameter, uint8_t *output);

 
 
FlagStatus cau_flag_get(uint32_t flag);
 
void cau_interrupt_enable(uint32_t interrupt);
 
void cau_interrupt_disable(uint32_t interrupt);
 
FlagStatus cau_interrupt_flag_get(uint32_t int_flag);






 


























 



 

 

 
 

 

 

 


 
 
typedef enum{
    CMP0,                                                                        
    CMP1                                                                         
}cmp_enum;

 

 

 

 

 

 

 

 

 

 

 

 
 
 
void cmp_deinit(cmp_enum cmp_periph);
 
void cmp_mode_init(cmp_enum cmp_periph, uint32_t operating_mode, uint32_t inverting_input, uint32_t output_hysteresis);
 
void cmp_noninverting_input_select(cmp_enum cmp_periph, uint32_t noninverting_input);
 
void cmp_output_init(cmp_enum cmp_periph, uint32_t output_polarity);
 
void cmp_output_mux_config(cmp_enum cmp_periph, uint32_t cmp_output_sel);
 
void cmp_blanking_init(cmp_enum cmp_periph,uint32_t blanking_source_selection);

 
 
void cmp_enable(cmp_enum cmp_periph);
 
void cmp_disable(cmp_enum cmp_periph);
 
void cmp_window_enable(void);
 
void cmp_window_disable(void);
 
void cmp_lock_enable(cmp_enum cmp_periph);
 
void cmp_voltage_scaler_enable(cmp_enum cmp_periph);
 
void cmp_voltage_scaler_disable(cmp_enum cmp_periph);
 
void cmp_scaler_bridge_enable(cmp_enum cmp_periph);
 
void cmp_scaler_bridge_disable(cmp_enum cmp_periph);

 
 
uint32_t cmp_output_level_get(cmp_enum cmp_periph);

 
 
FlagStatus cmp_flag_get(cmp_enum cmp_periph, uint32_t flag);
 
void cmp_flag_clear(cmp_enum cmp_periph, uint32_t flag);
 
void cmp_interrupt_enable(cmp_enum cmp_periph, uint32_t interrupt);
 
void cmp_interrupt_disable(cmp_enum cmp_periph, uint32_t interrupt);
 
FlagStatus cmp_interrupt_flag_get(cmp_enum cmp_periph, uint32_t flag);
 
void cmp_interrupt_flag_clear(cmp_enum cmp_periph, uint32_t flag);






 


























 



 

 

 
 

 

 
 
typedef enum
{
    CPDM_OUTPUT_PHASE_SELECTION_0 = 0,                              
    CPDM_OUTPUT_PHASE_SELECTION_1,                                  
    CPDM_OUTPUT_PHASE_SELECTION_2,                                  
    CPDM_OUTPUT_PHASE_SELECTION_3,                                  
    CPDM_OUTPUT_PHASE_SELECTION_4,                                  
    CPDM_OUTPUT_PHASE_SELECTION_5,                                  
    CPDM_OUTPUT_PHASE_SELECTION_6,                                  
    CPDM_OUTPUT_PHASE_SELECTION_7,                                  
    CPDM_OUTPUT_PHASE_SELECTION_8,                                  
    CPDM_OUTPUT_PHASE_SELECTION_9,                                  
    CPDM_OUTPUT_PHASE_SELECTION_10,                                 
    CPDM_OUTPUT_PHASE_SELECTION_11,                                 
    CPDM_OUTPUT_PHASE_SELECTION_12,                                 
}cpdm_output_phase_enum;


 
 
 
void cpdm_enable(uint32_t cpdm_periph);
 
void cpdm_disable(uint32_t cpdm_periph);
 
void cpdm_delayline_sample_enable(uint32_t cpdm_periph);
 
void cpdm_delayline_sample_disable(uint32_t cpdm_periph);

 
 
void cpdm_output_clock_phase_select(uint32_t cpdm_periph, cpdm_output_phase_enum output_clock_phase);
 
void cpdm_delay_step_config(uint32_t cpdm_periph, uint8_t delay_step);
 
FlagStatus cpdm_delayline_length_valid_flag_get(uint32_t cpdm_periph);
 
uint16_t cpdm_delayline_length_get(uint32_t cpdm_periph);

 
 
ErrStatus cpdm_clock_output(uint32_t cpdm_periph, cpdm_output_phase_enum output_clock_phase);






 


























 



 

 

 
 

 

 

 

 

 
 

 

 

 
 
void crc_deinit(void);
 
void crc_reverse_output_data_enable(void);
 
void crc_reverse_output_data_disable(void);

 
void crc_data_register_reset(void);
 
uint32_t crc_data_register_read(void);

 
uint8_t crc_free_data_register_read(void);
 
void crc_free_data_register_write(uint8_t free_data);

 
void crc_init_data_register_write(uint32_t init_data);
 
void crc_input_data_reverse_config(uint32_t data_reverse);

 
void crc_polynomial_size_set(uint32_t poly_size);
 
void crc_polynomial_set(uint32_t poly);

 
uint32_t crc_single_data_calculate(uint32_t sdata, uint8_t data_format);
 
uint32_t crc_block_data_calculate(void *array, uint32_t size, uint8_t data_format);






 


























 



 

 

 
 

 

 

 

 
 

 

 

 

 

 

 

 
 
void ctc_deinit(void);

 
void ctc_counter_enable(void);
 
void ctc_counter_disable(void);

 
void ctc_irc48m_trim_value_config(uint8_t trim_value);
 
void ctc_software_refsource_pulse_generate(void);
 
void ctc_hardware_trim_mode_config(uint32_t hardmode);

 
void ctc_refsource_polarity_config(uint32_t polarity);
 
void ctc_refsource_signal_select(uint32_t refs);
 
void ctc_refsource_prescaler_config(uint32_t prescaler);
 
void ctc_clock_limit_value_config(uint8_t limit_value);
 
void ctc_counter_reload_value_config(uint16_t reload_value);

 
uint16_t ctc_counter_capture_value_read(void);
 
FlagStatus ctc_counter_direction_read(void);
 
uint16_t ctc_counter_reload_value_read(void);
 
uint8_t ctc_irc48m_trim_value_read(void);

 
 
FlagStatus ctc_flag_get(uint32_t flag);
 
void ctc_flag_clear(uint32_t flag);
 
void ctc_interrupt_enable(uint32_t interrupt);
 
void ctc_interrupt_disable(uint32_t interrupt);
 
FlagStatus ctc_interrupt_flag_get(uint32_t int_flag); 
 
void ctc_interrupt_flag_clear(uint32_t int_flag);







 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 

 

 

 

 

 

 

 

 

 
 
 
void dac_deinit(uint32_t dac_periph);
 
void dac_enable(uint32_t dac_periph, uint8_t dac_out);
 
void dac_disable(uint32_t dac_periph, uint8_t dac_out);
 
void dac_dma_enable(uint32_t dac_periph, uint8_t dac_out);
 
void dac_dma_disable(uint32_t dac_periph, uint8_t dac_out);

 
 
void dac_mode_config(uint32_t dac_periph, uint32_t dac_out, uint32_t mode);
 
uint32_t dac_trimming_value_get(uint32_t dac_periph, uint32_t dac_out);
 
void dac_trimming_value_set(uint32_t dac_periph, uint32_t dac_out, uint32_t trim_value);
 
void dac_trimming_enable(uint32_t dac_periph, uint32_t dac_out);

 
 
uint16_t dac_output_value_get(uint32_t dac_periph, uint8_t dac_out);
 
void dac_data_set(uint32_t dac_periph, uint8_t dac_out, uint32_t dac_align, uint16_t data);

 
 
void dac_trigger_enable(uint32_t dac_periph, uint8_t dac_out);
 
void dac_trigger_disable(uint32_t dac_periph, uint8_t dac_out);
 
void dac_trigger_source_config(uint32_t dac_periph, uint8_t dac_out, uint32_t triggersource);
 
void dac_software_trigger_enable(uint32_t dac_periph, uint8_t dac_out);

 
 
void dac_wave_mode_config(uint32_t dac_periph, uint8_t dac_out, uint32_t wave_mode);
 
void dac_lfsr_noise_config(uint32_t dac_periph, uint8_t dac_out, uint32_t unmask_bits);
 
void dac_triangle_noise_config(uint32_t dac_periph, uint8_t dac_out, uint32_t amplitude);

 
 
void dac_concurrent_enable(uint32_t dac_periph);
 
void dac_concurrent_disable(uint32_t dac_periph);
 
void dac_concurrent_software_trigger_enable(uint32_t dac_periph);
 
void dac_concurrent_data_set(uint32_t dac_periph, uint32_t dac_align, uint16_t data0, uint16_t data1);

 
 
void dac_sample_keep_mode_config(uint32_t dac_periph, uint32_t dac_out, uint32_t sample_time, uint32_t keep_time, uint32_t refresh_time);

 
 
FlagStatus dac_flag_get(uint32_t dac_periph, uint32_t flag);
 
void dac_flag_clear(uint32_t dac_periph, uint32_t flag);
 
void dac_interrupt_enable(uint32_t dac_periph, uint32_t interrupt);
 
void dac_interrupt_disable(uint32_t dac_periph, uint32_t interrupt);
 
FlagStatus dac_interrupt_flag_get(uint32_t dac_periph, uint32_t int_flag);
 
void dac_interrupt_flag_clear(uint32_t dac_periph, uint32_t int_flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 
typedef enum 
{
    DBG_IDX_CTL1            = 0x34U,                                          
    DBG_IDX_CTL2            = 0x3CU,                                          
    DBG_IDX_CTL3            = 0x4CU,                                          
    DBG_IDX_CTL4            = 0x54U                                           
}dbg_reg_idx;

 
typedef enum
{
    DBG_WWDGT_HOLD               = (((DBG_IDX_CTL1) << 6) | (6U)),                      
    DBG_I2C3_HOLD                = (((DBG_IDX_CTL2) << 6) | (24U)),                     
    DBG_I2C2_HOLD                = (((DBG_IDX_CTL2) << 6) | (23U)),                     
    DBG_I2C1_HOLD                = (((DBG_IDX_CTL2) << 6) | (22U)),                     
    DBG_I2C0_HOLD                = (((DBG_IDX_CTL2) << 6) | (21U)),                     
    DBG_TIMER51_HOLD             = (((DBG_IDX_CTL2) << 6) | (11U)),                     
    DBG_TIMER50_HOLD             = (((DBG_IDX_CTL2) << 6) | (10U)),                     
    DBG_TIMER31_HOLD             = (((DBG_IDX_CTL2) << 6) | (9U)),                      
    DBG_TIMER30_HOLD             = (((DBG_IDX_CTL2) << 6) | (8U)),                      
    DBG_TIMER23_HOLD             = (((DBG_IDX_CTL2) << 6) | (7U)),                      
    DBG_TIMER22_HOLD             = (((DBG_IDX_CTL2) << 6) | (6U)),                      
    DBG_TIMER6_HOLD              = (((DBG_IDX_CTL2) << 6) | (5U)),                      
    DBG_TIMER5_HOLD              = (((DBG_IDX_CTL2) << 6) | (4U)),                      
    DBG_TIMER4_HOLD              = (((DBG_IDX_CTL2) << 6) | (3U)),                      
    DBG_TIMER3_HOLD              = (((DBG_IDX_CTL2) << 6) | (2U)),                      
    DBG_TIMER2_HOLD              = (((DBG_IDX_CTL2) << 6) | (1U)),                      
    DBG_TIMER1_HOLD              = (((DBG_IDX_CTL2) << 6) | (0U)),                      
    DBG_TIMER44_HOLD             = (((DBG_IDX_CTL3) << 6) | (23U)),                     
    DBG_TIMER43_HOLD             = (((DBG_IDX_CTL3) << 6) | (22U)),                     
    DBG_TIMER42_HOLD             = (((DBG_IDX_CTL3) << 6) | (21U)),                     
    DBG_TIMER41_HOLD             = (((DBG_IDX_CTL3) << 6) | (20U)),                     
    DBG_TIMER40_HOLD             = (((DBG_IDX_CTL3) << 6) | (19U)),                     
    DBG_TIMER16_HOLD             = (((DBG_IDX_CTL3) << 6) | (18U)),                     
    DBG_TIMER15_HOLD             = (((DBG_IDX_CTL3) << 6) | (17U)),                     
    DBG_TIMER14_HOLD             = (((DBG_IDX_CTL3) << 6) | (16U)),                     
    DBG_CAN2_HOLD                = (((DBG_IDX_CTL3) << 6) | (4U)),                      
    DBG_CAN1_HOLD                = (((DBG_IDX_CTL3) << 6) | (3U)),                          
    DBG_CAN0_HOLD                = (((DBG_IDX_CTL3) << 6) | (2U)),                      
    DBG_TIMER7_HOLD              = (((DBG_IDX_CTL3) << 6) | (1U)),                      
    DBG_TIMER0_HOLD              = (((DBG_IDX_CTL3) << 6) | (0U)),                      
    DBG_FWDGT_HOLD               = (((DBG_IDX_CTL4) << 6) | (18U)),                     
    DBG_RTC_HOLD                 = (((DBG_IDX_CTL4) << 6) | (16U))                      
}dbg_periph_enum;


 
 
void dbg_deinit(void);
 
uint32_t dbg_id_get(void);

 
void dbg_low_power_enable(uint32_t dbg_low_power);
 
void dbg_low_power_disable(uint32_t dbg_low_power);

 
void dbg_trace_pin_enable(void);
 
void dbg_trace_pin_disable(void);
 
void dbg_trace_pin_mode_set(uint32_t trace_mode);

 
void dbg_periph_enable(dbg_periph_enum dbg_periph);
 
void dbg_periph_disable(dbg_periph_enum dbg_periph);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 
 
typedef struct {
    uint32_t capture_mode;                                            
    uint32_t clock_polarity;                                          
    uint32_t hsync_polarity;                                          
    uint32_t vsync_polarity;                                          
    uint32_t frame_rate;                                              
    uint32_t interface_format;                                        
} dci_parameter_struct;









 

 

 

 
 
 
void dci_deinit(void);
 
void dci_init(dci_parameter_struct *dci_struct);
 
void dci_enable(void);
 
void dci_disable(void);
 
void dci_capture_enable(void);
 
void dci_capture_disable(void);
 
void dci_external_vsync_enable(void);
 
void dci_external_vsync_disable(void);
 
void dci_automatic_error_correction_enable(void);
 
void dci_automatic_error_correction_disable(void);
 
void dci_jpeg_enable(void);
 
void dci_jpeg_disable(void);

 
 
void dci_crop_window_enable(void);
 
void dci_crop_window_disable(void);
 
void dci_ccir_enable(void);
 
void dci_ccir_disable(void);
 
void dci_ccir_mode_select(uint32_t ccir_mode);
 
void dci_crop_window_config(uint16_t start_x, uint16_t start_y, uint16_t size_width, uint16_t size_height);
 
void dci_embedded_sync_enable(void);
 
void dci_embedded_sync_disable(void);
 
void dci_sync_codes_config(uint8_t frame_start, uint8_t line_start, uint8_t line_end, uint8_t frame_end);
 
void dci_sync_codes_unmask_config(uint8_t frame_start, uint8_t line_start, uint8_t line_end, uint8_t frame_end);
 
uint32_t dci_data_read(void);

 
 
FlagStatus dci_flag_get(uint32_t flag);
 
void dci_interrupt_enable(uint32_t interrupt);
 
void dci_interrupt_disable(uint32_t interrupt);
 
FlagStatus dci_interrupt_flag_get(uint32_t int_flag);
 
void dci_interrupt_flag_clear(uint32_t int_flag);






 


























 



 

 

 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 
typedef struct {
    uint32_t request;                                                                                                   
    uint32_t periph_addr;                                                                                               
    uint32_t periph_width;                                                                                              
    uint32_t periph_inc;                                                                                                
    uint32_t memory0_addr;                                                                                              
    uint32_t memory_width;                                                                                              
    uint32_t memory_inc;                                                                                                
    uint32_t memory_burst_width;                                                                                        
    uint32_t periph_burst_width;                                                                                        
    uint32_t critical_value;                                                                                            
    uint32_t circular_mode;                                                                                             
    uint32_t direction;                                                                                                 
    uint32_t number;                                                                                                    
    uint32_t priority;                                                                                                  
} dma_multi_data_parameter_struct;

 
typedef struct {
    uint32_t request;                                                                                                   
    uint32_t periph_addr;                                                                                               
    uint32_t periph_inc;                                                                                                
    uint32_t memory0_addr;                                                                                              
    uint32_t memory_inc;                                                                                                
    uint32_t periph_memory_width;                                                                                       
    uint32_t circular_mode;                                                                                             
    uint32_t direction;                                                                                                 
    uint32_t number;                                                                                                    
    uint32_t priority;                                                                                                  
} dma_single_data_parameter_struct;

 
typedef struct {
    uint32_t sync_id;                                                                                                   
    uint32_t sync_polarity;                                                                                             
    uint32_t request_number;                                                                                            
} dmamux_sync_parameter_struct;

 
typedef struct {
    uint32_t trigger_id;                                                                                                
    uint32_t trigger_polarity;                                                                                          
    uint32_t request_number;                                                                                            
} dmamux_gen_parameter_struct;

 
typedef enum {
    DMA_CH0 = 0U,                                                                                                       
    DMA_CH1,                                                                                                            
    DMA_CH2,                                                                                                            
    DMA_CH3,                                                                                                            
    DMA_CH4,                                                                                                            
    DMA_CH5,                                                                                                            
    DMA_CH6,                                                                                                            
    DMA_CH7                                                                                                             
} dma_channel_enum;

 
typedef enum {
    DMAMUX_MUXCH0 = 0U,                                                                                                 
    DMAMUX_MUXCH1,                                                                                                      
    DMAMUX_MUXCH2,                                                                                                      
    DMAMUX_MUXCH3,                                                                                                      
    DMAMUX_MUXCH4,                                                                                                      
    DMAMUX_MUXCH5,                                                                                                      
    DMAMUX_MUXCH6,                                                                                                      
    DMAMUX_MUXCH7,                                                                                                      
    DMAMUX_MUXCH8,                                                                                                      
    DMAMUX_MUXCH9,                                                                                                      
    DMAMUX_MUXCH10,                                                                                                     
    DMAMUX_MUXCH11,                                                                                                     
    DMAMUX_MUXCH12,                                                                                                     
    DMAMUX_MUXCH13,                                                                                                     
    DMAMUX_MUXCH14,                                                                                                     
    DMAMUX_MUXCH15                                                                                                      
} dmamux_multiplexer_channel_enum;

 
typedef enum {
    DMAMUX_GENCH0 = 0U,                                                                                                 
    DMAMUX_GENCH1,                                                                                                      
    DMAMUX_GENCH2,                                                                                                      
    DMAMUX_GENCH3,                                                                                                      
    DMAMUX_GENCH4,                                                                                                      
    DMAMUX_GENCH5,                                                                                                      
    DMAMUX_GENCH6,                                                                                                      
    DMAMUX_GENCH7                                                                                                       
} dmamux_generator_channel_enum;

 
typedef enum {
     
    DMAMUX_INT_MUXCH0_SO  = (((uint32_t)(((uint32_t)0x00000000U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_MUXCH1_SO  = (((uint32_t)(((uint32_t)0x00000004U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_MUXCH2_SO  = (((uint32_t)(((uint32_t)0x00000008U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_MUXCH3_SO  = (((uint32_t)(((uint32_t)0x0000000CU)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_MUXCH4_SO  = (((uint32_t)(((uint32_t)0x00000010U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_MUXCH5_SO  = (((uint32_t)(((uint32_t)0x00000014U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_MUXCH6_SO  = (((uint32_t)(((uint32_t)0x00000018U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_MUXCH7_SO  = (((uint32_t)(((uint32_t)0x0000001CU)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_MUXCH8_SO  = (((uint32_t)(((uint32_t)0x00000020U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_MUXCH9_SO  = (((uint32_t)(((uint32_t)0x00000024U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_MUXCH10_SO = (((uint32_t)(((uint32_t)0x00000028U)) << 6U) | (uint32_t)(8U)),                                        
    DMAMUX_INT_MUXCH11_SO = (((uint32_t)(((uint32_t)0x0000002CU)) << 6U) | (uint32_t)(8U)),                                        
    DMAMUX_INT_MUXCH12_SO = (((uint32_t)(((uint32_t)0x00000030U)) << 6U) | (uint32_t)(8U)),                                        
    DMAMUX_INT_MUXCH13_SO = (((uint32_t)(((uint32_t)0x00000034U)) << 6U) | (uint32_t)(8U)),                                        
    DMAMUX_INT_MUXCH14_SO = (((uint32_t)(((uint32_t)0x00000038U)) << 6U) | (uint32_t)(8U)),                                        
    DMAMUX_INT_MUXCH15_SO = (((uint32_t)(((uint32_t)0x0000003CU)) << 6U) | (uint32_t)(8U)),                                        
    DMAMUX_INT_GENCH0_TO  = (((uint32_t)(((uint32_t)0x00000100U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_GENCH1_TO  = (((uint32_t)(((uint32_t)0x00000104U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_GENCH2_TO  = (((uint32_t)(((uint32_t)0x00000108U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_GENCH3_TO  = (((uint32_t)(((uint32_t)0x0000010CU)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_GENCH4_TO  = (((uint32_t)(((uint32_t)0x00000110U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_GENCH5_TO  = (((uint32_t)(((uint32_t)0x00000114U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_GENCH6_TO  = (((uint32_t)(((uint32_t)0x00000118U)) << 6U) | (uint32_t)(8U)),                                         
    DMAMUX_INT_GENCH7_TO  = (((uint32_t)(((uint32_t)0x0000011CU)) << 6U) | (uint32_t)(8U))                                          
} dmamux_interrupt_enum;

 
typedef enum {
     
    DMAMUX_FLAG_MUXCH0_SO  = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(0U)),                                          
    DMAMUX_FLAG_MUXCH1_SO  = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(1U)),                                          
    DMAMUX_FLAG_MUXCH2_SO  = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(2U)),                                          
    DMAMUX_FLAG_MUXCH3_SO  = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(3U)),                                          
    DMAMUX_FLAG_MUXCH4_SO  = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(4U)),                                          
    DMAMUX_FLAG_MUXCH5_SO  = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(5U)),                                          
    DMAMUX_FLAG_MUXCH6_SO  = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(6U)),                                          
    DMAMUX_FLAG_MUXCH7_SO  = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(7U)),                                          
    DMAMUX_FLAG_MUXCH8_SO  = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(8U)),                                          
    DMAMUX_FLAG_MUXCH9_SO  = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(9U)),                                          
    DMAMUX_FLAG_MUXCH10_SO = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(10U)),                                         
    DMAMUX_FLAG_MUXCH11_SO = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(11U)),                                         
    DMAMUX_FLAG_MUXCH12_SO = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(12U)),                                         
    DMAMUX_FLAG_MUXCH13_SO = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(13U)),                                         
    DMAMUX_FLAG_MUXCH14_SO = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(14U)),                                         
    DMAMUX_FLAG_MUXCH15_SO = (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(15U)),                                         
    DMAMUX_FLAG_GENCH0_TO  = (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(0U)),                                          
    DMAMUX_FLAG_GENCH1_TO  = (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(1U)),                                          
    DMAMUX_FLAG_GENCH2_TO  = (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(2U)),                                          
    DMAMUX_FLAG_GENCH3_TO  = (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(3U)),                                          
    DMAMUX_FLAG_GENCH4_TO  = (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(4U)),                                          
    DMAMUX_FLAG_GENCH5_TO  = (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(5U)),                                          
    DMAMUX_FLAG_GENCH6_TO  = (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(6U)),                                          
    DMAMUX_FLAG_GENCH7_TO  = (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(7U))                                           
} dmamux_flag_enum;

 
typedef enum {
     
    DMAMUX_INT_FLAG_MUXCH0_SO  = (((uint32_t)(((uint32_t)0x00000000U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(0U))),    
    DMAMUX_INT_FLAG_MUXCH1_SO  = (((uint32_t)(((uint32_t)0x00000004U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(1U))),    
    DMAMUX_INT_FLAG_MUXCH2_SO  = (((uint32_t)(((uint32_t)0x00000008U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(2U))),    
    DMAMUX_INT_FLAG_MUXCH3_SO  = (((uint32_t)(((uint32_t)0x0000000CU)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(3U))),    
    DMAMUX_INT_FLAG_MUXCH4_SO  = (((uint32_t)(((uint32_t)0x00000010U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(4U))),    
    DMAMUX_INT_FLAG_MUXCH5_SO  = (((uint32_t)(((uint32_t)0x00000014U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(5U))),    
    DMAMUX_INT_FLAG_MUXCH6_SO  = (((uint32_t)(((uint32_t)0x00000018U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(6U))),    
    DMAMUX_INT_FLAG_MUXCH7_SO  = (((uint32_t)(((uint32_t)0x0000001CU)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(7U))),    
    DMAMUX_INT_FLAG_MUXCH8_SO  = (((uint32_t)(((uint32_t)0x00000020U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(8U))),    
    DMAMUX_INT_FLAG_MUXCH9_SO  = (((uint32_t)(((uint32_t)0x00000024U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(9U))),    
    DMAMUX_INT_FLAG_MUXCH10_SO = (((uint32_t)(((uint32_t)0x00000028U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(10U))),  
    DMAMUX_INT_FLAG_MUXCH11_SO = (((uint32_t)(((uint32_t)0x0000002CU)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(11U))),  
    DMAMUX_INT_FLAG_MUXCH12_SO = (((uint32_t)(((uint32_t)0x00000030U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(12U))),  
    DMAMUX_INT_FLAG_MUXCH13_SO = (((uint32_t)(((uint32_t)0x00000034U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(13U))),  
    DMAMUX_INT_FLAG_MUXCH14_SO = (((uint32_t)(((uint32_t)0x00000038U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(14U))),  
    DMAMUX_INT_FLAG_MUXCH15_SO = (((uint32_t)(((uint32_t)0x0000003CU)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000080U)) << 6U) | (uint32_t)(15U))),  
    DMAMUX_INT_FLAG_GENCH0_TO  = (((uint32_t)(((uint32_t)0x00000100U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(0U))),    
    DMAMUX_INT_FLAG_GENCH1_TO  = (((uint32_t)(((uint32_t)0x00000104U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(1U))),    
    DMAMUX_INT_FLAG_GENCH2_TO  = (((uint32_t)(((uint32_t)0x00000108U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(2U))),    
    DMAMUX_INT_FLAG_GENCH3_TO  = (((uint32_t)(((uint32_t)0x0000010CU)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(3U))),    
    DMAMUX_INT_FLAG_GENCH4_TO  = (((uint32_t)(((uint32_t)0x00000110U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(4U))),    
    DMAMUX_INT_FLAG_GENCH5_TO  = (((uint32_t)(((uint32_t)0x00000114U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(5U))),    
    DMAMUX_INT_FLAG_GENCH6_TO  = (((uint32_t)(((uint32_t)0x00000118U)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(6U))),    
    DMAMUX_INT_FLAG_GENCH7_TO  = (((uint32_t)(((uint32_t)0x0000011CU)) << 22U) | (uint32_t)((8U) << 16U) | (((uint32_t)(((uint32_t)0x00000140U)) << 6U) | (uint32_t)(7U)))     
} dmamux_interrupt_flag_enum;

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
 
void dma_deinit(uint32_t dma_periph, dma_channel_enum channelx);
 
void dma_single_data_para_struct_init(dma_single_data_parameter_struct *init_struct);
 
void dma_multi_data_para_struct_init(dma_multi_data_parameter_struct *init_struct);
 
void dma_single_data_mode_init(uint32_t dma_periph, dma_channel_enum channelx, dma_single_data_parameter_struct *init_struct);
 
void dma_multi_data_mode_init(uint32_t dma_periph, dma_channel_enum channelx, dma_multi_data_parameter_struct *init_struct);

 
 
void dma_periph_address_config(uint32_t dma_periph, dma_channel_enum channelx, uint32_t address);
 
void dma_memory_address_config(uint32_t dma_periph, dma_channel_enum channelx, uint8_t memory_flag, uint32_t address);
 
void dma_transfer_number_config(uint32_t dma_periph, dma_channel_enum channelx, uint32_t number);
 
uint32_t dma_transfer_number_get(uint32_t dma_periph, dma_channel_enum channelx);
 
void dma_priority_config(uint32_t dma_periph, dma_channel_enum channelx, uint32_t priority);
 
void dma_memory_burst_beats_config(uint32_t dma_periph, dma_channel_enum channelx, uint32_t mbeat);
 
void dma_periph_burst_beats_config(uint32_t dma_periph, dma_channel_enum channelx, uint32_t pbeat);
 
void dma_memory_width_config(uint32_t dma_periph, dma_channel_enum channelx, uint32_t msize);
 
void dma_periph_width_config(uint32_t dma_periph, dma_channel_enum channelx, uint32_t psize);
 
void dma_memory_address_generation_config(uint32_t dma_periph, dma_channel_enum channelx, uint8_t generation_algorithm);
 
void dma_peripheral_address_generation_config(uint32_t dma_periph, dma_channel_enum channelx, uint8_t generation_algorithm);
 
void dma_circulation_enable(uint32_t dma_periph, dma_channel_enum channelx);
 
void dma_circulation_disable(uint32_t dma_periph, dma_channel_enum channelx);
 
void dma_channel_enable(uint32_t dma_periph, dma_channel_enum channelx);
 
void dma_channel_disable(uint32_t dma_periph, dma_channel_enum channelx);
 
void dma_transfer_direction_config(uint32_t dma_periph, dma_channel_enum channelx, uint8_t direction);
 
void dma_switch_buffer_mode_config(uint32_t dma_periph, dma_channel_enum channelx, uint32_t memory1_addr, uint32_t memory_select);
 
uint32_t dma_using_memory_get(uint32_t dma_periph, dma_channel_enum channelx);
 
void dma_switch_buffer_mode_enable(uint32_t dma_periph, dma_channel_enum channelx);
 
void dma_switch_buffer_mode_disable(uint32_t dma_periph, dma_channel_enum channelx);
 
uint32_t dma_fifo_status_get(uint32_t dma_periph, dma_channel_enum channelx);

 
 
FlagStatus dma_flag_get(uint32_t dma_periph, dma_channel_enum channelx, uint32_t flag);
 
void dma_flag_clear(uint32_t dma_periph, dma_channel_enum channelx, uint32_t flag);
 
void dma_interrupt_enable(uint32_t dma_periph, dma_channel_enum channelx, uint32_t interrupt);
 
void dma_interrupt_disable(uint32_t dma_periph, dma_channel_enum channelx, uint32_t interrupt);
 
FlagStatus dma_interrupt_flag_get(uint32_t dma_periph, dma_channel_enum channelx, uint32_t int_flag);
 
void dma_interrupt_flag_clear(uint32_t dma_periph, dma_channel_enum channelx, uint32_t int_flag);

 
 
 
void dmamux_sync_struct_para_init(dmamux_sync_parameter_struct *init_struct);
 
void dmamux_synchronization_init(dmamux_multiplexer_channel_enum channelx, dmamux_sync_parameter_struct *init_struct);
 
void dmamux_synchronization_enable(dmamux_multiplexer_channel_enum channelx);
 
void dmamux_synchronization_disable(dmamux_multiplexer_channel_enum channelx);
 
void dmamux_event_generation_enable(dmamux_multiplexer_channel_enum channelx);
 
void dmamux_event_generation_disable(dmamux_multiplexer_channel_enum channelx);

 
 
void dmamux_gen_struct_para_init(dmamux_gen_parameter_struct *init_struct);
 
void dmamux_request_generator_init(dmamux_generator_channel_enum channelx, dmamux_gen_parameter_struct *init_struct);
 
void dmamux_request_generator_channel_enable(dmamux_generator_channel_enum channelx);
 
void dmamux_request_generator_channel_disable(dmamux_generator_channel_enum channelx);

 
 
void dmamux_synchronization_polarity_config(dmamux_multiplexer_channel_enum channelx, uint32_t polarity);
 
void dmamux_request_forward_number_config(dmamux_multiplexer_channel_enum channelx, uint32_t number);
 
void dmamux_sync_id_config(dmamux_multiplexer_channel_enum channelx, uint32_t id);
 
void dmamux_request_id_config(dmamux_multiplexer_channel_enum channelx, uint32_t id);
 
void dmamux_trigger_polarity_config(dmamux_generator_channel_enum channelx, uint32_t polarity);
 
void dmamux_request_generate_number_config(dmamux_generator_channel_enum channelx, uint32_t number);
 
void dmamux_trigger_id_config(dmamux_generator_channel_enum channelx, uint32_t id);

 
 
FlagStatus dmamux_flag_get(dmamux_flag_enum flag);
 
void dmamux_flag_clear(dmamux_flag_enum flag);
 
void dmamux_interrupt_enable(dmamux_interrupt_enum interrupt);
 
void dmamux_interrupt_disable(dmamux_interrupt_enum interrupt);
 
FlagStatus dmamux_interrupt_flag_get(dmamux_interrupt_flag_enum int_flag);
 
void dmamux_interrupt_flag_clear(dmamux_interrupt_flag_enum int_flag);






 


























 



 

 

 
 

 

 

 

 

 

 
 

 

 

 
 
void edout_deinit(void);
 
void edout_init(uint32_t pol, uint32_t max_loc, uint32_t cur_loc);
 
void edout_enable(void);
 
void edout_disable(void);
 
void edout_polarity_config(uint32_t pol);
 
void edout_max_location_value_config(uint32_t max_loc);
 
void edout_output_counter_update(int16_t num_edges, uint16_t phase_diff);
 
void edout_current_location_config(uint32_t cur_loc);
 
uint16_t edout_current_location_get(void);
 
void edout_z_output_mode_config(uint32_t mode);
 
void edout_z_output_start_loc_and_width_config(uint32_t start_loc, uint32_t width);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 
typedef enum {
    EFUSE_INT_FLAG_ILLEGAL_ACCESS_ERR = (((uint32_t)(((uint32_t)0x0000000CU)) << 22) | (uint32_t)((0U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(16U))),           
    EFUSE_INT_FLAG_PROGRAM_COMPLETE = (((uint32_t)(((uint32_t)0x0000000CU)) << 22) | (uint32_t)((1U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(17U))),             
    EFUSE_INT_FLAG_READ_COMPLETE = (((uint32_t)(((uint32_t)0x0000000CU)) << 22) | (uint32_t)((2U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(18U))),                
    EFUSE_INT_FLAG_PROGRAM_VOLTAGE_ERR = (((uint32_t)(((uint32_t)0x0000000CU)) << 22) | (uint32_t)((3U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(19U))),          
} efuse_interrupt_flag_enum;

 

 

 

 

 
typedef enum {
    USER_CTL_SIZE     = 4U,                                                   
    MCU_RESERVED_SIZE = 4U,                                                   
    DP_SIZE           = 8U,                                                   
    AES_KEY_SIZE      = 16U,                                                  
    USER_DATA_SIZE    = 16U,                                                  
} efuse_system_para_size_enum;

 
typedef enum {
    USER_CTL_IDX      = 0U,                                                   
    MCU_RESERVED_IDX  = 1U,                                                   
    DP_IDX            = 2U,                                                   
    AES_KEY_IDX       = 3U,                                                   
    USER_DATA_IDX     = 4U,                                                   
} efuse_system_para_index_enum;

 
typedef enum {
    EFUSE_READY = 0U,                                                         
    EFUSE_BUSY,                                                               
    EFUSE_IAERR,                                                              
    EFUSE_PVERR,                                                              
    EFUSE_TOERR,                                                              
} efuse_state_enum;

 

 

 
 

 
ErrStatus efuse_read(uint32_t ef_addr, uint32_t size, uint32_t buf[]);
 
ErrStatus efuse_write(uint32_t ef_addr, uint32_t size, uint8_t *buf);
 
ErrStatus efuse_user_control_write(uint8_t *buf);
 
ErrStatus efuse_mcu_reserved_write(uint8_t *buf);
 
ErrStatus efuse_dp_write(uint8_t *buf);
 
ErrStatus efuse_aes_key_write(uint8_t *buf);
 
ErrStatus efuse_user_data_write(uint8_t *buf);

 
 
uint8_t efuse_aes_key_crc_get(void);
 
void efuse_monitor_program_voltage_enable(void);
 
void efuse_monitor_program_voltage_disable(void);
 
FlagStatus efuse_monitor_program_voltage_get(void);
 
FlagStatus efuse_ldo_ready_get(void);

 
 
FlagStatus efuse_flag_get(uint32_t flag);
 
void efuse_flag_clear(uint32_t flag);
 
void efuse_interrupt_enable(uint32_t interrupt);
 
void efuse_interrupt_disable(uint32_t interrupt);
 
FlagStatus efuse_interrupt_flag_get(efuse_interrupt_flag_enum int_flag);
 
void efuse_interrupt_flag_clear(uint32_t int_flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 




 

 

 

 


 
 

 

 

 




 
typedef enum {
     
    ENET_MAC_FLAG_MPKR              = (((uint32_t)(0x0000002CU) << 6U) | (uint32_t)(5U)),       
    ENET_MAC_FLAG_WUFR              = (((uint32_t)(0x0000002CU) << 6U) | (uint32_t)(6U)),       
     
    ENET_MAC_FLAG_FLOWCONTROL       = (((uint32_t)(0x00000018U) << 6U) | (uint32_t)(0U)),      
     
    ENET_MAC_FLAG_WUM               = (((uint32_t)(0x00000038U) << 6U) | (uint32_t)(3U)),      
    ENET_MAC_FLAG_MSC               = (((uint32_t)(0x00000038U) << 6U) | (uint32_t)(4U)),      
    ENET_MAC_FLAG_MSCR              = (((uint32_t)(0x00000038U) << 6U) | (uint32_t)(5U)),      
    ENET_MAC_FLAG_MSCT              = (((uint32_t)(0x00000038U) << 6U) | (uint32_t)(6U)),      
    ENET_MAC_FLAG_TMST              = (((uint32_t)(0x00000038U) << 6U) | (uint32_t)(9U)),      
     
    ENET_PTP_FLAG_TSSCO             = (((uint32_t)(0x00000728U) << 6U) | (uint32_t)(0U)),       
    ENET_PTP_FLAG_TTM               = (((uint32_t)(0x00000728U) << 6U) | (uint32_t)(1U)),       
     
    ENET_MSC_FLAG_RFCE              = (((uint32_t)(0x00000104U) << 6U) | (uint32_t)(5U)),     
    ENET_MSC_FLAG_RFAE              = (((uint32_t)(0x00000104U) << 6U) | (uint32_t)(6U)),     
    ENET_MSC_FLAG_RGUF              = (((uint32_t)(0x00000104U) << 6U) | (uint32_t)(17U)),    
     
    ENET_MSC_FLAG_TGFSC             = (((uint32_t)(0x00000108U) << 6U) | (uint32_t)(14U)),    
    ENET_MSC_FLAG_TGFMSC            = (((uint32_t)(0x00000108U) << 6U) | (uint32_t)(15U)),    
    ENET_MSC_FLAG_TGF               = (((uint32_t)(0x00000108U) << 6U) | (uint32_t)(21U)),    
     
    ENET_DMA_FLAG_TS                = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(0U)),      
    ENET_DMA_FLAG_TPS               = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(1U)),      
    ENET_DMA_FLAG_TBU               = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(2U)),      
    ENET_DMA_FLAG_TJT               = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(3U)),      
    ENET_DMA_FLAG_RO                = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(4U)),      
    ENET_DMA_FLAG_TU                = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(5U)),      
    ENET_DMA_FLAG_RS                = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(6U)),      
    ENET_DMA_FLAG_RBU               = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(7U)),      
    ENET_DMA_FLAG_RPS               = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(8U)),      
    ENET_DMA_FLAG_RWT               = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(9U)),      
    ENET_DMA_FLAG_ET                = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(10U)),     
    ENET_DMA_FLAG_FBE               = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(13U)),     
    ENET_DMA_FLAG_ER                = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(14U)),     
    ENET_DMA_FLAG_AI                = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(15U)),     
    ENET_DMA_FLAG_NI                = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(16U)),     
    ENET_DMA_FLAG_EB_DMA_ERROR      = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(23U)),     
    ENET_DMA_FLAG_EB_TRANSFER_ERROR = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(24U)),     
    ENET_DMA_FLAG_EB_ACCESS_ERROR   = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(25U)),     
    ENET_DMA_FLAG_MSC               = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(27U)),     
    ENET_DMA_FLAG_WUM               = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(28U)),     
    ENET_DMA_FLAG_TST               = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(29U))      
} enet_flag_enum;

 
typedef enum {
     
    ENET_DMA_FLAG_TS_CLR            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(0U)),      
    ENET_DMA_FLAG_TPS_CLR           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(1U)),      
    ENET_DMA_FLAG_TBU_CLR           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(2U)),      
    ENET_DMA_FLAG_TJT_CLR           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(3U)),      
    ENET_DMA_FLAG_RO_CLR            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(4U)),      
    ENET_DMA_FLAG_TU_CLR            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(5U)),      
    ENET_DMA_FLAG_RS_CLR            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(6U)),      
    ENET_DMA_FLAG_RBU_CLR           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(7U)),      
    ENET_DMA_FLAG_RPS_CLR           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(8U)),      
    ENET_DMA_FLAG_RWT_CLR           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(9U)),      
    ENET_DMA_FLAG_ET_CLR            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(10U)),     
    ENET_DMA_FLAG_FBE_CLR           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(13U)),     
    ENET_DMA_FLAG_ER_CLR            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(14U)),     
    ENET_DMA_FLAG_AI_CLR            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(15U)),     
    ENET_DMA_FLAG_NI_CLR            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(16U))      
} enet_flag_clear_enum;

 
typedef enum {
     
    ENET_MAC_INT_WUMIM              = (((uint32_t)(0x0000003CU) << 6U) | (uint32_t)(3U)),    
    ENET_MAC_INT_TMSTIM             = (((uint32_t)(0x0000003CU) << 6U) | (uint32_t)(9U)),    
     
    ENET_MSC_INT_RFCEIM             = (((uint32_t)(0x0000010CU) << 6U) | (uint32_t)(5U)),   
    ENET_MSC_INT_RFAEIM             = (((uint32_t)(0x0000010CU) << 6U) | (uint32_t)(6U)),   
    ENET_MSC_INT_RGUFIM             = (((uint32_t)(0x0000010CU) << 6U) | (uint32_t)(17U)),  
     
    ENET_MSC_INT_TGFSCIM            = (((uint32_t)(0x00000110U) << 6U) | (uint32_t)(14U)),  
    ENET_MSC_INT_TGFMSCIM           = (((uint32_t)(0x00000110U) << 6U) | (uint32_t)(15U)),  
    ENET_MSC_INT_TGFIM              = (((uint32_t)(0x00000110U) << 6U) | (uint32_t)(21U)),  
     
    ENET_DMA_INT_TIE                = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(0U)),     
    ENET_DMA_INT_TPSIE              = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(1U)),     
    ENET_DMA_INT_TBUIE              = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(2U)),     
    ENET_DMA_INT_TJTIE              = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(3U)),     
    ENET_DMA_INT_ROIE               = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(4U)),     
    ENET_DMA_INT_TUIE               = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(5U)),     
    ENET_DMA_INT_RIE                = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(6U)),     
    ENET_DMA_INT_RBUIE              = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(7U)),     
    ENET_DMA_INT_RPSIE              = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(8U)),     
    ENET_DMA_INT_RWTIE              = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(9U)),     
    ENET_DMA_INT_ETIE               = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(10U)),    
    ENET_DMA_INT_FBEIE              = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(13U)),    
    ENET_DMA_INT_ERIE               = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(14U)),    
    ENET_DMA_INT_AIE                = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(15U)),    
    ENET_DMA_INT_NIE                = (((uint32_t)(0x0000101CU) << 6U) | (uint32_t)(16U))     
} enet_int_enum;

 
typedef enum {
     
    ENET_MAC_INT_FLAG_WUM           = (((uint32_t)(0x00000038U) << 6U) | (uint32_t)(3U)),      
    ENET_MAC_INT_FLAG_MSC           = (((uint32_t)(0x00000038U) << 6U) | (uint32_t)(4U)),      
    ENET_MAC_INT_FLAG_MSCR          = (((uint32_t)(0x00000038U) << 6U) | (uint32_t)(5U)),      
    ENET_MAC_INT_FLAG_MSCT          = (((uint32_t)(0x00000038U) << 6U) | (uint32_t)(6U)),      
    ENET_MAC_INT_FLAG_TMST          = (((uint32_t)(0x00000038U) << 6U) | (uint32_t)(9U)),      
     
    ENET_MSC_INT_FLAG_RFCE          = (((uint32_t)(0x00000104U) << 6U) | (uint32_t)(5U)),     
    ENET_MSC_INT_FLAG_RFAE          = (((uint32_t)(0x00000104U) << 6U) | (uint32_t)(6U)),     
    ENET_MSC_INT_FLAG_RGUF          = (((uint32_t)(0x00000104U) << 6U) | (uint32_t)(17U)),    
     
    ENET_MSC_INT_FLAG_TGFSC         = (((uint32_t)(0x00000108U) << 6U) | (uint32_t)(14U)),    
    ENET_MSC_INT_FLAG_TGFMSC        = (((uint32_t)(0x00000108U) << 6U) | (uint32_t)(15U)),    
    ENET_MSC_INT_FLAG_TGF           = (((uint32_t)(0x00000108U) << 6U) | (uint32_t)(21U)),    
     
    ENET_DMA_INT_FLAG_TS            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(0U)),      
    ENET_DMA_INT_FLAG_TPS           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(1U)),      
    ENET_DMA_INT_FLAG_TBU           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(2U)),      
    ENET_DMA_INT_FLAG_TJT           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(3U)),      
    ENET_DMA_INT_FLAG_RO            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(4U)),      
    ENET_DMA_INT_FLAG_TU            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(5U)),      
    ENET_DMA_INT_FLAG_RS            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(6U)),      
    ENET_DMA_INT_FLAG_RBU           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(7U)),      
    ENET_DMA_INT_FLAG_RPS           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(8U)),      
    ENET_DMA_INT_FLAG_RWT           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(9U)),      
    ENET_DMA_INT_FLAG_ET            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(10U)),     
    ENET_DMA_INT_FLAG_FBE           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(13U)),     
    ENET_DMA_INT_FLAG_ER            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(14U)),     
    ENET_DMA_INT_FLAG_AI            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(15U)),     
    ENET_DMA_INT_FLAG_NI            = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(16U)),     
    ENET_DMA_INT_FLAG_MSC           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(27U)),     
    ENET_DMA_INT_FLAG_WUM           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(28U)),     
    ENET_DMA_INT_FLAG_TST           = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(29U))      
} enet_int_flag_enum;

 
typedef enum {
     
    ENET_DMA_INT_FLAG_TS_CLR        = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(0U)),      
    ENET_DMA_INT_FLAG_TPS_CLR       = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(1U)),      
    ENET_DMA_INT_FLAG_TBU_CLR       = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(2U)),      
    ENET_DMA_INT_FLAG_TJT_CLR       = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(3U)),      
    ENET_DMA_INT_FLAG_RO_CLR        = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(4U)),      
    ENET_DMA_INT_FLAG_TU_CLR        = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(5U)),      
    ENET_DMA_INT_FLAG_RS_CLR        = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(6U)),      
    ENET_DMA_INT_FLAG_RBU_CLR       = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(7U)),      
    ENET_DMA_INT_FLAG_RPS_CLR       = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(8U)),      
    ENET_DMA_INT_FLAG_RWT_CLR       = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(9U)),      
    ENET_DMA_INT_FLAG_ET_CLR        = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(10U)),     
    ENET_DMA_INT_FLAG_FBE_CLR       = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(13U)),     
    ENET_DMA_INT_FLAG_ER_CLR        = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(14U)),     
    ENET_DMA_INT_FLAG_AI_CLR        = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(15U)),     
    ENET_DMA_INT_FLAG_NI_CLR        = (((uint32_t)(0x00001014U) << 6U) | (uint32_t)(16U))      
} enet_int_flag_clear_enum;

 
typedef enum {
    ENET_RX_DESC_TABLE              = 0x0000100CU,                        
    ENET_RX_CURRENT_DESC            = 0x0000104CU,                        
    ENET_RX_CURRENT_BUFFER          = 0x00001054U,                        
    ENET_TX_DESC_TABLE              = 0x00001010U,                        
    ENET_TX_CURRENT_DESC            = 0x00001048U,                        
    ENET_TX_CURRENT_BUFFER          = 0x00001050U                         
} enet_desc_reg_enum;

 
typedef enum {
    ENET_MSC_TX_SCCNT               = 0x0000014CU,                          
    ENET_MSC_TX_MSCCNT              = 0x00000150U,                         
    ENET_MSC_TX_TGFCNT              = 0x00000168U,                         
    ENET_MSC_RX_RFCECNT             = 0x00000194U,                        
    ENET_MSC_RX_RFAECNT             = 0x00000198U,                        
    ENET_MSC_RX_RGUFCNT             = 0x000001C4U                         
} enet_msc_counter_enum;

 
typedef enum {
    FORWARD_OPTION                  = ((uint32_t)((uint32_t)0x01U << (0))),                                        
    DMABUS_OPTION                   = ((uint32_t)((uint32_t)0x01U << (1))),                                        
    DMA_MAXBURST_OPTION             = ((uint32_t)((uint32_t)0x01U << (2))),                                        
    DMA_ARBITRATION_OPTION          = ((uint32_t)((uint32_t)0x01U << (3))),                                        
    STORE_OPTION                    = ((uint32_t)((uint32_t)0x01U << (4))),                                        
    DMA_OPTION                      = ((uint32_t)((uint32_t)0x01U << (5))),                                        
    VLAN_OPTION                     = ((uint32_t)((uint32_t)0x01U << (6))),                                        
    FLOWCTL_OPTION                  = ((uint32_t)((uint32_t)0x01U << (7))),                                        
    HASHH_OPTION                    = ((uint32_t)((uint32_t)0x01U << (8))),                                        
    HASHL_OPTION                    = ((uint32_t)((uint32_t)0x01U << (9))),                                        
    FILTER_OPTION                   = ((uint32_t)((uint32_t)0x01U << (10))),                                       
    HALFDUPLEX_OPTION               = ((uint32_t)((uint32_t)0x01U << (11))),                                       
    TIMER_OPTION                    = ((uint32_t)((uint32_t)0x01U << (12))),                                       
    INTERFRAMEGAP_OPTION            = ((uint32_t)((uint32_t)0x01U << (13)))                                        
} enet_option_enum;

 
typedef enum {
    ENET_AUTO_NEGOTIATION           = 0x01U,                                         
    ENET_100M_FULLDUPLEX            = (((uint32_t)((uint32_t)0x01U << (14))) | ((uint32_t)((uint32_t)0x01U << (11)))),         
    ENET_100M_HALFDUPLEX            = ((uint32_t)((uint32_t)0x01U << (14))),                              
    ENET_10M_FULLDUPLEX             = ((uint32_t)((uint32_t)0x01U << (11))),                              
    ENET_10M_HALFDUPLEX             = (uint32_t)0x00000000U,                         
    ENET_LOOPBACKMODE               = (((uint32_t)((uint32_t)0x01U << (12))) | ((uint32_t)((uint32_t)0x01U << (11))))          
} enet_mediamode_enum;

 
typedef enum {
    ENET_NO_AUTOCHECKSUM                = (uint32_t)0x00000000U,                       
    ENET_AUTOCHECKSUM_DROP_FAILFRAMES   = ((uint32_t)((uint32_t)0x01U << (10))),                          
    ENET_AUTOCHECKSUM_ACCEPT_FAILFRAMES = (((uint32_t)((uint32_t)0x01U << (10))) | ((uint32_t)((uint32_t)0x01U << (26)))) 
 
} enet_chksumconf_enum;

 
typedef enum {
    ENET_PROMISCUOUS_MODE           = ((uint32_t)((uint32_t)0x01U << (0))),                              
    ENET_RECEIVEALL                 = (int32_t)((uint32_t)((uint32_t)0x01U << (31))),                    
    ENET_BROADCAST_FRAMES_PASS      = (uint32_t)0x00000000U,                         
    ENET_BROADCAST_FRAMES_DROP      = ((uint32_t)((uint32_t)0x01U << (5)))                            
} enet_frmrecept_enum;

 
typedef enum {
    ALL_MAC_REG                     = 0U,                                            
    ALL_MSC_REG                     = 22U,                                           
    ALL_PTP_REG                     = 33U,                                           
    ALL_DMA_REG                     = 44U                                            
} enet_registers_type_enum;

 
typedef enum {
    ENET_DMA_TX                     = ((0xFFFFFFFFUL << (20)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(22)))),                              
    ENET_DMA_RX                     = ((0xFFFFFFFFUL << (17)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(19))))                               
} enet_dmadirection_enum;

 
typedef enum {
    ENET_PHY_READ                   = (uint32_t)0x00000000U,                         
    ENET_PHY_WRITE                  = ((uint32_t)((uint32_t)0x01U << (1)))                            
} enet_phydirection_enum;

 
typedef enum {
    ENET_REG_READ,                                                                   
    ENET_REG_WRITE                                                                   
} enet_regdirection_enum;

 
typedef enum {
    ENET_MAC_ADDRESS0               = ((uint32_t)0x00000000U),                       
    ENET_MAC_ADDRESS1               = ((uint32_t)0x00000008U),                       
    ENET_MAC_ADDRESS2               = ((uint32_t)0x00000010U),                       
    ENET_MAC_ADDRESS3               = ((uint32_t)0x00000018U)                        
} enet_macaddress_enum;

 
typedef enum {
    TXDESC_COLLISION_COUNT,                                                          
    TXDESC_BUFFER_1_ADDR,                                                            
    RXDESC_FRAME_LENGTH,                                                             
    RXDESC_BUFFER_1_SIZE,                                                            
    RXDESC_BUFFER_2_SIZE,                                                            
    RXDESC_BUFFER_1_ADDR                                                             
} enet_descstate_enum;

 
typedef enum {
    ENET_MSC_PRESET_NONE            = 0U,                                            
    ENET_MSC_PRESET_HALF            = ((uint32_t)((uint32_t)0x01U << (4))),                              
    ENET_MSC_PRESET_FULL            = ((uint32_t)((uint32_t)0x01U << (4))) | ((uint32_t)((uint32_t)0x01U << (5)))          
} enet_msc_preset_enum;

 
typedef struct {
    uint32_t option_enable;                                                          
    uint32_t forward_frame;                                                          
    uint32_t dmabus_mode;                                                            
    uint32_t dma_maxburst;                                                           
    uint32_t dma_arbitration;                                                        
    uint32_t store_forward_mode;                                                     
    uint32_t dma_function;                                                           
    uint32_t vlan_config;                                                            
    uint32_t flow_control;                                                           
    uint32_t hashtable_high;                                                         
    uint32_t hashtable_low;                                                          
    uint32_t framesfilter_mode;                                                      
    uint32_t halfduplex_param;                                                       
    uint32_t timer_config;                                                           
    uint32_t interframegap;                                                          
} enet_initpara_struct;

 
typedef struct {
    uint32_t status;                                                                 
    uint32_t control_buffer_size;                                                    
    uint32_t buffer1_addr;                                                           
    uint32_t buffer2_next_desc_addr;                                                 


} enet_descriptors_struct;

 
typedef struct {
    uint32_t second;                                                                 
    uint32_t nanosecond;                                                             
    uint32_t sign;                                                                   
} enet_ptp_systime_struct;

 














 








 



 

 






 


 

 






 

 



 


 

 


 

 



 


 

 









 



 









 

 

 


 


 

 



 


 



typedef enum {
    ENET_CKNT_ORDINARY                = (((0xFFFFFFFFUL << (16)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(17)))) & ((uint32_t)(0) << 16U)),                                       
    ENET_CKNT_BOUNDARY                = (((0xFFFFFFFFUL << (16)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(17)))) & ((uint32_t)(1) << 16U)),                                       
    ENET_CKNT_END_TO_END              = (((0xFFFFFFFFUL << (16)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(17)))) & ((uint32_t)(2) << 16U)),                                       
    ENET_CKNT_PEER_TO_PEER            = (((0xFFFFFFFFUL << (16)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(17)))) & ((uint32_t)(3) << 16U)),                                       
    ENET_PTP_SYSTIME_INIT             = ((uint32_t)((uint32_t)0x01U << (2))),                                   
    ENET_PTP_SYSTIME_UPDATE           = ((uint32_t)((uint32_t)0x01U << (3))),                                   
    ENET_PTP_ADDEND_UPDATE            = ((uint32_t)((uint32_t)0x01U << (5))),                                   
    ENET_PTP_FINEMODE                 = (int32_t)(((uint32_t)((uint32_t)0x01U << (1))) | ((uint32_t)((uint32_t)0x01U << (31)))),              
    ENET_PTP_COARSEMODE               = ((uint32_t)((uint32_t)0x01U << (1))),                                   
    ENET_SUBSECOND_DIGITAL_ROLLOVER   = (int32_t)(((uint32_t)((uint32_t)0x01U << (9))) | ((uint32_t)((uint32_t)0x01U << (31)))),               
    ENET_SUBSECOND_BINARY_ROLLOVER    = ((uint32_t)((uint32_t)0x01U << (9))),                                    
    ENET_SNOOPING_PTP_VERSION_2       = (int32_t)(((uint32_t)((uint32_t)0x01U << (10))) | ((uint32_t)((uint32_t)0x01U << (31)))),                
    ENET_SNOOPING_PTP_VERSION_1       = ((uint32_t)((uint32_t)0x01U << (10))),                                     
    ENET_EVENT_TYPE_MESSAGES_SNAPSHOT = (int32_t)(((uint32_t)((uint32_t)0x01U << (14))) | ((uint32_t)((uint32_t)0x01U << (31)))),              
    ENET_ALL_TYPE_MESSAGES_SNAPSHOT   = ((uint32_t)((uint32_t)0x01U << (14))),                                   
    ENET_MASTER_NODE_MESSAGE_SNAPSHOT = (int32_t)(((uint32_t)((uint32_t)0x01U << (15))) | ((uint32_t)((uint32_t)0x01U << (31)))),              
    ENET_SLAVE_NODE_MESSAGE_SNAPSHOT  = ((uint32_t)((uint32_t)0x01U << (15)))                                    
} enet_ptp_function_enum;


 

 

 






 

 



 

 

 

 

 



 
 
 
void enet_deinit(uint32_t enet_periph);
 
void enet_initpara_config(enet_option_enum option, uint32_t para);
 
ErrStatus enet_init(uint32_t enet_periph, enet_mediamode_enum mediamode, enet_chksumconf_enum checksum, enet_frmrecept_enum recept);
 
ErrStatus enet_software_reset(uint32_t enet_periph);
 
uint32_t enet_rxframe_size_get(uint32_t enet_periph);
 
void enet_descriptors_chain_init(uint32_t enet_periph, enet_dmadirection_enum direction);
 
void enet_descriptors_ring_init(uint32_t enet_periph, enet_dmadirection_enum direction);
 
ErrStatus enet_frame_receive(uint32_t enet_periph, uint8_t buffer[], uint32_t bufsize);
 
 
ErrStatus enet_frame_transmit(uint32_t enet_periph, uint8_t buffer[], uint32_t length);
 
 
ErrStatus enet_transmit_checksum_config(enet_descriptors_struct *desc, uint32_t checksum);
 
void enet_enable(uint32_t enet_periph);
 
void enet_disable(uint32_t enet_periph);
 
void enet_mac_address_set(uint32_t enet_periph, enet_macaddress_enum mac_addr, uint8_t paddr[]);
 
ErrStatus enet_mac_address_get(uint32_t enet_periph, enet_macaddress_enum mac_addr, uint8_t paddr[], uint8_t bufsize);

 
 
void enet_tx_enable(uint32_t enet_periph);
 
void enet_tx_disable(uint32_t enet_periph);
 
void enet_rx_enable(uint32_t enet_periph);
 
void enet_rx_disable(uint32_t enet_periph);
 
void enet_registers_get(uint32_t enet_periph, enet_registers_type_enum type, uint32_t *preg, uint32_t num);
 
uint32_t enet_debug_status_get(uint32_t enet_periph, uint32_t mac_debug);
 
void enet_address_filter_enable(uint32_t enet_periph, enet_macaddress_enum mac_addr);
 
void enet_address_filter_disable(uint32_t enet_periph, enet_macaddress_enum mac_addr);
 
void enet_address_filter_config(uint32_t enet_periph, enet_macaddress_enum mac_addr, uint32_t addr_mask, uint32_t filter_type);
 
ErrStatus enet_phy_config(uint32_t enet_periph);
 
ErrStatus enet_phy_write_read(uint32_t enet_periph, enet_phydirection_enum direction, uint16_t phy_address, uint16_t phy_reg, uint16_t *pvalue);
 
ErrStatus enet_phyloopback_enable(uint32_t enet_periph);
 
ErrStatus enet_phyloopback_disable(uint32_t enet_periph);
 
void enet_forward_feature_enable(uint32_t enet_periph, uint32_t feature);
 
void enet_forward_feature_disable(uint32_t enet_periph, uint32_t feature);
 
void enet_fliter_feature_enable(uint32_t enet_periph, uint32_t feature);
 
void enet_fliter_feature_disable(uint32_t enet_periph, uint32_t feature);

 
 
ErrStatus enet_pauseframe_generate(uint32_t enet_periph);
 
void enet_pauseframe_detect_config(uint32_t enet_periph, uint32_t detect);
 
void enet_pauseframe_config(uint32_t enet_periph, uint32_t pausetime, uint32_t pause_threshold);
 
void enet_flowcontrol_threshold_config(uint32_t enet_periph, uint32_t deactive, uint32_t active);
 
void enet_flowcontrol_feature_enable(uint32_t enet_periph, uint32_t feature);
 
void enet_flowcontrol_feature_disable(uint32_t enet_periph, uint32_t feature);

 
 
uint32_t enet_dmaprocess_state_get(uint32_t enet_periph, enet_dmadirection_enum direction);
 
void enet_dmaprocess_resume(uint32_t enet_periph, enet_dmadirection_enum direction);
 
void enet_rxprocess_check_recovery(uint32_t enet_periph);
 
ErrStatus enet_txfifo_flush(uint32_t enet_periph);
 
uint32_t enet_current_desc_address_get(uint32_t enet_periph, enet_desc_reg_enum addr_get);
 
uint32_t enet_desc_information_get(uint32_t enet_periph, enet_descriptors_struct *desc, enet_descstate_enum info_get);
 
void enet_missed_frame_counter_get(uint32_t enet_periph, uint32_t *rxfifo_drop, uint32_t *rxdma_drop);

 
 
FlagStatus enet_desc_flag_get(enet_descriptors_struct *desc, uint32_t desc_flag);
 
void enet_desc_flag_set(enet_descriptors_struct *desc, uint32_t desc_flag);
 
void enet_desc_flag_clear(enet_descriptors_struct *desc, uint32_t desc_flag);
 
void enet_rx_desc_immediate_receive_complete_interrupt(enet_descriptors_struct *desc);
 
void enet_rx_desc_delay_receive_complete_interrupt(uint32_t enet_periph, enet_descriptors_struct *desc, uint32_t delay_time);
 
void enet_rxframe_drop(uint32_t enet_periph);
 
void enet_dma_feature_enable(uint32_t enet_periph, uint32_t feature);
 
void enet_dma_feature_disable(uint32_t enet_periph, uint32_t feature);


 

 
void enet_desc_select_normal_mode(uint32_t enet_periph);
 
void enet_ptp_normal_descriptors_chain_init(uint32_t enet_periph, enet_dmadirection_enum direction, enet_descriptors_struct *desc_ptptab);
 
void enet_ptp_normal_descriptors_ring_init(uint32_t enet_periph, enet_dmadirection_enum direction, enet_descriptors_struct *desc_ptptab);
 
ErrStatus enet_ptpframe_receive_normal_mode(uint32_t enet_periph, uint8_t buffer[], uint32_t bufsize, uint32_t timestamp[]);
 
 
ErrStatus enet_ptpframe_transmit_normal_mode(uint32_t enet_periph, uint8_t buffer[], uint32_t length, uint32_t timestamp[]);
 


 
 
void enet_wum_filter_register_pointer_reset(uint32_t enet_periph);
 
void enet_wum_filter_config(uint32_t enet_periph, uint32_t pdata[]);
 
void enet_wum_feature_enable(uint32_t enet_periph, uint32_t feature);
 
void enet_wum_feature_disable(uint32_t enet_periph, uint32_t feature);

 
 
void enet_msc_counters_reset(uint32_t enet_periph);
 
void enet_msc_feature_enable(uint32_t enet_periph, uint32_t feature);
 
void enet_msc_feature_disable(uint32_t enet_periph, uint32_t feature);
 
void enet_msc_counters_preset_config(uint32_t enet_periph, enet_msc_preset_enum mode);
 
uint32_t enet_msc_counters_get(uint32_t enet_periph, enet_msc_counter_enum counter);

 
 
uint32_t enet_ptp_subsecond_2_nanosecond(uint32_t subsecond);
 
uint32_t enet_ptp_nanosecond_2_subsecond(uint32_t nanosecond);
 
void enet_ptp_feature_enable(uint32_t enet_periph, uint32_t feature);
 
void enet_ptp_feature_disable(uint32_t enet_periph, uint32_t feature);
 
ErrStatus enet_ptp_timestamp_function_config(uint32_t enet_periph, enet_ptp_function_enum func);
 
void enet_ptp_subsecond_increment_config(uint32_t enet_periph, uint32_t subsecond);
 
void enet_ptp_timestamp_addend_config(uint32_t enet_periph, uint32_t add);
 
void enet_ptp_timestamp_update_config(uint32_t enet_periph, uint32_t sign, uint32_t second, uint32_t subsecond);
 
void enet_ptp_expected_time_config(uint32_t enet_periph, uint32_t second, uint32_t nanosecond);
 
void enet_ptp_system_time_get(uint32_t enet_periph, enet_ptp_systime_struct *systime_struct);
 
void enet_ptp_pps_output_frequency_config(uint32_t enet_periph, uint32_t freq);
 
void enet_ptp_start(uint32_t enet_periph, int32_t updatemethod, uint32_t init_sec, uint32_t init_subsec, uint32_t carry_cfg, uint32_t accuracy_cfg);
 
void enet_ptp_finecorrection_adjfreq(uint32_t enet_periph, int32_t carry_cfg);
 
void enet_ptp_coarsecorrection_systime_update(uint32_t enet_periph, enet_ptp_systime_struct *systime_struct);
 
void enet_ptp_finecorrection_settime(uint32_t enet_periph, enet_ptp_systime_struct *systime_struct);
 
FlagStatus enet_ptp_flag_get(uint32_t enet_periph, uint32_t flag);

 
 
void enet_initpara_reset(void);
 

 
FlagStatus enet_flag_get(uint32_t enet_periph, enet_flag_enum enet_flag);
 
void enet_flag_clear(uint32_t enet_periph, enet_flag_clear_enum enet_flag);
 
void enet_interrupt_enable(uint32_t enet_periph, enet_int_enum enet_int);
 
void enet_interrupt_disable(uint32_t enet_periph, enet_int_enum enet_int);
 
FlagStatus enet_interrupt_flag_get(uint32_t enet_periph, enet_int_flag_enum int_flag);
 
void enet_interrupt_flag_clear(uint32_t enet_periph, enet_int_flag_clear_enum int_flag_clear);






 


























 



 

 
 

 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
typedef struct {
    uint32_t asyn_access_mode;                                             
    uint32_t syn_data_latency;                                             
    uint32_t syn_clk_division;                                             
    uint32_t bus_latency;                                                  
    uint32_t asyn_data_setuptime;                                          
    uint32_t asyn_address_holdtime;                                        
    uint32_t asyn_address_setuptime;                                       
} exmc_norsram_timing_parameter_struct;

 
typedef struct {
    uint32_t norsram_region;                                               
    uint32_t address_data_mux;                                             
    uint32_t memory_type;                                                  
    uint32_t databus_width;                                                
    uint32_t burst_mode;                                                   
    uint32_t nwait_polarity;                                               
    uint32_t nwait_config;                                                 
    uint32_t memory_write;                                                 
    uint32_t nwait_signal;                                                 
    uint32_t extended_mode;                                                
    uint32_t asyn_wait;                                                    
    uint32_t cram_page_size;                                               
    uint32_t write_mode;                                                   
    exmc_norsram_timing_parameter_struct
    *read_write_timing;              
 
    exmc_norsram_timing_parameter_struct *write_timing;                    
} exmc_norsram_parameter_struct;

 
typedef struct {
    uint32_t databus_hiztime;                                              
    uint32_t holdtime;                                                     
    uint32_t waittime;                                                     
    uint32_t setuptime;                                                    
} exmc_nand_timing_parameter_struct;

 
typedef struct {
    uint32_t ecc_size;                                                     
    uint32_t atr_latency;                                                  
    uint32_t ctr_latency;                                                  
    uint32_t ecc_logic;                                                    
    uint32_t databus_width;                                                
    uint32_t wait_feature;                                                 
    exmc_nand_timing_parameter_struct *common_space_timing;                
    exmc_nand_timing_parameter_struct *attribute_space_timing;             
} exmc_nand_parameter_struct;

 
typedef struct {
    uint32_t row_to_column_delay;                                          
    uint32_t row_precharge_delay;                                          
    uint32_t write_recovery_delay;                                         
    uint32_t auto_refresh_delay;                                           
    uint32_t row_address_select_delay;                                     
    uint32_t exit_selfrefresh_delay;                                       
    uint32_t load_mode_register_delay;                                     
} exmc_sdram_timing_parameter_struct;

 
typedef struct {
    uint32_t sdram_device;                                                 
    uint32_t pipeline_read_delay;                                          
    uint32_t burst_read_switch;                                            
    uint32_t sdclock_config;                                               
    uint32_t write_protection;                                             
    uint32_t cas_latency;                                                  
    uint32_t internal_bank_number;                                         
    uint32_t data_width;                                                   
    uint32_t row_address_width;                                            
    uint32_t column_address_width;                                         
    exmc_sdram_timing_parameter_struct *timing;                            
} exmc_sdram_parameter_struct;

 
typedef struct {
    uint32_t mode_register_content;                                        
    uint32_t auto_refresh_number;                                          
    uint32_t bank_select;                                                  
    uint32_t command;                                                      
} exmc_sdram_command_parameter_struct;

 


 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
 
 
void exmc_norsram_deinit(uint32_t exmc_norsram_region);
 
void exmc_norsram_struct_para_init(exmc_norsram_parameter_struct *exmc_norsram_init_struct);
 
void exmc_norsram_init(exmc_norsram_parameter_struct *exmc_norsram_init_struct);
 
void exmc_norsram_enable(uint32_t exmc_norsram_region);
 
void exmc_norsram_disable(uint32_t exmc_norsram_region);
 
 
void exmc_nand_deinit(void);
 
void exmc_nand_struct_para_init(exmc_nand_parameter_struct *exmc_nand_init_struct);
 
void exmc_nand_init(exmc_nand_parameter_struct *exmc_nand_init_struct);
 
void exmc_nand_enable(void);
 
void exmc_nand_disable(void);
 
 
void exmc_sdram_deinit(uint32_t exmc_sdram_device);
 
void exmc_sdram_struct_para_init(exmc_sdram_parameter_struct *exmc_sdram_init_struct);
 
void exmc_sdram_init(exmc_sdram_parameter_struct *exmc_sdram_init_struct);

 
 
 
void exmc_norsram_sdram_remap_config(uint32_t bank_remap);
 
uint32_t exmc_norsram_sdram_remap_get(void);
 
void exmc_norsram_consecutive_clock_config(uint32_t clock_mode);
 
void exmc_norsram_page_size_config(uint32_t exmc_norsram_region, uint32_t page_size);
 
 
void exmc_nand_ecc_config(ControlStatus newvalue);
 
uint32_t exmc_ecc_get(void);
 
 
void exmc_sdram_readsample_enable(void);
 
void exmc_sdram_readsample_disable(void);
 
void exmc_sdram_readsample_config(uint32_t delay_cell, uint32_t extra_clk);
 
void exmc_sdram_command_config(exmc_sdram_command_parameter_struct *exmc_sdram_command_init_struct);
 
void exmc_sdram_refresh_count_set(uint32_t exmc_count);
 
void exmc_sdram_autorefresh_number_set(uint32_t exmc_number);
 
void exmc_sdram_write_protection_config(uint32_t exmc_sdram_device, ControlStatus newvalue);
 
uint32_t exmc_sdram_bankstatus_get(uint32_t exmc_sdram_device);

 
 
FlagStatus exmc_flag_get(uint32_t exmc_bank, uint32_t flag);
 
void exmc_flag_clear(uint32_t exmc_bank, uint32_t flag);
 
void exmc_interrupt_enable(uint32_t exmc_bank, uint32_t interrupt);
 
void exmc_interrupt_disable(uint32_t exmc_bank, uint32_t interrupt);
 
FlagStatus exmc_interrupt_flag_get(uint32_t exmc_bank, uint32_t interrupt);
 
void exmc_interrupt_flag_clear(uint32_t exmc_bank, uint32_t interrupt);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 

 
typedef enum {
    EXTI_0      = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(0U)),     
    EXTI_1      = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(1U)),     
    EXTI_2      = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(2U)),     
    EXTI_3      = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(3U)),     
    EXTI_4      = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(4U)),     
    EXTI_5      = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(5U)),     
    EXTI_6      = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(6U)),     
    EXTI_7      = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(7U)),     
    EXTI_8      = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(8U)),     
    EXTI_9      = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(9U)),     
    EXTI_10     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(10U)),    
    EXTI_11     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(11U)),    
    EXTI_12     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(12U)),    
    EXTI_13     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(13U)),    
    EXTI_14     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(14U)),    
    EXTI_15     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(15U)),    
    EXTI_16     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(16U)),    
    EXTI_17     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(17U)),    
    EXTI_18     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(18U)),    
    EXTI_19     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(19U)),    
    EXTI_20     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(20U)),    
    EXTI_21     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(21U)),    
    EXTI_22     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(22U)),    
    EXTI_23     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(23U)),    
    EXTI_24     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(24U)),    
    EXTI_25     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(25U)),    
    EXTI_26     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(26U)),    
    EXTI_27     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(27U)),    
    EXTI_28     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(28U)),    
    EXTI_29     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(29U)),    
    EXTI_30     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(30U)),    
    EXTI_31     = (((uint32_t)(0x00000000U) << 8U) | (uint32_t)(31U)),    
    EXTI_32     = (((uint32_t)(0x00000018U) << 8U) | (uint32_t)(32U)),    
    EXTI_33     = (((uint32_t)(0x00000018U) << 8U) | (uint32_t)(33U)),    
    EXTI_34     = (((uint32_t)(0x00000018U) << 8U) | (uint32_t)(34U)),    
    EXTI_35     = (((uint32_t)(0x00000018U) << 8U) | (uint32_t)(35U)),    
    EXTI_36     = (((uint32_t)(0x00000018U) << 8U) | (uint32_t)(36U)),    
    EXTI_37     = (((uint32_t)(0x00000018U) << 8U) | (uint32_t)(37U))     
} exti_line_enum;

 
typedef enum {
    EXTI_INTERRUPT = 0,                                          
    EXTI_EVENT                                                   
} exti_mode_enum;

 
typedef enum {
    EXTI_TRIG_RISING = 0,                                        
    EXTI_TRIG_FALLING,                                           
    EXTI_TRIG_BOTH,                                              
    EXTI_TRIG_NONE                                               
} exti_trig_type_enum;

 
 
 
void exti_deinit(void);
 
void exti_init(exti_line_enum linex, exti_mode_enum mode, exti_trig_type_enum trig_type);
 
void exti_interrupt_enable(exti_line_enum linex);
 
void exti_interrupt_disable(exti_line_enum linex);
 
void exti_event_enable(exti_line_enum linex);
 
void exti_event_disable(exti_line_enum linex);
 
void exti_software_interrupt_enable(exti_line_enum linex);
 
void exti_software_interrupt_disable(exti_line_enum linex);

 
 
FlagStatus exti_flag_get(exti_line_enum linex);
 
void exti_flag_clear(exti_line_enum linex);
 
FlagStatus exti_interrupt_flag_get(exti_line_enum linex);
 
void exti_interrupt_flag_clear(exti_line_enum linex);






 


























 



 

    
 



 
 

 

 

 

 

 

 

 

 
 
typedef struct
{
    uint8_t input_addr;                                                   
    uint8_t input_size;                                                   
    uint8_t coeff_addr;                                                   
    uint8_t coeff_size;                                                   
    uint8_t output_addr;                                                  
    uint8_t output_size;                                                  
    uint8_t ipp;                                                          
    uint8_t ipq;                                                          
    uint8_t ipr;                                                          
    uint32_t input_threshold;                                             
    uint32_t output_threshold;                                            
    uint32_t clip;                                                        
    uint32_t func;                                                        
}fac_parameter_struct;

 
typedef struct
{
    uint8_t coeffa_size;                                                  
    int16_t *coeffa_ctx;                                                  
    uint8_t coeffb_size;                                                  
    int16_t *coeffb_ctx;                                                  
    uint8_t input_size;                                                   
    int16_t *input_ctx;                                                   
    uint8_t output_size;                                                  
    int16_t *output_ctx;                                                  
}fac_fixed_data_preload_struct;

 
typedef struct
{
    uint8_t coeffa_size;                                                  
    float *coeffa_ctx;                                                    
    uint8_t coeffb_size;                                                  
    float *coeffb_ctx;                                                    
    uint8_t input_size;                                                   
    float *input_ctx;                                                     
    uint8_t  output_size;                                                 
    float *output_ctx;                                                    
}fac_float_data_preload_struct;

 

 
                                                                  
                                               
 
 

 

 

   

 
 
 
void fac_deinit(void);
 
void fac_struct_para_init(fac_parameter_struct* fac_parameter);
 
void fac_fixed_data_preload_init(fac_fixed_data_preload_struct *init_struct);
 
void fac_float_data_preload_init(fac_float_data_preload_struct *init_struct);
 
void fac_init(fac_parameter_struct* fac_parameter);
 
void fac_fixed_buffer_preload(fac_fixed_data_preload_struct* init_struct);
 
void fac_float_buffer_preload(fac_float_data_preload_struct* init_struct);
 
void fac_fixed_data_preload(uint8_t size, int16_t array[]);
 
void fac_float_data_preload(uint8_t size, float array[]);
 
 
void fac_reset(void);
 
void fac_clip_config(uint8_t cpmod);
 
void fac_float_enable(void);
 
void fac_float_disable(void);
 
void fac_dma_enable(uint32_t dma_req);
 
void fac_dma_disable(uint32_t dma_req);
 
void fac_x0_config(uint32_t watermark, uint8_t baseaddr, uint8_t bufsize);
 
void fac_x1_config(uint8_t baseaddr, uint8_t bufsize);
 
void fac_y_config(uint32_t watermark, uint8_t baseaddr, uint8_t bufsize);
 
void fac_function_config(fac_parameter_struct* fac_parameter);
 
void fac_start(void);
 
void fac_stop(void);
 
void fac_finish_calculate(void);

 
 
void fac_fixed_data_write(int16_t data);
 
int16_t fac_fixed_data_read(void);
 
void fac_float_data_write(float data);
 
float fac_float_data_read(void);

 
 
void fac_interrupt_enable(uint32_t interrupt);
 
void fac_interrupt_disable(uint32_t interrupt);
 
FlagStatus fac_interrupt_flag_get(uint8_t interrupt);
 
FlagStatus fac_flag_get(uint32_t flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
typedef enum {
    FMC_READY = 0U,                                                         
    FMC_BUSY,                                                               
    FMC_WPERR,                                                              
    FMC_PGSERR,                                                             
    FMC_RPERR,                                                              
    FMC_RSERR,                                                              
    FMC_ECCCOR,                                                             
    FMC_ECCDET,                                                             
    FMC_OBMERR,                                                             
    FMC_TOERR                                                               
} fmc_state_enum;

 

 

 
typedef enum {
     
    FMC_FLAG_BUSY = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(0U)),                
    FMC_FLAG_END = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(16U)),                
    FMC_FLAG_WPERR = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(17U)),              
    FMC_FLAG_PGSERR = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(18U)),             
    FMC_FLAG_RPERR = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(23U)),              
    FMC_FLAG_RSERR = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(24U)),              
    FMC_FLAG_ECCCOR = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(25U)),             
    FMC_FLAG_ECCDET = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(26U)),             
    FMC_FLAG_OBMERR = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(30U)),             
    FMC_FLAG_FECC = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(31U))                
} fmc_flag_enum;

 
typedef enum {
     
    FMC_INT_FLAG_END = (((uint32_t)(((uint32_t)0x00000010U)) << 22) | (uint32_t)((16U) << 16) | (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(16U))),                  
    FMC_INT_FLAG_WPERR = (((uint32_t)(((uint32_t)0x00000010U)) << 22) | (uint32_t)((17U) << 16) | (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(17U))),                
    FMC_INT_FLAG_PGSERR = (((uint32_t)(((uint32_t)0x00000010U)) << 22) | (uint32_t)((18U) << 16) | (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(18U))),               
    FMC_INT_FLAG_RPERR = (((uint32_t)(((uint32_t)0x00000010U)) << 22) | (uint32_t)((23U) << 16) | (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(23U))),                
    FMC_INT_FLAG_RSERR = (((uint32_t)(((uint32_t)0x00000010U)) << 22) | (uint32_t)((24U) << 16) | (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(24U))),                
    FMC_INT_FLAG_ECCCOR = (((uint32_t)(((uint32_t)0x00000010U)) << 22) | (uint32_t)((25U) << 16) | (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(25U))),               
    FMC_INT_FLAG_ECCDET = (((uint32_t)(((uint32_t)0x00000010U)) << 22) | (uint32_t)((26U) << 16) | (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(26U))),               
    FMC_INT_FLAG_OBMERR = (((uint32_t)(((uint32_t)0x00000010U)) << 22) | (uint32_t)((30U) << 16) | (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(30U)))              
} fmc_interrupt_flag_enum;

 
typedef enum {
     
    FMC_INT_END = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(16U)),                  
    FMC_INT_WPERR = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(17U)),                
    FMC_INT_PGSERR = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(18U)),               
    FMC_INT_RPERR = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(23U)),                
    FMC_INT_RSERR = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(24U)),                
    FMC_INT_ECCCOR = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(25U)),               
    FMC_INT_ECCDET = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(26U)),               
     
    FMC_INT_OBMERR = (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(30U))              
} fmc_interrupt_enum;

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 



 
 
 
void fmc_unlock(void);
 
void fmc_lock(void);
 
fmc_state_enum fmc_sector_erase(uint32_t address);
 
fmc_state_enum fmc_typical_mass_erase(void);
 
fmc_state_enum fmc_protection_removed_mass_erase(void);
 
fmc_state_enum fmc_word_program(uint32_t address, uint32_t data);
 
fmc_state_enum fmc_doubleword_program(uint32_t address, uint64_t data);
 
fmc_state_enum fmc_check_programming_area_enable(void);
 
fmc_state_enum fmc_check_programming_area_disable(void);

 
 
void ob_unlock(void);
 
void ob_lock(void);
 
fmc_state_enum ob_start(void);
 
fmc_state_enum ob_factory_value_config(void);
 
fmc_state_enum ob_secure_access_mode_enable(void);
 
fmc_state_enum ob_secure_access_mode_disable(void);
 
fmc_state_enum ob_security_protection_config(uint8_t ob_spc);
 
fmc_state_enum ob_bor_threshold_config(uint32_t ob_bor_th);
 
fmc_state_enum ob_low_power_config(uint32_t ob_fwdgt, uint32_t ob_deepsleep, uint32_t ob_stdby, uint32_t ob_fwdg_suspend_deepsleep,
                                   uint32_t ob_fwdg_suspend_standby);
 
fmc_state_enum ob_tcm_ecc_config(uint32_t ob_itcmecc, uint32_t ob_dtcm0ecc, uint32_t ob_dtcm1ecc);
 
fmc_state_enum ob_iospeed_optimize_config(uint32_t ob_iospeed_op);
 
fmc_state_enum ob_tcm_shared_ram_config(uint32_t itcm_shared_ram_size, uint32_t dtcm_shared_ram_size);
 
fmc_state_enum ob_data_program(uint16_t ob_data);
 
fmc_state_enum ob_boot_address_config(uint8_t boot_pin, uint16_t boot_address);
 
fmc_state_enum ob_dcrp_area_config(uint32_t dcrp_eren, uint32_t dcrp_start, uint32_t dcrp_end);
 
fmc_state_enum ob_secure_area_config(uint32_t scr_eren, uint32_t scr_start, uint32_t scr_end);
 
fmc_state_enum ob_write_protection_enable(uint32_t ob_wp);
 
fmc_state_enum ob_write_protection_disable(uint32_t ob_wp);
 
FlagStatus ob_secure_mode_get(void);
 
FlagStatus ob_security_protection_flag_get(void);
 
uint32_t ob_bor_threshold_get(void);
 
void ob_low_power_get(uint32_t *fwdgt, uint32_t *deepsleep, uint32_t *standby, uint32_t *fwdg_suspend_deepsleep, uint32_t *fwdg_suspend_standby);
 
void ob_tcm_ecc_get(uint32_t *itcmecc_option, uint32_t *dtcm0ecc_option, uint32_t *dtcm1ecc_option);
 
FlagStatus ob_iospeed_optimize_get(void);
 
void ob_tcm_shared_ram_size_get(uint32_t *itcm_shared_ram_kb_size, uint32_t *dtcm_shared_ram_kb_size);
 
uint16_t ob_data_get(void);
 
uint32_t ob_boot_address_get(uint8_t boot_pin);
 
uint8_t ob_dcrp_area_get(uint32_t *dcrp_erase_option, uint32_t *dcrp_area_start_addr, uint32_t *dcrp_area_end_addr);
 
uint8_t ob_secure_area_get(uint32_t *secure_area_option, uint32_t *scr_area_start_addr, uint32_t *scr_area_end_addr);
 
uint32_t ob_write_protection_get(void);

 
 
fmc_state_enum fmc_no_rtdec_config(uint32_t nodec_area_start, uint32_t nodec_area_end);
 
fmc_state_enum fmc_aes_iv_config(uint32_t *aes_iv);
 
FlagStatus fmc_flash_ecc_get(void);
 
void fmc_no_rtdec_get(uint32_t *nodec_area_start, uint32_t *nodec_area_end);
 
void fmc_aes_iv_get(uint32_t *aes_iv);
 
void fmc_pid_get(uint32_t *pid);

 
 
FlagStatus fmc_flag_get(fmc_flag_enum flag);
 
void fmc_flag_clear(fmc_flag_enum flag);
 
void fmc_interrupt_enable(fmc_interrupt_enum interrupt);
 
void fmc_interrupt_disable(fmc_interrupt_enum interrupt);
 
FlagStatus fmc_interrupt_flag_get(fmc_interrupt_flag_enum int_flag);
 
void fmc_interrupt_flag_clear(fmc_interrupt_flag_enum int_flag);






 


























 



 

 

 
 

 

 

 

 

 
 

 

 

 

 
 
void fwdgt_write_enable(void);
 
void fwdgt_write_disable(void);
 
void fwdgt_enable(void);

 
ErrStatus fwdgt_prescaler_value_config(uint16_t prescaler_value);
 
ErrStatus fwdgt_reload_value_config(uint16_t reload_value);
 
ErrStatus fwdgt_window_value_config(uint16_t window_value);
 
void fwdgt_counter_reload(void);
 
ErrStatus fwdgt_config(uint16_t reload_value, uint8_t prescaler_div);

 
FlagStatus fwdgt_flag_get(uint16_t flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 
                                                                   
                                                   

 

 

 

 

 
typedef FlagStatus bit_status;

 

 

 

 

 

 

 

 

 

 

 

 
 
 

 
 
void gpio_deinit(uint32_t gpio_periph);
 
void gpio_mode_set(uint32_t gpio_periph, uint32_t mode, uint32_t pull_up_down, uint32_t pin);
 
void gpio_output_options_set(uint32_t gpio_periph, uint8_t otype, uint32_t speed, uint32_t pin);

 
void gpio_bit_set(uint32_t gpio_periph, uint32_t pin);
 
void gpio_bit_reset(uint32_t gpio_periph, uint32_t pin);
 
void gpio_bit_write(uint32_t gpio_periph, uint32_t pin, bit_status bit_value);
 
void gpio_port_write(uint32_t gpio_periph, uint16_t data);

 
void gpio_input_filter_set(uint32_t gpio_periph, uint8_t speriod, uint32_t iftype, uint32_t pin);
 
FlagStatus gpio_input_bit_get(uint32_t gpio_periph, uint32_t pin);
 
uint16_t gpio_input_port_get(uint32_t gpio_periph);
 
FlagStatus gpio_output_bit_get(uint32_t gpio_periph, uint32_t pin);
 
uint16_t gpio_output_port_get(uint32_t gpio_periph);

 
void gpio_af_set(uint32_t gpio_periph, uint32_t alt_func_num, uint32_t pin);
 
void gpio_pin_lock(uint32_t gpio_periph, uint32_t pin);

 
void gpio_bit_toggle(uint32_t gpio_periph, uint32_t pin);
 
void gpio_port_toggle(uint32_t gpio_periph);






 


























 



 

 

 
 

 

 

 

 

 

 
  
typedef struct
{
    uint32_t algo;                                                                        
    uint32_t mode;                                                                        
    uint32_t datatype;                                                                    
    uint32_t keytype;                                                                     
}hau_init_parameter_struct;

 
typedef struct
{
    uint32_t out[8];                                                                      
}hau_digest_parameter_struct;

 
typedef struct
{
    uint32_t hau_ctl_bak;                                                                 
    uint32_t hau_cfg_bak;                                                                 
    uint32_t hau_inten_bak;                                                               
    uint32_t hau_ctxs_bak[54];                                                            
}hau_context_parameter_struct;

  






 

 



 
 
 
void hau_deinit(void);
 
void hau_init(hau_init_parameter_struct* initpara);
 
void hau_init_struct_para_init(hau_init_parameter_struct* initpara);
 
void hau_reset(void);
 
void hau_last_word_validbits_num_config(uint32_t valid_num);
 
void hau_data_write(uint32_t data);
 
uint32_t hau_infifo_words_num_get(void);
 
void hau_digest_read(hau_digest_parameter_struct* digestpara);
 
void hau_digest_calculation_enable(void);
 
void hau_multiple_single_dma_config(uint32_t multi_single);
 
void hau_dma_enable(void);
 
void hau_dma_disable(void);

 
 
void hau_context_struct_para_init(hau_context_parameter_struct* context);
 
void hau_context_save(hau_context_parameter_struct* context_save);
 
void hau_context_restore(hau_context_parameter_struct* context_restore);

 
 
ErrStatus hau_hash_sha_1(uint8_t input[], uint32_t in_length, uint8_t output[]);
 
ErrStatus hau_hmac_sha_1(uint8_t key[], uint32_t keysize, uint8_t input[], uint32_t in_length, uint8_t output[]);
 
ErrStatus hau_hash_sha_224(uint8_t input[], uint32_t in_length, uint8_t output[]);
 
ErrStatus hau_hmac_sha_224(uint8_t key[], uint32_t keysize, uint8_t input[], uint32_t in_length, uint8_t output[]);
 
ErrStatus hau_hash_sha_256(uint8_t input[], uint32_t in_length, uint8_t output[]);
 
ErrStatus hau_hmac_sha_256(uint8_t key[], uint32_t keysize, uint8_t input[], uint32_t in_length, uint8_t output[]);
 
ErrStatus hau_hash_md5(uint8_t input[], uint32_t in_length, uint8_t output[]);
 
ErrStatus hau_hmac_md5(uint8_t key[], uint32_t keysize, uint8_t input[], uint32_t in_length, uint8_t output[]);

 
 
FlagStatus hau_flag_get(uint32_t flag);
 
void hau_flag_clear(uint32_t flag);
 
void hau_interrupt_enable(uint32_t interrupt);
 
void hau_interrupt_disable(uint32_t interrupt);
 
FlagStatus hau_interrupt_flag_get(uint32_t int_flag);
 
void hau_interrupt_flag_clear(uint32_t int_flag);






 


























 



 

 

 
 
 

 

 

 

 

 

 

 
 

 
 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 
 
 
typedef struct {
    uint32_t data_packing_mode;                  
    uint32_t channel_multiplexer;                
    uint32_t channel_pin_select;                 
    uint32_t ck_loss_detector;                   
    uint32_t malfunction_monitor;                
    uint32_t spi_ck_source;                      
    uint32_t serial_interface;                   
    int32_t  calibration_offset;                 
    uint32_t right_bit_shift;                    
    uint32_t tm_filter;                          
    uint32_t tm_filter_oversample;               
    uint32_t mm_break_signal;                    
    uint32_t mm_counter_threshold;               
    uint32_t plsk_value;                         
} hpdf_channel_parameter_struct;

 
typedef struct {
    uint32_t tm_fast_mode;                       
    uint32_t tm_channel;                         
    int32_t  tm_high_threshold;                  
    int32_t  tm_low_threshold;                   
    uint32_t extreme_monitor_channel;            
    uint32_t sinc_filter;                        
    uint32_t sinc_oversample;                    
    uint32_t integrator_oversample;              
    uint32_t ht_break_signal;                    
    uint32_t lt_break_signal;                    
} hpdf_filter_parameter_struct;

 
typedef struct {
    uint32_t fast_mode;                          
    uint32_t rcs_channel;                        
    uint32_t rcdmaen;                            
    uint32_t rcsyn;                              
    uint32_t continuous_mode;                    
} hpdf_rc_parameter_struct;

 
typedef struct {
    uint32_t trigger_edge;                       
    uint32_t trigger_signal;                     
    uint32_t icdmaen;                            
    uint32_t scmod;                              
    uint32_t icsyn;                              
    uint32_t ic_channel_group;                   
} hpdf_ic_parameter_struct;

 
 
typedef enum {
    CHANNEL0 = 0,                                
    CHANNEL1,                                    
    CHANNEL2,                                    
    CHANNEL3,                                    
    CHANNEL4,                                    
    CHANNEL5,                                    
    CHANNEL6,                                    
    CHANNEL7                                     
} hpdf_channel_enum;

 
typedef enum {
    FLT0 = 0,                                    
    FLT1,                                        
    FLT2,                                        
    FLT3                                         
} hpdf_filter_enum;

 
typedef enum {
     
    HPDF_FLAG_FLTY_ICEF      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(0U)),                  
    HPDF_FLAG_FLTY_RCEF      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(1U)),                  
    HPDF_FLAG_FLTY_ICDOF     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(2U)),                  
    HPDF_FLAG_FLTY_RCDOF     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(3U)),                  
    HPDF_FLAG_FLTY_TMEOF     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(4U)),                  
    HPDF_FLAG_FLTY_ICPF      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(13U)),                 
    HPDF_FLAG_FLTY_RCPF      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(14U)),                 
    HPDF_FLAG_FLT0_CKLF0     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(16U)),                 
    HPDF_FLAG_FLT0_CKLF1     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(17U)),                 
    HPDF_FLAG_FLT0_CKLF2     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(18U)),                 
    HPDF_FLAG_FLT0_CKLF3     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(19U)),                 
    HPDF_FLAG_FLT0_CKLF4     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(20U)),                 
    HPDF_FLAG_FLT0_CKLF5     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(21U)),                 
    HPDF_FLAG_FLT0_CKLF6     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(22U)),                 
    HPDF_FLAG_FLT0_CKLF7     = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(23U)),                 
    HPDF_FLAG_FLT0_MMF0      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(24U)),                 
    HPDF_FLAG_FLT0_MMF1      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(25U)),                 
    HPDF_FLAG_FLT0_MMF2      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(26U)),                 
    HPDF_FLAG_FLT0_MMF3      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(27U)),                 
    HPDF_FLAG_FLT0_MMF4      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(28U)),                 
    HPDF_FLAG_FLT0_MMF5      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(29U)),                 
    HPDF_FLAG_FLT0_MMF6      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(30U)),                 
    HPDF_FLAG_FLT0_MMF7      = (((uint32_t)(((uint16_t)0x0008U)) << 6) | (uint32_t)(31U)),                 
     
    HPDF_FLAG_FLTY_RCHPDT    = (((uint32_t)(((uint16_t)0x001CU)) << 6) | (uint32_t)(4U)),                 
     
    HPDF_FLAG_FLTY_LTF0      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(0U)),                
    HPDF_FLAG_FLTY_LTF1      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(1U)),                
    HPDF_FLAG_FLTY_LTF2      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(2U)),                
    HPDF_FLAG_FLTY_LTF3      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(3U)),                
    HPDF_FLAG_FLTY_LTF4      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(4U)),                
    HPDF_FLAG_FLTY_LTF5      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(5U)),                
    HPDF_FLAG_FLTY_LTF6      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(6U)),                
    HPDF_FLAG_FLTY_LTF7      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(7U)),                
    HPDF_FLAG_FLTY_HTF0      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(8U)),                
    HPDF_FLAG_FLTY_HTF1      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(9U)),                
    HPDF_FLAG_FLTY_HTF2      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(10U)),               
    HPDF_FLAG_FLTY_HTF3      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(11U)),               
    HPDF_FLAG_FLTY_HTF4      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(12U)),               
    HPDF_FLAG_FLTY_HTF5      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(13U)),               
    HPDF_FLAG_FLTY_HTF6      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(14U)),               
    HPDF_FLAG_FLTY_HTF7      = (((uint32_t)(((uint16_t)0x0028U)) << 6) | (uint32_t)(15U))                
} hpdf_flag_enum;

 
typedef enum {
     
    HPDF_INT_FLAG_FLTY_ICEF      = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((0U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(0U))),       
    HPDF_INT_FLAG_FLTY_RCEF      = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((1U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(1U))),       
    HPDF_INT_FLAG_FLTY_ICDOF     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((2U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(2U))),       
    HPDF_INT_FLAG_FLTY_RCDOF     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((3U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(3U))),       
    HPDF_INT_FLAG_FLTY_TMEOF     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((4U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(4U))),       
    HPDF_INT_FLAG_FLT0_CKLF0     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((16U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(6U))),      
    HPDF_INT_FLAG_FLT0_CKLF1     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((17U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(6U))),      
    HPDF_INT_FLAG_FLT0_CKLF2     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((18U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(6U))),      
    HPDF_INT_FLAG_FLT0_CKLF3     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((19U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(6U))),      
    HPDF_INT_FLAG_FLT0_CKLF4     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((20U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(6U))),      
    HPDF_INT_FLAG_FLT0_CKLF5     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((21U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(6U))),      
    HPDF_INT_FLAG_FLT0_CKLF6     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((22U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(6U))),      
    HPDF_INT_FLAG_FLT0_CKLF7     = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((23U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(6U))),      
    HPDF_INT_FLAG_FLT0_MMF0      = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((24U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(5U))),      
    HPDF_INT_FLAG_FLT0_MMF1      = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((25U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(5U))),      
    HPDF_INT_FLAG_FLT0_MMF2      = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((26U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(5U))),      
    HPDF_INT_FLAG_FLT0_MMF3      = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((27U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(5U))),      
    HPDF_INT_FLAG_FLT0_MMF4      = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((28U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(5U))),      
    HPDF_INT_FLAG_FLT0_MMF5      = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((29U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(5U))),      
    HPDF_INT_FLAG_FLT0_MMF6      = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((30U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(5U))),      
    HPDF_INT_FLAG_FLT0_MMF7      = (((uint32_t)(((uint16_t)0x0008U)) << 22) | (uint32_t)((31U) << 16) | (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(5U)))       
} hpdf_interrput_flag_enum;

 
typedef enum {
     
    HPDF_INT_FLTY_ICEIE      = (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(0U)),             
    HPDF_INT_FLTY_RCEIE      = (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(1U)),             
    HPDF_INT_FLTY_ICDOIE     = (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(2U)),             
    HPDF_INT_FLTY_RCDOIE     = (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(3U)),             
    HPDF_INT_FLTY_TMIE       = (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(4U)),             
    HPDF_INT_FLT0_MMIE       = (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(5U)),             
    HPDF_INT_FLT0_CKLIE      = (((uint32_t)(((uint16_t)0x0004U)) << 6) | (uint32_t)(6U))              
} hpdf_interrput_enum;

 
 

 

 

 

 

 

 

 

 

 

 

 
 

 

 

 

 

 

 

 

 

 

 

 
 

 

 
 

 

 

 

 

 
 

 
 
 
void hpdf_deinit(void);
 
void hpdf_channel_struct_para_init(hpdf_channel_parameter_struct *init_struct);
 
void hpdf_filter_struct_para_init(hpdf_filter_parameter_struct *init_struct);
 
void hpdf_rc_struct_para_init(hpdf_rc_parameter_struct *init_struct);
 
void hpdf_ic_struct_para_init(hpdf_ic_parameter_struct *init_struct);
 
void hpdf_enable(void);
 
void hpdf_disable(void);
 
void hpdf_channel_init(hpdf_channel_enum channelx, hpdf_channel_parameter_struct *init_struct);
 
void hpdf_filter_init(hpdf_filter_enum filtery, hpdf_filter_parameter_struct *init_struct);
 
void hpdf_rc_init(hpdf_filter_enum filtery, hpdf_rc_parameter_struct *init_struct);
 
void hpdf_ic_init(hpdf_filter_enum filtery, hpdf_ic_parameter_struct *init_struct);

 
 
void hpdf_clock_output_config(uint32_t source, uint8_t divider, uint32_t mode);
 
void hpdf_clock_output_source_config(uint32_t source);
 
void hpdf_clock_output_duty_mode_disable(void);
 
void hpdf_clock_output_duty_mode_enable(void);
 
void hpdf_clock_output_divider_config(uint8_t divider);

 
 
void hpdf_channel_enable(hpdf_channel_enum channelx);
 
void hpdf_channel_disable(hpdf_channel_enum channelx);
 
void hpdf_spi_clock_source_config(hpdf_channel_enum channelx, uint32_t clock_source);
 
void hpdf_serial_interface_type_config(hpdf_channel_enum channelx, uint32_t type);
 
void hpdf_malfunction_monitor_disable(hpdf_channel_enum channelx);
 
void hpdf_malfunction_monitor_enable(hpdf_channel_enum channelx);
 
void hpdf_clock_loss_disable(hpdf_channel_enum channelx);
 
void hpdf_clock_loss_enable(hpdf_channel_enum channelx);
 
void hpdf_channel_pin_redirection_disable(hpdf_channel_enum channelx);
 
void hpdf_channel_pin_redirection_enable(hpdf_channel_enum channelx);
 
void hpdf_channel_multiplexer_config(hpdf_channel_enum channelx, uint32_t data_source);
 
void hpdf_data_pack_mode_config(hpdf_channel_enum channelx, uint32_t mode);
 
void hpdf_data_right_bit_shift_config(hpdf_channel_enum channelx, uint8_t right_shift);
 
void hpdf_calibration_offset_config(hpdf_channel_enum channelx, int32_t offset);
 
void hpdf_malfunction_break_signal_config(hpdf_channel_enum channelx, uint32_t break_signal);
 
void hpdf_malfunction_counter_config(hpdf_channel_enum channelx, uint8_t threshold);
 
void hpdf_write_parallel_data_standard_mode(hpdf_channel_enum channelx, int16_t data);
 
void hpdf_write_parallel_data_interleaved_mode(hpdf_channel_enum channelx, int32_t data);
 
void hpdf_write_parallel_data_dual_mode(hpdf_channel_enum channelx, int32_t data);
 
void hpdf_pulse_skip_update(hpdf_channel_enum channelx, uint8_t number);
 
uint8_t hpdf_pulse_skip_read(hpdf_channel_enum channelx);

 
 
void hpdf_filter_enable(hpdf_filter_enum filtery);
 
void hpdf_filter_disable(hpdf_filter_enum filtery);
 
void hpdf_filter_config(hpdf_filter_enum filtery, uint32_t order, uint16_t oversample);
 
void hpdf_integrator_oversample(hpdf_filter_enum filtery, uint16_t oversample);

 
 
void hpdf_threshold_monitor_filter_config(hpdf_channel_enum channelx, uint32_t order, uint8_t oversample);
 
int16_t hpdf_threshold_monitor_filter_read_data(hpdf_channel_enum channelx);
 
void hpdf_threshold_monitor_fast_mode_disable(hpdf_filter_enum filtery);
 
void hpdf_threshold_monitor_fast_mode_enable(hpdf_filter_enum filtery);
 
void hpdf_threshold_monitor_channel(hpdf_filter_enum filtery, uint32_t channel);
 
void hpdf_threshold_monitor_high_threshold(hpdf_filter_enum filtery, int32_t value);
 
void hpdf_threshold_monitor_low_threshold(hpdf_filter_enum filtery, int32_t value);
 
void hpdf_high_threshold_break_signal(hpdf_filter_enum filtery, uint32_t break_signal);
 
void hpdf_low_threshold_break_signal(hpdf_filter_enum filtery, uint32_t break_signal);

 
 
void hpdf_extremes_monitor_channel(hpdf_filter_enum filtery, uint32_t channel);
 
int32_t hpdf_extremes_monitor_maximum_get(hpdf_filter_enum filtery);
 
int32_t hpdf_extremes_monitor_minimum_get(hpdf_filter_enum filtery);

 
uint32_t hpdf_conversion_time_get(hpdf_filter_enum filtery);

 
 
void hpdf_rc_continuous_disable(hpdf_filter_enum filtery);
 
void hpdf_rc_continuous_enable(hpdf_filter_enum filtery);
 
void hpdf_rc_start_by_software(hpdf_filter_enum filtery);
 
void hpdf_rc_syn_disable(hpdf_filter_enum filtery);
 
void hpdf_rc_syn_enable(hpdf_filter_enum filtery);
 
void hpdf_rc_dma_disable(hpdf_filter_enum filtery);
 
void hpdf_rc_dma_enable(hpdf_filter_enum filtery);
 
void hpdf_rc_channel_config(hpdf_filter_enum filtery, uint32_t channel);
 
void hpdf_rc_fast_mode_disable(hpdf_filter_enum filtery);
 
void hpdf_rc_fast_mode_enable(hpdf_filter_enum filtery);
 
int32_t hpdf_rc_data_get(hpdf_filter_enum filtery);
 
uint8_t hpdf_rc_channel_get(hpdf_filter_enum filtery);

 
 
void hpdf_ic_start_by_software(hpdf_filter_enum filtery);
 
void hpdf_ic_syn_disable(hpdf_filter_enum filtery);
 
void hpdf_ic_syn_enable(hpdf_filter_enum filtery);
 
void hpdf_ic_dma_disable(hpdf_filter_enum filtery);
 
void hpdf_ic_dma_enable(hpdf_filter_enum filtery);
 
void hpdf_ic_scan_mode_disable(hpdf_filter_enum filtery);
 
void hpdf_ic_scan_mode_enable(hpdf_filter_enum filtery);
 
void hpdf_ic_trigger_signal_disable(hpdf_filter_enum filtery);
 
void hpdf_ic_trigger_signal_config(hpdf_filter_enum filtery, uint32_t trigger, uint32_t trigger_edge);
 
void hpdf_ic_channel_config(hpdf_filter_enum filtery, uint32_t channel);
 
int32_t hpdf_ic_data_get(hpdf_filter_enum filtery);
 
uint8_t hpdf_ic_channel_get(hpdf_filter_enum filtery);

 
 
FlagStatus hpdf_flag_get(hpdf_filter_enum filtery, hpdf_flag_enum flag);
 
void hpdf_flag_clear(hpdf_filter_enum filtery, hpdf_flag_enum flag);
 
void hpdf_interrupt_enable(hpdf_filter_enum filtery, hpdf_interrput_enum interrupt);
 
void hpdf_interrupt_disable(hpdf_filter_enum filtery, hpdf_interrput_enum interrupt);
 
FlagStatus hpdf_interrupt_flag_get(hpdf_filter_enum filtery, hpdf_interrput_flag_enum int_flag);
 
void hpdf_interrupt_flag_clear(hpdf_filter_enum filtery, hpdf_interrput_flag_enum int_flag);






 


























 






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 
typedef enum {
    I2C_INT_FLAG_TI      = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((1U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(1U))),           
    I2C_INT_FLAG_RBNE    = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((2U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(2U))),           
    I2C_INT_FLAG_ADDSEND = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((3U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(3U))),           
    I2C_INT_FLAG_NACK    = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((4U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(4U))),           
    I2C_INT_FLAG_STPDET  = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((5U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(5U))),           
    I2C_INT_FLAG_TC      = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((6U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(6U))),           
    I2C_INT_FLAG_TCR     = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((7U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(6U))),           
    I2C_INT_FLAG_BERR    = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((8U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(7U))),           
    I2C_INT_FLAG_LOSTARB = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((9U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(7U))),           
    I2C_INT_FLAG_OUERR   = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((10U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(7U))),          
    I2C_INT_FLAG_PECERR  = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((11U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(7U))),          
    I2C_INT_FLAG_TIMEOUT = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((12U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(7U))),          
    I2C_INT_FLAG_SMBALT  = (((uint32_t)(((uint32_t)0x00000018U)) << 22) | (uint32_t)((13U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(7U)))           
} i2c_interrupt_flag_enum;

 

 

 

 

 

 

 

 

 


typedef enum {
    I2C_MODE_NONE               = 0x00U,                                          
    I2C_MODE_MASTER             = 0x10U,                                          
    I2C_MODE_SLAVE              = 0x20U                                           
} i2c_mode_enum;

 





 
 
 
void i2c_deinit(uint32_t i2c_periph);
 
void i2c_timing_config(uint32_t i2c_periph, uint32_t psc, uint32_t scl_dely, uint32_t sda_dely);
 
void i2c_digital_noise_filter_config(uint32_t i2c_periph, uint32_t filter_length);
 
void i2c_analog_noise_filter_enable(uint32_t i2c_periph);
 
void i2c_analog_noise_filter_disable(uint32_t i2c_periph);
 
void i2c_master_clock_config(uint32_t i2c_periph, uint32_t sclh, uint32_t scll);
 
void i2c_master_addressing(uint32_t i2c_periph, uint32_t address, uint32_t trans_direction);

 
 
void i2c_address10_header_enable(uint32_t i2c_periph);
 
void i2c_address10_header_disable(uint32_t i2c_periph);
 
void i2c_address10_enable(uint32_t i2c_periph);
 
void i2c_address10_disable(uint32_t i2c_periph);
 
void i2c_automatic_end_enable(uint32_t i2c_periph);
 
void i2c_automatic_end_disable(uint32_t i2c_periph);
 
void i2c_slave_response_to_gcall_enable(uint32_t i2c_periph);
 
void i2c_slave_response_to_gcall_disable(uint32_t i2c_periph);
 
void i2c_stretch_scl_low_enable(uint32_t i2c_periph);
 
void i2c_stretch_scl_low_disable(uint32_t i2c_periph);
 
void i2c_address_config(uint32_t i2c_periph, uint32_t address, uint32_t addr_format);
 
void i2c_address_bit_compare_config(uint32_t i2c_periph, uint32_t compare_bits);
 
void i2c_address_disable(uint32_t i2c_periph);
 
void i2c_second_address_config(uint32_t i2c_periph, uint32_t address, uint32_t addr_mask);
 
void i2c_second_address_disable(uint32_t i2c_periph);
 
uint32_t i2c_recevied_address_get(uint32_t i2c_periph);
 
void i2c_slave_byte_control_enable(uint32_t i2c_periph);
 
void i2c_slave_byte_control_disable(uint32_t i2c_periph);
 
void i2c_nack_enable(uint32_t i2c_periph);
 
void i2c_nack_disable(uint32_t i2c_periph);
 
void i2c_wakeup_from_deepsleep_enable(uint32_t i2c_periph);
 
void i2c_wakeup_from_deepsleep_disable(uint32_t i2c_periph);
 
void i2c_enable(uint32_t i2c_periph);
 
void i2c_disable(uint32_t i2c_periph);
 
void i2c_start_on_bus(uint32_t i2c_periph);
 
void i2c_stop_on_bus(uint32_t i2c_periph);
 
void i2c_data_transmit(uint32_t i2c_periph, uint32_t data);
 
uint32_t i2c_data_receive(uint32_t i2c_periph);
 
void i2c_reload_enable(uint32_t i2c_periph);
 
void i2c_reload_disable(uint32_t i2c_periph);
 
void i2c_transfer_byte_number_config(uint32_t i2c_periph, uint32_t byte_number);
 
void i2c_dma_enable(uint32_t i2c_periph, uint8_t dma);
 
void i2c_dma_disable(uint32_t i2c_periph, uint8_t dma);
 
void i2c_pec_transfer(uint32_t i2c_periph);
 
void i2c_pec_enable(uint32_t i2c_periph);
 
void i2c_pec_disable(uint32_t i2c_periph);
 
uint32_t i2c_pec_value_get(uint32_t i2c_periph);
 
void i2c_smbus_alert_enable(uint32_t i2c_periph);
 
void i2c_smbus_alert_disable(uint32_t i2c_periph);
 
void i2c_smbus_default_addr_enable(uint32_t i2c_periph);
 
void i2c_smbus_default_addr_disable(uint32_t i2c_periph);
 
void i2c_smbus_host_addr_enable(uint32_t i2c_periph);
 
void i2c_smbus_host_addr_disable(uint32_t i2c_periph);
 
void i2c_extented_clock_timeout_enable(uint32_t i2c_periph);
 
void i2c_extented_clock_timeout_disable(uint32_t i2c_periph);
 
void i2c_clock_timeout_enable(uint32_t i2c_periph);
 
void i2c_clock_timeout_disable(uint32_t i2c_periph);
 
void i2c_bus_timeout_b_config(uint32_t i2c_periph, uint32_t timeout);
 
void i2c_bus_timeout_a_config(uint32_t i2c_periph, uint32_t timeout);
 
void i2c_idle_clock_timeout_config(uint32_t i2c_periph, uint32_t timeout);

 
 
FlagStatus i2c_flag_get(uint32_t i2c_periph, uint32_t flag);
 
void i2c_flag_clear(uint32_t i2c_periph, uint32_t flag);
 
void i2c_interrupt_enable(uint32_t i2c_periph, uint32_t interrupt);
 
void i2c_interrupt_disable(uint32_t i2c_periph, uint32_t interrupt);
 
FlagStatus i2c_interrupt_flag_get(uint32_t i2c_periph, i2c_interrupt_flag_enum int_flag);
 
void i2c_interrupt_flag_clear(uint32_t i2c_periph, i2c_interrupt_flag_enum int_flag);






 


























 



 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
typedef struct {
    uint32_t foreground_memaddr;                           
    uint32_t foreground_lineoff;                           
    uint32_t foreground_prealpha;                          
    uint32_t foreground_alpha_algorithm;                   
    uint32_t foreground_pf;                                
    uint32_t foreground_prered;                            
    uint32_t foreground_pregreen;                          
    uint32_t foreground_preblue;                           
    uint32_t foreground_interlace_mode;                    
    uint32_t foreground_efuv_memaddr;                      
} ipa_foreground_parameter_struct;

 
typedef struct {
    uint32_t background_memaddr;                           
    uint32_t background_lineoff;                           
    uint32_t background_prealpha;                          
    uint32_t background_alpha_algorithm;                   
    uint32_t background_pf;                                
    uint32_t background_prered;                            
    uint32_t background_pregreen;                          
    uint32_t background_preblue;                           
} ipa_background_parameter_struct;

 
typedef struct {
    uint32_t destination_memaddr;                          
    uint32_t destination_lineoff;                          
    uint32_t destination_prealpha;                         
    uint32_t destination_pf;                               
    uint32_t destination_prered;                           
    uint32_t destination_pregreen;                         
    uint32_t destination_preblue;                          
    uint32_t image_width;                                  
    uint32_t image_height;                                 
    uint32_t image_rotate;                                 
    uint32_t image_hor_decimation;                         
    uint32_t image_ver_decimation;                         
    uint32_t image_bilinear_xscale;                        
    uint32_t image_bilinear_yscale;                        
    uint32_t image_scaling_width;                          
    uint32_t image_scaling_height;                         
} ipa_destination_parameter_struct;

 
typedef enum {
    IPA_DPF_ARGB8888 = 0U,                                 
    IPA_DPF_RGB888,                                        
    IPA_DPF_RGB565,                                        
    IPA_DPF_ARGB1555,                                      
    IPA_DPF_ARGB4444                                       
} ipa_dpf_enum;

 
typedef struct {
    uint32_t color_space;                                  
    uint32_t y_offset;                                     
    uint32_t uv_offset;                                    
    uint32_t coef_c0;                                      
    uint32_t coef_c1;                                      
    uint32_t coef_c2;                                      
    uint32_t coef_c3;                                      
    uint32_t coef_c4;                                      
} ipa_conversion_parameter_struct;

 
typedef enum {
    IPA_COLORSPACE_YUV = 0U,                               
    IPA_COLORSPACE_YCBCR                                   
} ipa_colorspace_enum;

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
 
void ipa_deinit(void);
 
void ipa_transfer_enable(void);
 
void ipa_transfer_hangup_enable(void);
 
void ipa_transfer_hangup_disable(void);
 
void ipa_transfer_stop_enable(void);
 
void ipa_transfer_stop_disable(void);
 
void ipa_foreground_lut_loading_enable(void);
 
void ipa_background_lut_loading_enable(void);
 
void ipa_pixel_format_convert_mode_set(uint32_t pfcm);
 
void ipa_foreground_interlace_mode_enable(void);
 
void ipa_foreground_interlace_mode_disable(void);

 

 
void ipa_foreground_struct_para_init(ipa_foreground_parameter_struct *foreground_struct);
 
void ipa_foreground_init(ipa_foreground_parameter_struct *foreground_struct);

 
void ipa_background_struct_para_init(ipa_background_parameter_struct *background_struct);
 
void ipa_background_init(ipa_background_parameter_struct *background_struct);

 
void ipa_destination_struct_para_init(ipa_destination_parameter_struct *destination_struct);
 
void ipa_destination_init(ipa_destination_parameter_struct *destination_struct);
 
void ipa_foreground_lut_init(uint8_t fg_lut_num, uint8_t fg_lut_pf, uint32_t fg_lut_addr);
 
void ipa_background_lut_init(uint8_t bg_lut_num, uint8_t bg_lut_pf, uint32_t bg_lut_addr);

 
 
void ipa_line_mark_config(uint16_t line_num);
 
void ipa_inter_timer_config(uint8_t timer_cfg);
 
void ipa_interval_clock_num_config(uint8_t clk_num);

 
void ipa_color_conversion_struct_para_init(ipa_conversion_parameter_struct *conversion_struct, ipa_colorspace_enum colorspace);
 
void ipa_color_conversion_config(ipa_conversion_parameter_struct *conversion_struct);
 
void ipa_foreground_scaling_config(uint32_t horizontal_decimation, uint32_t vertical_decimation, uint32_t image_scaling_width,
                                   uint32_t image_scaling_height);
 
void ipa_destination_scaling_config(uint32_t dest_scaling_width, uint32_t dest_scaling_height);

 
 
FlagStatus ipa_flag_get(uint32_t flag);
 
void ipa_flag_clear(uint32_t flag);
 
void ipa_interrupt_enable(uint32_t int_flag);
 
void ipa_interrupt_disable(uint32_t int_flag);
 
FlagStatus ipa_interrupt_flag_get(uint32_t int_flag);
 
void ipa_interrupt_flag_clear(uint32_t int_flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 
 
typedef struct {
    uint32_t ref_clock;                                              
    uint32_t trigger_input;                                          
    uint32_t sampling_time;                                          
} lpdts_parameter_struct;

 

 

 

 

 

 
 
 
void lpdts_deinit(void);
 
void lpdts_struct_para_init(lpdts_parameter_struct *init_struct);
 
void lpdts_init(lpdts_parameter_struct *init_struct);

 
 
void lpdts_enable(void);
 
void lpdts_disable(void);
 
void lpdts_soft_trigger_enable(void);
 
void lpdts_soft_trigger_disable(void);
 
void lpdts_high_threshold_set(uint16_t value);
 
void lpdts_low_threshold_set(uint16_t value);
 
void lpdts_ref_clock_source_config(uint32_t source);
 
int32_t lpdts_temperature_get(void);

 
 
FlagStatus lpdts_flag_get(uint32_t flag);
 
void lpdts_interrupt_enable(uint32_t interrupt);
 
void lpdts_interrupt_disable(uint32_t interrupt);
 
FlagStatus lpdts_interrupt_flag_get(uint32_t flag);
 
void lpdts_interrupt_flag_clear(uint32_t flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 
 

 

 

 

 

 

 

 

 

 
 
 
void mdio_deinit(void);
 
void mdio_software_reset(void);

 
 
uint32_t mdio_init(uint32_t phy_size, uint32_t phy_softaddr, uint32_t phy_sel, uint16_t devadd);
 
void mdio_phy_length_config(uint32_t phy_bit);
 
void mdio_soft_phyadr_set(uint32_t phy_soft);
 
void mdio_framefield_phyadr_config(uint32_t phy_sel);
 
void mdio_framefield_devadd_config(uint16_t type);
 
uint32_t mdio_phy_pin_read(void);
 
void mdio_timeout_config(uint16_t timeout);
 
void mdio_timeout_enable(void);
 
void mdio_timeout_disable(void);

 
 
uint16_t mdio_op_receive(void);
 
uint16_t mdio_phyadr_receive(void);
 
uint16_t mdio_devadd_receive(void);
 
uint16_t mdio_ta_receive(void);
 
uint16_t mdio_data_receive(void);
 
uint16_t mdio_address_receive(void);
 
void mdio_data_transmit(uint16_t data);

 
 
FlagStatus mdio_flag_get(uint32_t flag);
 
void mdio_flag_clear(uint32_t flag);
 
void mdio_interrupt_enable(uint32_t interrupt);
 
void mdio_interrupt_disable(uint32_t interrupt);






 


























 



 

 
        

 
 

 

 

 

 

 

 

 

 

 

          

           

          
            
          

 
 
typedef struct {            
    uint32_t request;                                                                                        
    uint32_t trans_trig_mode;                                                                                
    uint32_t priority;                                                                                       
    uint32_t endianness;                                                                                     
    uint32_t source_inc;                                                                                     
    uint32_t dest_inc;                                                                                       
    uint32_t source_data_size;                                                                               
    uint32_t dest_data_dize;                                                                                 
    uint32_t data_alignment;                                                                                 
    uint32_t buff_trans_len;                                                                                 
    uint32_t source_burst;                                                                                   
    uint32_t dest_burst;                                                                                     
    uint32_t mask_addr;                                                                                      
    uint32_t mask_data;                                                                                      
    uint32_t source_addr;                                                                                    
    uint32_t destination_addr;                                                                               
    uint32_t tbytes_num_in_block;                                                                            
    uint32_t source_bus;                                                                                     
    uint32_t destination_bus;                                                                                
    uint32_t bufferable_write_mode;                                                                          
} mdma_parameter_struct;

 
typedef enum {
    UPDATE_DIR_INCREASE = 0,                                                                                 
    UPDATE_DIR_DECREASE = 1,                                                                                 
} mdma_add_update_dir_enum;

 
typedef struct {
    uint32_t block_num;                                                                                      
    uint16_t saddr_update_val;                                                                               
    uint16_t dstaddr_update_val;                                                                             
    mdma_add_update_dir_enum saddr_update_dir;                                                               
    mdma_add_update_dir_enum dstaddr_update_dir;                                                             
} mdma_multi_block_parameter_struct;

 
typedef struct {
    volatile uint32_t chxcfg_reg;                                                                                
    volatile uint32_t chxbtcfg_reg;                                                                              
    volatile uint32_t chxsaddr_reg;                                                                              
    volatile uint32_t chxdaddr_reg;                                                                              
    volatile uint32_t chxmbaddru_reg;                                                                            
    volatile uint32_t chxladdr_reg;                                                                              
    volatile uint32_t chxctl1_reg;                                                                               
    volatile uint32_t reserved;                                                                                  
    volatile uint32_t chxmaddr_reg;                                                                              
    volatile uint32_t chxmdata_reg;                                                                              
} mdma_link_node_parameter_struct;

 
typedef enum {
    MDMA_CH0 = 0,                                                                                            
    MDMA_CH1,                                                                                                
    MDMA_CH2,                                                                                                
    MDMA_CH3,                                                                                                
    MDMA_CH4,                                                                                                
    MDMA_CH5,                                                                                                
    MDMA_CH6,                                                                                                
    MDMA_CH7,                                                                                                
    MDMA_CH8,                                                                                                
    MDMA_CH9,                                                                                                
    MDMA_CH10,                                                                                               
    MDMA_CH11,                                                                                               
    MDMA_CH12,                                                                                               
    MDMA_CH13,                                                                                               
    MDMA_CH14,                                                                                               
    MDMA_CH15                                                                                                
} mdma_channel_enum;

 

 

 

 

 

 

 

 

 

 

 

 

             

            
            
             

 

             

           

 
 
 
void mdma_deinit(void);
 
void mdma_channel_deinit(mdma_channel_enum channelx);
 
void mdma_para_struct_init(mdma_parameter_struct *init_struct);
 
void mdma_multi_block_para_struct_init(mdma_multi_block_parameter_struct *block_init_struct);
 
void mdma_link_node_para_struct_init(mdma_link_node_parameter_struct *node);
 
void mdma_init(mdma_channel_enum channelx, mdma_parameter_struct *init_struct);
 
void mdma_buffer_block_mode_config(mdma_channel_enum channelx, uint32_t saddr, uint32_t daddr, uint32_t tbnum);
 
void mdma_multi_block_mode_config(mdma_channel_enum channelx, uint32_t tbnum, mdma_multi_block_parameter_struct *block_init_struct);
 
void mdma_node_create(mdma_link_node_parameter_struct *node, mdma_multi_block_parameter_struct *block_init_struct, mdma_parameter_struct *init_struct);
 
void mdma_node_add(mdma_link_node_parameter_struct *pre_node, mdma_link_node_parameter_struct *new_node);
 
ErrStatus mdma_node_delete(mdma_link_node_parameter_struct *pre_node, mdma_link_node_parameter_struct *unused_node);

 
 
void mdma_destination_address_config(mdma_channel_enum channelx, uint32_t address);
 
void mdma_source_address_config(mdma_channel_enum channelx, uint32_t address);
 
void mdma_destination_bus_config(mdma_channel_enum channelx, uint32_t bus);
 
void mdma_source_bus_config(mdma_channel_enum channelx, uint32_t bus);
 
void mdma_priority_config(mdma_channel_enum channelx, uint32_t priority);
 
void mdma_endianness_config(mdma_channel_enum channelx, uint32_t endianness);
 
void mdma_alignment_config(mdma_channel_enum channelx, uint32_t alignment);
 
void mdma_source_burst_beats_config(mdma_channel_enum channelx, uint32_t sbeat);
 
void mdma_destination_burst_beats_config(mdma_channel_enum channelx, uint32_t dbeat);
 
void mdma_source_width_config(mdma_channel_enum channelx, uint32_t swidth);
 
void mdma_destination_width_config(mdma_channel_enum channelx, uint32_t dwidth);
 
void mdma_source_increment_config(mdma_channel_enum channelx, uint32_t sinc);
 
void mdma_destination_increment_config(mdma_channel_enum channelx, uint32_t dinc);
 
void mdma_channel_bufferable_write_enable(mdma_channel_enum channelx);
 
void mdma_channel_bufferable_write_disable(mdma_channel_enum channelx);
 
void mdma_channel_software_request_enable(mdma_channel_enum channelx);
 
void mdma_channel_enable(mdma_channel_enum channelx);
 
void mdma_channel_disable(mdma_channel_enum channelx);
 
uint32_t mdma_transfer_error_direction_get(mdma_channel_enum channelx);
 
uint32_t mdma_transfer_error_address_get(mdma_channel_enum channelx);

 
 
FlagStatus mdma_flag_get(mdma_channel_enum channelx, uint32_t flag);
 
void mdma_flag_clear(mdma_channel_enum channelx, uint32_t flag);
 
void mdma_interrupt_enable(mdma_channel_enum channelx, uint32_t interrupt);
 
void mdma_interrupt_disable(mdma_channel_enum channelx, uint32_t interrupt);
 
FlagStatus mdma_interrupt_flag_get(mdma_channel_enum channelx, uint32_t int_flag);
 
void mdma_interrupt_flag_clear(mdma_channel_enum channelx, uint32_t int_flag);






 


























 




 
typedef struct {
    uint32_t region_base_address;     
    uint8_t region_number;            
    uint8_t region_size;              
    uint8_t subregion_disable;        
    uint8_t tex_type;                 
    uint8_t access_permission;        
    uint8_t access_shareable;         
    uint8_t access_cacheable;         
    uint8_t access_bufferable;        
    uint8_t instruction_exec;         
} mpu_region_init_struct;


 
 

 

 

 

 


 













 
 
void nvic_priority_group_set(uint32_t nvic_prigroup);

 
void nvic_irq_enable(IRQn_Type nvic_irq, uint8_t nvic_irq_pre_priority, uint8_t nvic_irq_sub_priority);
 
void nvic_irq_disable(IRQn_Type nvic_irq);

 
void nvic_vector_table_set(uint32_t nvic_vect_tab, uint32_t offset);

 
void system_lowpower_set(uint8_t lowpower_mode);
 
void system_lowpower_reset(uint8_t lowpower_mode);

 
void systick_clksource_set(uint32_t systick_clksource);

 
void mpu_region_struct_para_init(mpu_region_init_struct *mpu_init_struct);
 
void mpu_region_config(mpu_region_init_struct *mpu_init_struct);
 
void mpu_region_enable(void);






 


























 



 

 

 
 


 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
typedef struct {
    uint32_t prescaler;                                                        
 
    uint32_t fifo_threshold;                                                   
 
    uint32_t sample_shift;                                                     
 
    uint32_t device_size;                                                      



 
    uint32_t cs_hightime;                                                      
 
    uint32_t memory_type;                                                       

    uint32_t wrap_size;                                                         

    uint32_t delay_hold_cycle;                                                  
} ospi_parameter_struct;

 
typedef struct {
    uint32_t operation_type;                                                   

 
    uint32_t instruction;                                                      
 
    uint32_t ins_mode;                                                          

    uint32_t ins_size;                                                          

    uint32_t address;                                                          
 
    uint32_t addr_mode;                                                         

    uint32_t addr_size;                                                         

    uint32_t addr_dtr_mode;                                                     

    uint32_t alter_bytes;                                                      
 
    uint32_t alter_bytes_mode;                                                  

    uint32_t alter_bytes_size;                                                  

    uint32_t alter_bytes_dtr_mode;                                              

    uint32_t data_mode;                                                         

    uint32_t nbdata;                                                           

 
    uint32_t data_dtr_mode;                                                     

    uint32_t dummy_cycles;                                                     
 
} ospi_regular_cmd_struct;

 
typedef struct {
    uint32_t match;                                                            
 
    uint32_t mask;                                                             
 
    uint32_t interval;                                                         
 
    uint32_t match_mode;                                                        

    uint32_t automatic_stop;                                                    

} ospi_autopolling_struct;

 
 

 

 

 

 

 

 

 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
typedef enum {
    OSPI_INT_FLAG_TERR      = (((uint32_t)(((uint32_t)0x00000020U)) << 22) | (uint32_t)((0U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(16U))),           
    OSPI_INT_FLAG_TC        = (((uint32_t)(((uint32_t)0x00000020U)) << 22) | (uint32_t)((1U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(17U))),           
    OSPI_INT_FLAG_FT        = (((uint32_t)(((uint32_t)0x00000020U)) << 22) | (uint32_t)((2U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(18U))),           
    OSPI_INT_FLAG_SM        = (((uint32_t)(((uint32_t)0x00000020U)) << 22) | (uint32_t)((3U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(19U))),           
} ospi_interrupt_flag_enum;

 
 
 
void ospi_deinit(uint32_t ospi_periph);
 
void ospi_struct_init(ospi_parameter_struct *ospi_struct);
 
void ospi_init(uint32_t ospi_periph, ospi_parameter_struct *ospi_struct);
 
void ospi_enable(uint32_t ospi_periph);
 
void ospi_disable(uint32_t ospi_periph);

 
 
void ospi_device_memory_type_config(uint32_t ospi_periph, uint32_t dtysel);
 
void ospi_device_memory_size_config(uint32_t ospi_periph, uint32_t mesz);

 
 
void ospi_functional_mode_config(uint32_t ospi_periph, uint32_t fmod);
 
void ospi_status_polling_config(uint32_t ospi_periph, uint32_t stop, uint32_t mode);
 
void ospi_status_mask_config(uint32_t ospi_periph, uint32_t mask);
 
void ospi_status_match_config(uint32_t ospi_periph, uint32_t match);
 
void ospi_interval_cycle_config(uint32_t ospi_periph, uint16_t interval);

 
 
void ospi_fifo_level_config(uint32_t ospi_periph, uint32_t ftl);
 
void ospi_chip_select_high_cycle_config(uint32_t ospi_periph, uint32_t cshc);
 
void ospi_prescaler_config(uint32_t ospi_periph, uint32_t psc);
 
void ospi_send_instruction_mode_config(uint32_t ospi_periph, uint32_t sioo);
 
void ospi_dummy_cycles_config(uint32_t ospi_periph, uint32_t dumyc);
 
void ospi_delay_hold_cycle_config(uint32_t ospi_periph, uint32_t dehqc);
 
void ospi_sample_shift_config(uint32_t ospi_periph, uint32_t ssample);

 
 
void ospi_data_length_config(uint32_t ospi_periph, uint32_t dtlen);
 
void ospi_instruction_config(uint32_t ospi_periph, uint32_t instruction);
 
void ospi_instruction_mode_config(uint32_t ospi_periph, uint32_t imod);
 
void ospi_instruction_size_config(uint32_t ospi_periph, uint32_t inssz);
 
void ospi_address_config(uint32_t ospi_periph, uint32_t addr);
 
void ospi_address_mode_config(uint32_t ospi_periph, uint32_t addrmod);
 
void ospi_address_dtr_config(uint32_t ospi_periph, uint32_t addrdtr);
 
void ospi_address_size_config(uint32_t ospi_periph, uint32_t addrsz);
 
void ospi_alternate_byte_config(uint32_t ospi_periph, uint32_t alte);
 
void ospi_alternate_byte_mode_config(uint32_t ospi_periph, uint32_t atlemod);
 
void ospi_alternate_byte_dtr_config(uint32_t ospi_periph, uint32_t abdtr);
 
void ospi_alternate_byte_size_config(uint32_t ospi_periph, uint32_t altesz);
 
void ospi_data_mode_config(uint32_t ospi_periph, uint32_t datamod);
 
void ospi_data_dtr_config(uint32_t ospi_periph, uint32_t dadtr);
 
void ospi_data_transmit(uint32_t ospi_periph, uint32_t data);
 
uint32_t ospi_data_receive(uint32_t ospi_periph);

 
 
void ospi_dma_enable(uint32_t ospi_periph);
 
void ospi_dma_disable(uint32_t ospi_periph);

 
 
void ospi_wrap_size_config(uint32_t ospi_periph, uint32_t wpsz);
 
void ospi_wrap_instruction_config(uint32_t ospi_periph, uint32_t instruction);
 
void ospi_wrap_instruction_mode_config(uint32_t ospi_periph, uint32_t imod);
 
void ospi_wrap_instruction_size_config(uint32_t ospi_periph, uint32_t inssz);
 
void ospi_wrap_address_config(uint32_t ospi_periph, uint32_t addr);
 
void ospi_wrap_address_mode_config(uint32_t ospi_periph, uint32_t addrmod);
 
void ospi_wrap_address_dtr_config(uint32_t ospi_periph, uint32_t addrdtr);
 
void ospi_wrap_address_size_config(uint32_t ospi_periph, uint32_t addrsz);
 
void ospi_wrap_alternate_byte_config(uint32_t ospi_periph, uint32_t alte);
 
void ospi_wrap_alternate_byte_mode_config(uint32_t ospi_periph, uint32_t atlemod);
 
void ospi_wrap_alternate_byte_dtr_config(uint32_t ospi_periph, uint32_t abdtr);
 
void ospi_wrap_alternate_byte_size_config(uint32_t ospi_periph, uint32_t altesz);
 
void ospi_wrap_data_mode_config(uint32_t ospi_periph, uint32_t datamod);
 
void ospi_wrap_data_dtr_config(uint32_t ospi_periph, uint32_t dadtr);

 
void ospi_wrap_dummy_cycles_config(uint32_t ospi_periph, uint32_t dumyc);
 
void ospi_wrap_delay_hold_cycle_config(uint32_t ospi_periph, uint32_t dehqc);
 
void ospi_wrap_sample_shift_config(uint32_t ospi_periph, uint32_t ssample);

 
 
void ospi_write_instruction_config(uint32_t ospi_periph, uint32_t instruction);
 
void ospi_write_instruction_mode_config(uint32_t ospi_periph, uint32_t imod);
 
void ospi_write_instruction_size_config(uint32_t ospi_periph, uint32_t inssz);
 
void ospi_write_address_config(uint32_t ospi_periph, uint32_t addr);
 
void ospi_write_address_mode_config(uint32_t ospi_periph, uint32_t addrmod);
 
void ospi_write_address_dtr_config(uint32_t ospi_periph, uint32_t addrdtr);
 
void ospi_write_address_size_config(uint32_t ospi_periph, uint32_t addrsz);
 
void ospi_write_alternate_byte_config(uint32_t ospi_periph, uint32_t alte);
 
void ospi_write_alternate_byte_mode_config(uint32_t ospi_periph, uint32_t atlemod);
 
void ospi_write_alternate_byte_dtr_config(uint32_t ospi_periph, uint32_t abdtr);
 
void ospi_write_alternate_byte_size_config(uint32_t ospi_periph, uint32_t altesz);
 
void ospi_write_data_mode_config(uint32_t ospi_periph, uint32_t datamod);
 
void ospi_write_data_dtr_config(uint32_t ospi_periph, uint32_t dadtr);
 
void ospi_write_dummy_cycles_config(uint32_t ospi_periph, uint32_t dumyc);

 
void ospi_command_config(uint32_t ospi_periph, ospi_parameter_struct *ospi_struct, ospi_regular_cmd_struct *cmd_struct);
 
void ospi_transmit(uint32_t ospi_periph, uint8_t *pdata);
 
void ospi_receive(uint32_t ospi_periph, uint8_t *pdata);
 
void ospi_autopolling_mode(uint32_t ospi_periph, ospi_parameter_struct *ospi_struct, ospi_autopolling_struct *autopl_cfg_struct);

 
 
void ospi_interrupt_enable(uint32_t ospi_periph, uint32_t interrupt);
 
void ospi_interrupt_disable(uint32_t ospi_periph, uint32_t interrupt);
 
uint32_t ospi_fifo_level_get(uint32_t ospi_periph);
 
FlagStatus ospi_flag_get(uint32_t ospi_periph, uint32_t flag);
 
void ospi_flag_clear(uint32_t ospi_periph, uint32_t flag);
 
FlagStatus ospi_interrupt_flag_get(uint32_t ospi_periph, uint32_t int_flag);
 
void ospi_interrupt_flag_clear(uint32_t ospi_periph, uint32_t int_flag);






 


























 



 


 

 
 
 

 

 

 

 

 

 

 

 

 

 
 
void ospim_deinit(void);

 
 
void ospim_port_sck_config(uint8_t port, uint32_t sckconfg);
 
void ospim_port_sck_source_select(uint8_t port, uint32_t sck_source);
 
void ospim_port_csn_config(uint8_t port, uint32_t csnconfig);
 
void ospim_port_csn_source_select(uint8_t port, uint32_t csn_source);
 
void ospim_port_io3_0_config(uint8_t port, uint32_t ioconfig);
 
void ospim_port_io3_0_source_select(uint8_t port, uint32_t io_source);
 
void ospim_port_io7_4_config(uint8_t port, uint32_t ioconfig);
 
void ospim_port_io7_4_source_select(uint8_t port, uint32_t io_source);






 


























 



 

 

 
 

 

 

 

 

 

 
 

 

 

 

 

 



 

 

 

 
 
void pmu_deinit(void);

 
void pmu_lvd_select(uint32_t lvdt_n);
 
void pmu_lvd_enable(void);
 
void pmu_lvd_disable(void);
 
void pmu_vavd_select(uint32_t vavdt_n);
 
void pmu_vavd_enable(void);
 
void pmu_vavd_disable(void);
 
void pmu_vovd_enable(void);
 
void pmu_vovd_disable(void);
 
void pmu_ldo_output_select(uint32_t ldo_n);
 
void pmu_sldo_output_select(uint32_t sldo_n);

 
void pmu_vbat_charging_select(uint32_t resistor);
 
void pmu_vbat_charging_enable(void);
 
void pmu_vbat_charging_disable(void);
 
void pmu_vbat_temp_moniter_enable(void);
 
void pmu_vbat_temp_moniter_disable(void);

 
 
void pmu_usb_regulator_enable(void);
 
void pmu_usb_regulator_disable(void);
 
void pmu_usb_voltage_detector_enable(void);
 
void pmu_usb_voltage_detector_disable(void);

 
void pmu_smps_ldo_supply_config(uint32_t smpsmode);

 
 
void pmu_to_sleepmode(uint8_t sleepmodecmd);
 
void pmu_to_deepsleepmode(uint8_t deepsleepmodecmd);
 
void pmu_to_standbymode(void);
 
void pmu_wakeup_pin_enable(uint32_t wakeup_pin);
 
void pmu_wakeup_pin_disable(uint32_t wakeup_pin);

 
 
void pmu_backup_write_enable(void);
 
void pmu_backup_write_disable(void);
 
void pmu_backup_voltage_stabilizer_enable(void);
 
void pmu_backup_voltage_stabilizer_disable(void);

 
void pmu_enter_deepsleep_wait_time_config(uint32_t wait_time);
 
void pmu_exit_deepsleep_wait_time_config(uint32_t wait_time);

 
 
FlagStatus pmu_flag_get(uint32_t flag);
 
void pmu_flag_clear(uint32_t flag_reset);






 


























 



 

 






 
 

  

 

 

 

 

 

 
 
typedef enum 
{
    RAMECCMU0_MONITOR0 = 0x00U,                                                                               
    RAMECCMU0_MONITOR1 = 0x01U,                                                                               
    RAMECCMU0_MONITOR2 = 0x02U,                                                                               
    RAMECCMU0_MONITOR3 = 0x03U,                                                                               
    RAMECCMU0_MONITOR4 = 0x04U,                                                                               
    RAMECCMU1_MONITOR0 = 0x10U,                                                                               
    RAMECCMU1_MONITOR1 = 0x11U,                                                                               
    RAMECCMU1_MONITOR2 = 0x12U                                                                                
} rameccmu_monitor_enum;

 





 
 
void rameccmu_deinit(uint32_t rameccmu_periph);

 
 
uint32_t rameccmu_monitor_failing_address_get(rameccmu_monitor_enum rameccmu_monitor);
 
uint32_t rameccmu_monitor_failing_data_low_bits_get(rameccmu_monitor_enum rameccmu_monitor);
 
uint32_t rameccmu_monitor_failing_data_high_bits_get(rameccmu_monitor_enum rameccmu_monitor);
 
uint32_t rameccmu_monitor_failing_ecc_error_code_get(rameccmu_monitor_enum rameccmu_monitor);

 
 
void rameccmu_global_interrupt_enable(uint32_t rameccmu_periph, uint32_t interrupt);
 
void rameccmu_global_interrupt_disable(uint32_t rameccmu_periph, uint32_t interrupt);
 
void rameccmu_monitor_interrupt_enable(rameccmu_monitor_enum rameccmu_monitor, uint32_t monitor_interrupt);
 
void rameccmu_monitor_interrupt_disable(rameccmu_monitor_enum rameccmu_monitor, uint32_t monitor_interrupt);
 
FlagStatus rameccmu_monitor_flag_get(rameccmu_monitor_enum rameccmu_monitor, uint32_t flag);
 
void rameccmu_monitor_flag_clear(rameccmu_monitor_enum rameccmu_monitor, uint32_t flag);
 
FlagStatus rameccmu_monitor_interrupt_flag_get(rameccmu_monitor_enum rameccmu_monitor, uint32_t int_flag);
 
void rameccmu_monitor_interrupt_flag_clear(rameccmu_monitor_enum rameccmu_monitor, uint32_t int_flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 
 

 

 

 

 

 
typedef enum {
     
    RCU_ENET1      = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(0U)),                          
    RCU_ENET1TX    = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(1U)),                          
    RCU_ENET1RX    = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(2U)),                          
    RCU_ENET1PTP   = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(3U)),                          
    RCU_USBHS0     = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(14U)),                         
    RCU_USBHS0ULPI = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(15U)),                         
    RCU_DMA0       = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(21U)),                         
    RCU_DMA1       = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(22U)),                         
    RCU_DMAMUX     = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(23U)),                         
    RCU_ENET0      = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(25U)),                         
    RCU_ENET0TX    = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(26U)),                         
    RCU_ENET0RX    = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(27U)),                         
    RCU_ENET0PTP   = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(28U)),                         
    RCU_USBHS1     = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(29U)),                         
    RCU_USBHS1ULPI = (((uint32_t)(((uint32_t)0x00000030U)) << 6) | (uint32_t)(30U)),                         
     
    RCU_DCI        = (((uint32_t)(((uint32_t)0x00000034U)) << 6) | (uint32_t)(0U)),                          
    RCU_FAC        = (((uint32_t)(((uint32_t)0x00000034U)) << 6) | (uint32_t)(1U)),                          
    RCU_SDIO1      = (((uint32_t)(((uint32_t)0x00000034U)) << 6) | (uint32_t)(2U)),                          
    RCU_CAU        = (((uint32_t)(((uint32_t)0x00000034U)) << 6) | (uint32_t)(3U)),                          
    RCU_HAU        = (((uint32_t)(((uint32_t)0x00000034U)) << 6) | (uint32_t)(4U)),                          
    RCU_TRNG       = (((uint32_t)(((uint32_t)0x00000034U)) << 6) | (uint32_t)(6U)),                          
    RCU_TMU        = (((uint32_t)(((uint32_t)0x00000034U)) << 6) | (uint32_t)(7U)),                          
    RCU_RAMECCMU1  = (((uint32_t)(((uint32_t)0x00000034U)) << 6) | (uint32_t)(8U)),                          
     
    RCU_EXMC       = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(0U)),                          
    RCU_IPA        = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(1U)),                          
    RCU_SDIO0      = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(2U)),                          
    RCU_MDMA       = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(3U)),                          
    RCU_OSPIM      = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(4U)),                          
    RCU_OSPI0      = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(5U)),                          
    RCU_OSPI1      = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(6U)),                          
    RCU_RTDEC0     = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(8U)),                          
    RCU_RTDEC1     = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(9U)),                          
    RCU_RAMECCMU0  = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(10U)),                         
    RCU_CPU        = (((uint32_t)(((uint32_t)0x00000038U)) << 6) | (uint32_t)(15U)),                         
     
    RCU_GPIOA      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(0U)),                          
    RCU_GPIOB      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(1U)),                          
    RCU_GPIOC      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(2U)),                          
    RCU_GPIOD      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(3U)),                          
    RCU_GPIOE      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(4U)),                          
    RCU_GPIOF      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(5U)),                          
    RCU_GPIOG      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(6U)),                          
    RCU_GPIOH      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(7U)),                          
    RCU_GPIOJ      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(8U)),                          
    RCU_GPIOK      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(9U)),                          
    RCU_BKPSRAM    = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(13U)),                         
    RCU_CRC        = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(14U)),                         
    RCU_HWSEM      = (((uint32_t)(((uint32_t)0x0000003CU)) << 6) | (uint32_t)(15U)),                         
     
    RCU_TIMER1     = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(0U)),                          
    RCU_TIMER2     = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(1U)),                          
    RCU_TIMER3     = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(2U)),                          
    RCU_TIMER4     = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(3U)),                          
    RCU_TIMER5     = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(4U)),                          
    RCU_TIMER6     = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(5U)),                          
    RCU_TIMER22    = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(6U)),                          
    RCU_TIMER23    = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(7U)),                          
    RCU_TIMER30    = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(8U)),                          
    RCU_TIMER31    = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(9U)),                          
    RCU_TIMER50    = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(10U)),                         
    RCU_TIMER51    = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(11U)),                         
    RCU_RSPDIF     = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(13U)),                         
    RCU_SPI1       = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(14U)),                         
    RCU_SPI2       = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(15U)),                         
    RCU_MDIO       = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(16U)),                         
    RCU_USART1     = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(17U)),                         
    RCU_USART2     = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(18U)),                         
    RCU_UART3      = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(19U)),                         
    RCU_UART4      = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(20U)),                         
    RCU_I2C0       = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(21U)),                         
    RCU_I2C1       = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(22U)),                         
    RCU_I2C2       = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(23U)),                         
    RCU_I2C3       = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(24U)),                         
    RCU_CTC        = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(27U)),                         
    RCU_DACHOLD    = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(28U)),                         
    RCU_DAC        = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(29U)),                         
    RCU_UART6      = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(30U)),                         
    RCU_UART7      = (((uint32_t)(((uint32_t)0x00000040U)) << 6) | (uint32_t)(31U)),                         
     
    RCU_TIMER0     = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(0U)),                          
    RCU_TIMER7     = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(1U)),                          
    RCU_USART0     = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(4U)),                          
    RCU_USART5     = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(5U)),                          
    RCU_ADC0       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(8U)),                          
    RCU_ADC1       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(9U)),                          
    RCU_ADC2       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(10U)),                         
    RCU_SPI0       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(12U)),                         
    RCU_SPI3       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(13U)),                         
    RCU_TIMER14    = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(16U)),                         
    RCU_TIMER15    = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(17U)),                         
    RCU_TIMER16    = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(18U)),                         
    RCU_HPDF       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(19U)),                         
    RCU_SPI4       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(20U)),                         
    RCU_SPI5       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(21U)),                         
    RCU_SAI0       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(22U)),                         
    RCU_SAI1       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(23U)),                         
    RCU_SAI2       = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(24U)),                         
    RCU_TIMER40    = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(25U)),                         
    RCU_TIMER41    = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(26U)),                         
    RCU_TIMER42    = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(27U)),                         
    RCU_TIMER43    = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(28U)),                         
    RCU_TIMER44    = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(29U)),                         
    RCU_EDOUT      = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(30U)),                         
    RCU_TRIGSEL    = (((uint32_t)(((uint32_t)0x00000044U)) << 6) | (uint32_t)(31U)),                         
     
    RCU_TLI        = (((uint32_t)(((uint32_t)0x00000048U)) << 6) | (uint32_t)(0U)),                          
    RCU_WWDGT      = (((uint32_t)(((uint32_t)0x00000048U)) << 6) | (uint32_t)(1U)),                          
     
    RCU_SYSCFG     = (((uint32_t)(((uint32_t)0x0000004CU)) << 6) | (uint32_t)(0U)),                          
    RCU_CMP        = (((uint32_t)(((uint32_t)0x0000004CU)) << 6) | (uint32_t)(1U)),                          
    RCU_VREF       = (((uint32_t)(((uint32_t)0x0000004CU)) << 6) | (uint32_t)(2U)),                          
    RCU_LPDTS      = (((uint32_t)(((uint32_t)0x0000004CU)) << 6) | (uint32_t)(3U)),                          
    RCU_PMU        = (((uint32_t)(((uint32_t)0x0000004CU)) << 6) | (uint32_t)(4U)),                          
    RCU_RTC        = (((uint32_t)(((uint32_t)0x00000070U)) << 6) | (uint32_t)(15U)),                          
     
    RCU_CAN0       = (((uint32_t)(((uint32_t)0x000000E4U)) << 6) | (uint32_t)(0U)),                      
    RCU_CAN1       = (((uint32_t)(((uint32_t)0x000000E4U)) << 6) | (uint32_t)(1U)),                      
    RCU_CAN2       = (((uint32_t)(((uint32_t)0x000000E4U)) << 6) | (uint32_t)(2U))                       
} rcu_periph_enum;

 
typedef enum {
     
    RCU_ENET1_SLP       = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(0U)),                   
    RCU_ENET1TX_SLP     = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(1U)),                   
    RCU_ENET1RX_SLP     = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(2U)),                   
    RCU_ENET1PTP_SLP    = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(3U)),                   
    RCU_USBHS0_SLP      = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(14U)),                  
    RCU_USBHS0ULPI_SLP  = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(15U)),                  
    RCU_SRAM0_SLP       = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(16U)),                  
    RCU_SRAM1_SLP       = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(17U)),                  
    RCU_DMA0_SLP        = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(21U)),                  
    RCU_DMA1_SLP        = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(22U)),                  
    RCU_DMAMUX_SLP      = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(23U)),                  
    RCU_ENET0_SLP       = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(25U)),                  
    RCU_ENET0TX_SLP     = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(26U)),                  
    RCU_ENET0RX_SLP     = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(27U)),                  
    RCU_ENET0PTP_SLP    = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(28U)),                  
    RCU_USBHS1_SLP      = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(29U)),                  
    RCU_USBHS1ULPI_SLP  = (((uint32_t)(((uint32_t)0x00000050U)) << 6) | (uint32_t)(30U)),                  
     
    RCU_DCI_SLP         = (((uint32_t)(((uint32_t)0x00000054U)) << 6) | (uint32_t)(0U)),                   
    RCU_FAC_SLP         = (((uint32_t)(((uint32_t)0x00000054U)) << 6) | (uint32_t)(1U)),                   
    RCU_SDIO1_SLP       = (((uint32_t)(((uint32_t)0x00000054U)) << 6) | (uint32_t)(2U)),                   
    RCU_CAU_SLP         = (((uint32_t)(((uint32_t)0x00000054U)) << 6) | (uint32_t)(3U)),                   
    RCU_HAU_SLP         = (((uint32_t)(((uint32_t)0x00000054U)) << 6) | (uint32_t)(4U)),                   
    RCU_TRNG_SLP        = (((uint32_t)(((uint32_t)0x00000054U)) << 6) | (uint32_t)(6U)),                   
    RCU_TMU_SLP         = (((uint32_t)(((uint32_t)0x00000054U)) << 6) | (uint32_t)(7U)),                   
    RCU_RAMECCMU1_SLP   = (((uint32_t)(((uint32_t)0x00000054U)) << 6) | (uint32_t)(8U)),                   
     
    RCU_EXMC_SLP        = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(0U)),                   
    RCU_IPA_SLP         = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(1U)),                   
    RCU_SDIO0_SLP       = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(2U)),                   
    RCU_MDMA_SLP        = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(3U)),                   
    RCU_OSPIM_SLP       = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(4U)),                   
    RCU_OSPI0_SLP       = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(5U)),                   
    RCU_OSPI1_SLP       = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(6U)),                   
    RCU_RTDEC0_SLP      = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(8U)),                   
    RCU_RTDEC1_SLP      = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(9U)),                   
    RCU_RAMECCMU0_SLP   = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(10U)),                  
    RCU_AXISRAM_SLP     = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(14U)),                  
    RCU_FMC_SLP         = (((uint32_t)(((uint32_t)0x00000058U)) << 6) | (uint32_t)(15U)),                  
     
    RCU_GPIOA_SLP       = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(0U)),                   
    RCU_GPIOB_SLP       = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(1U)),                   
    RCU_GPIOC_SLP       = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(2U)),                   
    RCU_GPIOD_SLP       = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(3U)),                   
    RCU_GPIOE_SLP       = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(4U)),                   
    RCU_GPIOF_SLP       = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(5U)),                   
    RCU_GPIOG_SLP       = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(6U)),                   
    RCU_GPIOH_SLP       = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(7U)),                   
    RCU_GPIOJ_SLP       = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(8U)),                   
    RCU_GPIOK_SLP       = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(9U)),                   
    RCU_BKPSRAM_SLP     = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(13U)),                  
    RCU_CRC_SLP         = (((uint32_t)(((uint32_t)0x0000005CU)) << 6) | (uint32_t)(14U)),                  
     
    RCU_TIMER1_SLP      = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(0U)),                   
    RCU_TIMER2_SLP      = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(1U)),                   
    RCU_TIMER3_SLP      = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(2U)),                   
    RCU_TIMER4_SLP      = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(3U)),                   
    RCU_TIMER5_SLP      = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(4U)),                   
    RCU_TIMER6_SLP      = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(5U)),                   
    RCU_TIMER22_SLP     = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(6U)),                   
    RCU_TIMER23_SLP     = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(7U)),                   
    RCU_TIMER30_SLP     = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(8U)),                   
    RCU_TIMER31_SLP     = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(9U)),                   
    RCU_TIMER50_SLP     = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(10U)),                  
    RCU_TIMER51_SLP     = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(11U)),                  
    RCU_RSPDIF_SLP      = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(13U)),                  
    RCU_SPI1_SLP        = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(14U)),                  
    RCU_SPI2_SLP        = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(15U)),                  
    RCU_MDIO_SLP        = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(16U)),                  
    RCU_USART1_SLP      = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(17U)),                  
    RCU_USART2_SLP      = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(18U)),                  
    RCU_UART3_SLP       = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(19U)),                  
    RCU_UART4_SLP       = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(20U)),                  
    RCU_I2C0_SLP        = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(21U)),                  
    RCU_I2C1_SLP        = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(22U)),                  
    RCU_I2C2_SLP        = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(23U)),                  
    RCU_I2C3_SLP        = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(24U)),                  
    RCU_CTC_SLP         = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(27U)),                  
    RCU_DACHOLD_SLP     = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(28U)),                  
    RCU_DAC_SLP         = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(29U)),                  
    RCU_UART6_SLP       = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(30U)),                  
    RCU_UART7_SLP       = (((uint32_t)(((uint32_t)0x00000060U)) << 6) | (uint32_t)(31U)),                  
     
    RCU_TIMER0_SLP      = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(0U)),                   
    RCU_TIMER7_SLP      = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(1U)),                   
    RCU_USART0_SLP      = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(4U)),                   
    RCU_USART5_SLP      = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(5U)),                   
    RCU_ADC0_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(8U)),                   
    RCU_ADC1_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(9U)),                   
    RCU_ADC2_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(10U)),                  
    RCU_SPI0_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(12U)),                  
    RCU_SPI3_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(13U)),                  
    RCU_TIMER14_SLP     = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(16U)),                  
    RCU_TIMER15_SLP     = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(17U)),                  
    RCU_TIMER16_SLP     = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(18U)),                  
    RCU_HPDF_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(19U)),                  
    RCU_SPI4_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(20U)),                  
    RCU_SPI5_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(21U)),                  
    RCU_SAI0_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(22U)),                  
    RCU_SAI1_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(23U)),                  
    RCU_SAI2_SLP        = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(24U)),                  
    RCU_TIMER40_SLP     = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(25U)),                  
    RCU_TIMER41_SLP     = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(26U)),                  
    RCU_TIMER42_SLP     = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(27U)),                  
    RCU_TIMER43_SLP     = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(28U)),                  
    RCU_TIMER44_SLP     = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(29U)),                  
    RCU_EDOUT_SLP       = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(30U)),                  
    RCU_TRIGSEL_SLP     = (((uint32_t)(((uint32_t)0x00000064U)) << 6) | (uint32_t)(31U)),                  
     
    RCU_TLI_SLP         = (((uint32_t)(((uint32_t)0x00000068U)) << 6) | (uint32_t)(0U)),                   
    RCU_WWDGT_SLP       = (((uint32_t)(((uint32_t)0x00000068U)) << 6) | (uint32_t)(1U)),                   
     
    RCU_SYSCFG_SLP      = (((uint32_t)(((uint32_t)0x0000006CU)) << 6) | (uint32_t)(0U)),                   
    RCU_CMP_SLP         = (((uint32_t)(((uint32_t)0x0000006CU)) << 6) | (uint32_t)(1U)),                   
    RCU_VREF_SLP        = (((uint32_t)(((uint32_t)0x0000006CU)) << 6) | (uint32_t)(2U)),                   
    RCU_LPDTS_SLP       = (((uint32_t)(((uint32_t)0x0000006CU)) << 6) | (uint32_t)(3U)),                   
    RCU_PMU_SLP         = (((uint32_t)(((uint32_t)0x0000006CU)) << 6) | (uint32_t)(4U)),                   
     
    RCU_CAN0_SLP        = (((uint32_t)(((uint32_t)0x000000E8U)) << 6) | (uint32_t)(0U)),               
    RCU_CAN1_SLP        = (((uint32_t)(((uint32_t)0x000000E8U)) << 6) | (uint32_t)(1U)),               
    RCU_CAN2_SLP        = (((uint32_t)(((uint32_t)0x000000E8U)) << 6) | (uint32_t)(2U))                
} rcu_periph_sleep_enum;

 
typedef enum {
     
    RCU_ENET1RST     = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(0U)),                       
    RCU_USBHS0RST    = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(14U)),                      
    RCU_DMA0RST      = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(21U)),                      
    RCU_DMA1RST      = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(22U)),                      
    RCU_DMAMUXRST    = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(23U)),                      
    RCU_ENET0RST     = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(25U)),                      
    RCU_USBHS1RST    = (((uint32_t)(((uint32_t)0x00000010U)) << 6) | (uint32_t)(29U)),                      
     
    RCU_DCIRST       = (((uint32_t)(((uint32_t)0x00000014U)) << 6) | (uint32_t)(0U)),                       
    RCU_FACRST       = (((uint32_t)(((uint32_t)0x00000014U)) << 6) | (uint32_t)(1U)),                       
    RCU_SDIO1RST     = (((uint32_t)(((uint32_t)0x00000014U)) << 6) | (uint32_t)(2U)),                       
    RCU_CAURST       = (((uint32_t)(((uint32_t)0x00000014U)) << 6) | (uint32_t)(3U)),                       
    RCU_HAURST       = (((uint32_t)(((uint32_t)0x00000014U)) << 6) | (uint32_t)(4U)),                       
    RCU_TRNGRST      = (((uint32_t)(((uint32_t)0x00000014U)) << 6) | (uint32_t)(6U)),                       
    RCU_TMURST       = (((uint32_t)(((uint32_t)0x00000014U)) << 6) | (uint32_t)(7U)),                       
     
    RCU_EXMCRST      = (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(0U)),                       
    RCU_IPARST       = (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(1U)),                       
    RCU_SDIO0RST     = (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(2U)),                       
    RCU_MDMARST      = (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(3U)),                       
    RCU_OSPIMRST     = (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(4U)),                       
    RCU_OSPI0RST     = (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(5U)),                       
    RCU_OSPI1RST     = (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(6U)),                       
    RCU_RTDEC0RST    = (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(8U)),                       
    RCU_RTDEC1RST    = (((uint32_t)(((uint32_t)0x00000018U)) << 6) | (uint32_t)(9U)),                       
     
    RCU_GPIOARST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(0U)),                       
    RCU_GPIOBRST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(1U)),                       
    RCU_GPIOCRST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(2U)),                       
    RCU_GPIODRST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(3U)),                       
    RCU_GPIOERST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(4U)),                       
    RCU_GPIOFRST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(5U)),                       
    RCU_GPIOGRST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(6U)),                       
    RCU_GPIOHRST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(7U)),                       
    RCU_GPIOJRST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(8U)),                       
    RCU_GPIOKRST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(9U)),                       
    RCU_CRCRST       = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(14U)),                      
    RCU_HWSEMRST     = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(15U)),                      
     
    RCU_TIMER1RST    = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(0U)),                       
    RCU_TIMER2RST    = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(1U)),                       
    RCU_TIMER3RST    = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(2U)),                       
    RCU_TIMER4RST    = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(3U)),                       
    RCU_TIMER5RST    = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(4U)),                       
    RCU_TIMER6RST    = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(5U)),                       
    RCU_TIMER22RST   = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(6U)),                       
    RCU_TIMER23RST   = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(7U)),                       
    RCU_TIMER30RST   = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(8U)),                       
    RCU_TIMER31RST   = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(9U)),                       
    RCU_TIMER50RST   = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(10U)),                      
    RCU_TIMER51RST   = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(11U)),                      
    RCU_RSPDIFRST    = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(13U)),                      
    RCU_SPI1RST      = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(14U)),                      
    RCU_SPI2RST      = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(15U)),                      
    RCU_MDIORST      = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(16U)),                      
    RCU_USART1RST    = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(17U)),                      
    RCU_USART2RST    = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(18U)),                      
    RCU_UART3RST     = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(19U)),                      
    RCU_UART4RST     = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(20U)),                      
    RCU_I2C0RST      = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(21U)),                      
    RCU_I2C1RST      = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(22U)),                      
    RCU_I2C2RST      = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(23U)),                      
    RCU_I2C3RST      = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(24U)),                      
    RCU_CTCRST       = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(27U)),                      
    RCU_DACHOLDRST   = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(28U)),                      
    RCU_DACRST       = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(29U)),                      
    RCU_UART6RST     = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(30U)),                      
    RCU_UART7RST     = (((uint32_t)(((uint32_t)0x00000020U)) << 6) | (uint32_t)(31U)),                      
     
    RCU_TIMER0RST    = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(0U)),                       
    RCU_TIMER7RST    = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(1U)),                       
    RCU_USART0RST    = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(4U)),                       
    RCU_USART5RST    = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(5U)),                       
    RCU_ADC0RST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(8U)),                       
    RCU_ADC1RST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(9U)),                       
    RCU_ADC2RST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(10U)),                      
    RCU_SPI0RST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(12U)),                      
    RCU_SPI3RST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(13U)),                      
    RCU_TIMER14RST   = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(16U)),                      
    RCU_TIMER15RST   = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(17U)),                      
    RCU_TIMER16RST   = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(18U)),                      
    RCU_HPDFRST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(19U)),                      
    RCU_SPI4RST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(20U)),                      
    RCU_SPI5RST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(21U)),                      
    RCU_SAI0RST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(22U)),                      
    RCU_SAI1RST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(23U)),                      
    RCU_SAI2RST      = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(24U)),                      
    RCU_TIMER40RST   = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(25U)),                      
    RCU_TIMER41RST   = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(26U)),                      
    RCU_TIMER42RST   = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(27U)),                      
    RCU_TIMER43RST   = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(28U)),                      
    RCU_TIMER44RST   = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(29U)),                      
    RCU_EDOUTRST     = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(30U)),                      
    RCU_TRIGSELRST   = (((uint32_t)(((uint32_t)0x00000024U)) << 6) | (uint32_t)(31U)),                      
     
    RCU_TLIRST       = (((uint32_t)(((uint32_t)0x00000028U)) << 6) | (uint32_t)(0U)),                       
    RCU_WWDGTRST     = (((uint32_t)(((uint32_t)0x00000028U)) << 6) | (uint32_t)(1U)),                       
     
    RCU_SYSCFGRST    = (((uint32_t)(((uint32_t)0x0000002CU)) << 6) | (uint32_t)(0U)),                       
    RCU_CMPRST       = (((uint32_t)(((uint32_t)0x0000002CU)) << 6) | (uint32_t)(1U)),                       
    RCU_VREFRST      = (((uint32_t)(((uint32_t)0x0000002CU)) << 6) | (uint32_t)(2U)),                       
    RCU_LPDTSRST     = (((uint32_t)(((uint32_t)0x0000002CU)) << 6) | (uint32_t)(3U)),                       
    RCU_PMURST       = (((uint32_t)(((uint32_t)0x0000002CU)) << 6) | (uint32_t)(4U)),                       
     
    RCU_CAN0RST      = (((uint32_t)(((uint32_t)0x000000E0U)) << 6) | (uint32_t)(0U)),                   
    RCU_CAN1RST      = (((uint32_t)(((uint32_t)0x000000E0U)) << 6) | (uint32_t)(1U)),                   
    RCU_CAN2RST      = (((uint32_t)(((uint32_t)0x000000E0U)) << 6) | (uint32_t)(2U))                    
} rcu_periph_reset_enum;

 
typedef enum {
     
    RCU_FLAG_IRC64MSTB     = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(31U)),                    
    RCU_FLAG_HXTALSTB      = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(17U)),                    
    RCU_FLAG_PLL0STB       = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(25U)),                    
    RCU_FLAG_PLL1STB       = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(27U)),                    
    RCU_FLAG_PLL2STB       = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(29U)),                    
    RCU_FLAG_LXTALSTB      = (((uint32_t)(((uint32_t)0x00000070U)) << 6) | (uint32_t)(1U)),                   
    RCU_FLAG_IRC32KSTB     = (((uint32_t)(((uint32_t)0x00000074U)) << 6) | (uint32_t)(1U)),                  
    RCU_FLAG_IRC48MSTB     = (((uint32_t)(((uint32_t)0x000000C0U)) << 6) | (uint32_t)(17U)),                
    RCU_FLAG_LPIRC4MSTB    = (((uint32_t)(((uint32_t)0x000000C4U)) << 6) | (uint32_t)(1U)),                 
    RCU_FLAG_PLLUSBHS0STB  = (((uint32_t)(((uint32_t)0x000000C4U)) << 6) | (uint32_t)(29U)),                
    RCU_FLAG_PLLUSBHS1STB  = (((uint32_t)(((uint32_t)0x000000C4U)) << 6) | (uint32_t)(31U)),                
     
    RCU_FLAG_LCKMD         = (((uint32_t)(((uint32_t)0x00000070U)) << 6) | (uint32_t)(6U)),                   
     
    RCU_FLAG_BORRST        = (((uint32_t)(((uint32_t)0x00000074U)) << 6) | (uint32_t)(25U)),                 
    RCU_FLAG_EPRST         = (((uint32_t)(((uint32_t)0x00000074U)) << 6) | (uint32_t)(26U)),                 
    RCU_FLAG_PORRST        = (((uint32_t)(((uint32_t)0x00000074U)) << 6) | (uint32_t)(27U)),                 
    RCU_FLAG_SWRST         = (((uint32_t)(((uint32_t)0x00000074U)) << 6) | (uint32_t)(28U)),                 
    RCU_FLAG_FWDGTRST      = (((uint32_t)(((uint32_t)0x00000074U)) << 6) | (uint32_t)(29U)),                 
    RCU_FLAG_WWDGTRST      = (((uint32_t)(((uint32_t)0x00000074U)) << 6) | (uint32_t)(30U)),                 
    RCU_FLAG_LPRST         = (((uint32_t)(((uint32_t)0x00000074U)) << 6) | (uint32_t)(31U))                  
} rcu_flag_enum;

 
typedef enum {
    RCU_INT_FLAG_IRC32KSTB      = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(0U)),                
    RCU_INT_FLAG_LXTALSTB       = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(1U)),                
    RCU_INT_FLAG_IRC64MSTB      = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(2U)),                
    RCU_INT_FLAG_HXTALSTB       = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(3U)),                
    RCU_INT_FLAG_PLL0STB        = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(4U)),                
    RCU_INT_FLAG_PLL1STB        = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(5U)),                
    RCU_INT_FLAG_PLL2STB        = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(6U)),                
    RCU_INT_FLAG_CKM            = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(7U)),                
    RCU_INT_FLAG_LCKM           = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(27U)),               
    RCU_INT_FLAG_LPIRC4MSTB     = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(24U)),               
    RCU_INT_FLAG_IRC48MSTB      = (((uint32_t)(((uint32_t)0x000000CCU)) << 6) | (uint32_t)(6U)),             
    RCU_INT_FLAG_PLLUSBHS0STB   = (((uint32_t)(((uint32_t)0x000000CCU)) << 6) | (uint32_t)(4U)),             
    RCU_INT_FLAG_PLLUSBHS1STB   = (((uint32_t)(((uint32_t)0x000000CCU)) << 6) | (uint32_t)(5U))              
} rcu_int_flag_enum;

 
typedef enum {
    RCU_INT_FLAG_IRC32KSTB_CLR      = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(16U)),           
    RCU_INT_FLAG_LXTALSTB_CLR       = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(17U)),           
    RCU_INT_FLAG_IRC64MSTB_CLR      = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(18U)),           
    RCU_INT_FLAG_HXTALSTB_CLR       = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(19U)),           
    RCU_INT_FLAG_PLL0STB_CLR        = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(20U)),           
    RCU_INT_FLAG_PLL1STB_CLR        = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(21U)),           
    RCU_INT_FLAG_PLL2STB_CLR        = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(22U)),           
    RCU_INT_FLAG_CKM_CLR            = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(23U)),           
    RCU_INT_FLAG_LCKM_CLR           = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(28U)),           
    RCU_INT_FLAG_LPIRC4MSTB_CLR     = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(26U)),           
    RCU_INT_FLAG_IRC48MSTB_CLR      = (((uint32_t)(((uint32_t)0x000000CCU)) << 6) | (uint32_t)(22U)),        
    RCU_INT_FLAG_PLLUSBHS0STB_CLR   = (((uint32_t)(((uint32_t)0x000000CCU)) << 6) | (uint32_t)(20U)),        
    RCU_INT_FLAG_PLLUSBHS1STB_CLR   = (((uint32_t)(((uint32_t)0x000000CCU)) << 6) | (uint32_t)(21U))         
} rcu_int_flag_clear_enum;

 
typedef enum {
    RCU_INT_IRC32KSTB               = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(8U)),            
    RCU_INT_LXTALSTB                = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(9U)),            
    RCU_INT_IRC64MSTB               = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(10U)),           
    RCU_INT_HXTALSTB                = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(11U)),           
    RCU_INT_PLL0STB                 = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(12U)),           
    RCU_INT_PLL1STB                 = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(13U)),           
    RCU_INT_PLL2STB                 = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(14U)),           
    RCU_INT_IRC48MSTB               = (((uint32_t)(((uint32_t)0x000000CCU)) << 6) | (uint32_t)(14U)),        
    RCU_INT_LPIRC4MSTB              = (((uint32_t)(((uint32_t)0x0000000CU)) << 6) | (uint32_t)(25U)),           
    RCU_INT_PLLUSBHS0STB            = (((uint32_t)(((uint32_t)0x000000CCU)) << 6) | (uint32_t)(12U)),        
    RCU_INT_PLLUSBHS1STB            = (((uint32_t)(((uint32_t)0x000000CCU)) << 6) | (uint32_t)(13U))         
} rcu_int_enum;

 
typedef enum {
    RCU_HXTAL                       = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(16U)),           
    RCU_LXTAL                       = (((uint32_t)(((uint32_t)0x00000070U)) << 6) | (uint32_t)(0U)),          
    RCU_IRC64M                      = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(30U)),           
    RCU_IRC48M                      = (((uint32_t)(((uint32_t)0x000000C0U)) << 6) | (uint32_t)(16U)),       
    RCU_IRC32K                      = (((uint32_t)(((uint32_t)0x00000074U)) << 6) | (uint32_t)(0U)),         
    RCU_LPIRC4M                     = (((uint32_t)(((uint32_t)0x000000C4U)) << 6) | (uint32_t)(0U)),        
    RCU_PLL0_CK                     = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(24U)),           
    RCU_PLL1_CK                     = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(26U)),           
    RCU_PLL2_CK                     = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(28U)),           
    RCU_PLLUSBHS0_CK                = (((uint32_t)(((uint32_t)0x000000C4U)) << 6) | (uint32_t)(28U)),       
    RCU_PLLUSBHS1_CK                = (((uint32_t)(((uint32_t)0x000000C4U)) << 6) | (uint32_t)(30U))        
} rcu_osci_type_enum;

 
typedef enum {
    CK_SYS = 0U,                                                                     
    CK_AHB,                                                                          
    CK_APB1,                                                                         
    CK_APB2,                                                                         
    CK_APB3,                                                                         
    CK_APB4,                                                                         
    CK_PLL0P,                                                                        
    CK_PLL0Q,                                                                        
    CK_PLL0R,                                                                        
    CK_PLL1P,                                                                        
    CK_PLL1Q,                                                                        
    CK_PLL1R,                                                                        
    CK_PLL2P,                                                                        
    CK_PLL2Q,                                                                        
    CK_PLL2R,                                                                        
    CK_PER,                                                                          
    CK_USART0,                                                                       
    CK_USART1,                                                                       
    CK_USART2,                                                                       
    CK_USART5,                                                                       
    CK_IRC64MDIV,                                                                    
    CK_HXTAL,                                                                        
    CK_LPIRC4M                                                                       
} rcu_clock_freq_enum;

typedef enum {
    IDX_USART0 = 0U,                                                                 
    IDX_USART1,                                                                      
    IDX_USART2,                                                                      
    IDX_USART5                                                                       
} usart_idx_enum;

typedef enum {
    IDX_I2C0 = 0U,                                                                   
    IDX_I2C1,                                                                        
    IDX_I2C2,                                                                        
    IDX_I2C3,                                                                        
} i2c_idx_enum;

typedef enum {
    IDX_CAN0 = 0U,                                                                   
    IDX_CAN1,                                                                        
    IDX_CAN2,                                                                        
} can_idx_enum;

typedef enum {
    IDX_SAI0 = 0U,                                                                   
    IDX_SAI1                                                                         
} sai_idx_enum;

typedef enum {
    IDX_SAI2B0 = 0U,                                                                 
    IDX_SAI2B1                                                                       
} sai2b_idx_enum;

typedef enum {
    IDX_ADC0 = 0U,                                                                   
    IDX_ADC1,                                                                        
    IDX_ADC2                                                                         
} adc_idx_enum;

typedef enum {
    IDX_USBHS0 = 0U,                                                                 
    IDX_USBHS1                                                                       
} usbhs_idx_enum;

typedef enum {
    IDX_PLL0 = 0U,                                                                   
    IDX_PLL1,                                                                        
    IDX_PLL2                                                                         
} pll_idx_enum;

typedef enum {
    IDX_SDIO0 = 0U,                                                                  
    IDX_SDIO1                                                                        
} sdio_idx_enum;

typedef enum {
    IDX_SPI0 = 0U,                                                                   
    IDX_SPI1,                                                                        
    IDX_SPI2,                                                                        
    IDX_SPI3,                                                                        
    IDX_SPI4,                                                                        
    IDX_SPI5                                                                         
} spi_idx_enum;

 
 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 

 

 

 


 
 

 

 
 

 

 

 

 

 
 

 

 

 

 



 

 

 

 

 

 

 
 

 

 

 

 

 

 

 

 

 
 
 
void rcu_deinit(void);
 
void rcu_periph_clock_enable(rcu_periph_enum periph);
 
void rcu_periph_clock_disable(rcu_periph_enum periph);
 
void rcu_periph_clock_sleep_enable(rcu_periph_sleep_enum periph);
 
void rcu_periph_clock_sleep_disable(rcu_periph_sleep_enum periph);
 
void rcu_periph_reset_enable(rcu_periph_reset_enum periph_reset);
 
void rcu_periph_reset_disable(rcu_periph_reset_enum periph_reset);
 
void rcu_bkp_reset_enable(void);
 
void rcu_bkp_reset_disable(void);

 
 
void rcu_system_clock_source_config(uint32_t ck_sys);
 
uint32_t rcu_system_clock_source_get(void);
 
void rcu_ahb_clock_config(uint32_t ck_ahb);
 
void rcu_apb1_clock_config(uint32_t ck_apb1);
 
void rcu_apb2_clock_config(uint32_t ck_apb2);
 
void rcu_apb3_clock_config(uint32_t ck_apb3);
 
void rcu_apb4_clock_config(uint32_t ck_apb4);
 
void rcu_ckout0_config(uint32_t ckout0_src, uint32_t ckout0_div);
 
void rcu_ckout1_config(uint32_t ckout1_src, uint32_t ckout1_div);

 
void rcu_pll_input_output_clock_range_config(pll_idx_enum pll_idx, uint32_t ck_input, uint32_t ck_output);
 
void rcu_pll_fractional_config(pll_idx_enum pll_idx, uint32_t pll_fracn);
 
void rcu_pll_fractional_latch_enable(pll_idx_enum pll_idx);
 
void rcu_pll_fractional_latch_disable(pll_idx_enum pll_idx);
 
void rcu_pll_source_config(uint32_t pll_src);
 
ErrStatus rcu_pll0_config(uint32_t pll0_psc, uint32_t pll0_n, uint32_t pll0_p, uint32_t pll0_q, uint32_t pll0_r);
 
ErrStatus rcu_pll1_config(uint32_t pll1_psc, uint32_t pll1_n, uint32_t pll1_p, uint32_t pll1_q, uint32_t pll1_r);
 
ErrStatus rcu_pll2_config(uint32_t pll2_psc, uint32_t pll2_n, uint32_t pll2_p, uint32_t pll2_q, uint32_t pll2_r);
 
void rcu_pll_clock_output_enable(uint32_t pllxy);
 
void rcu_pll_clock_output_disable(uint32_t pllxy);
 
void rcu_pllusb0_config(uint32_t pllusb_presel, uint32_t pllusb_predv, uint32_t pllusb_mf, uint32_t usbhsdv);
 
void rcu_pllusb1_config(uint32_t pllusb_presel, uint32_t pllusb_predv, uint32_t pllusb_mf, uint32_t usbhsdv);
 
void rcu_rtc_clock_config(uint32_t rtc_clock_source);
 
void rcu_rtc_div_config(uint32_t rtc_div);
 
void rcu_ck48m_clock_config(uint32_t ck48m_clock_source);
 
void rcu_pll48m_clock_config(uint32_t pll48m_clock_source);
 
void rcu_irc64mdiv_clock_config(uint32_t ck_irc64mdiv);
 
uint32_t rcu_irc64mdiv_freq_get(void);
 
void rcu_timer_clock_prescaler_config(uint32_t timer_clock_prescaler);
 
void rcu_spi_clock_config(spi_idx_enum spi_idx, uint32_t ck_spi);
 
void rcu_sdio_clock_config(sdio_idx_enum sdio_idx, uint32_t ck_sdio);
 
void rcu_deepsleep_wakeup_sys_clock_config(uint32_t ck_dspwussel);
 
void rcu_tli_clock_div_config(uint32_t pll2_r_div);
 
void rcu_usart_clock_config(usart_idx_enum usart_idx, uint32_t ck_usart);
 
void rcu_i2c_clock_config(i2c_idx_enum i2c_idx, uint32_t ck_i2c);
 
void rcu_can_clock_config(can_idx_enum can_idx, uint32_t ck_can);
 
void rcu_adc_clock_config(adc_idx_enum adc_idx, uint32_t ck_adc);
 
void rcu_sai_clock_config(sai_idx_enum sai_idx, uint32_t ck_sai);
 
void rcu_sai2_block_clock_config(sai2b_idx_enum sai2b_idx, uint32_t ck_sai2b);
 
void rcu_rspdif_clock_config(uint32_t ck_rspdif);
 
void rcu_exmc_clock_config(uint32_t ck_exmc);
 
void rcu_hpdf_clock_config(uint32_t ck_hpdf);
 
void rcu_per_clock_config(uint32_t ck_per);
 
void rcu_usbhs_pll1qpsc_config(usbhs_idx_enum usbhs_idx, uint32_t ck_usbhspsc);
 
void rcu_usb48m_clock_config(usbhs_idx_enum usbhs_idx, uint32_t ck_usb48m);
 
void rcu_usbhs_clock_config(usbhs_idx_enum usbhs_idx, uint32_t ck_usbhs);
 
void rcu_usbhs_clock_selection_enable(usbhs_idx_enum usbhs_idx);
 
void rcu_usbhs_clock_selection_disable(usbhs_idx_enum usbhs_idx);

 
 
void rcu_lxtal_drive_capability_config(uint32_t lxtal_dricap);
 
ErrStatus rcu_osci_stab_wait(rcu_osci_type_enum osci);
 
void rcu_osci_on(rcu_osci_type_enum osci);
 
void rcu_osci_off(rcu_osci_type_enum osci);
 
void rcu_osci_bypass_mode_enable(rcu_osci_type_enum osci);
 
void rcu_osci_bypass_mode_disable(rcu_osci_type_enum osci);
 
void rcu_irc64m_adjust_value_set(uint32_t irc64m_adjval);
 
void rcu_lpirc4m_adjust_value_set(uint32_t lpirc4m_adjval);

 
 
void rcu_hxtal_clock_monitor_enable(void);
 
void rcu_hxtal_clock_monitor_disable(void);
 
void rcu_lxtal_clock_monitor_enable(void);
 
void rcu_lxtal_clock_monitor_disable(void);

 
 
uint32_t rcu_clock_freq_get(rcu_clock_freq_enum clock);

 
 
FlagStatus rcu_flag_get(rcu_flag_enum flag);
 
void rcu_all_reset_flag_clear(void);
 
void rcu_interrupt_enable(rcu_int_enum interrupt);
 
void rcu_interrupt_disable(rcu_int_enum interrupt);
 
FlagStatus rcu_interrupt_flag_get(rcu_int_flag_enum int_flag);
 
void rcu_interrupt_flag_clear(rcu_int_flag_clear_enum int_flag);






 


























 



 

 


 
 

 

 

 

 
 

 

 

 

 

 
 
typedef struct {
    uint32_t input_sel;                                                              
    uint32_t max_retrie;                                                             
    uint32_t wait_activity;                                                          
    uint32_t channel_sel;                                                            
    uint32_t sample_format;                                                          
    uint32_t sound_mode;                                                             
    uint32_t pre_type;                                                               
    uint32_t channel_status_bit;                                                     
    uint32_t validity_bit;                                                           
    uint32_t parity_error_bit;                                                       
    uint32_t symbol_clk;                                                             
    uint32_t bak_symbol_clk;                                                         
} rspdif_parameter_struct;

 
typedef struct {
    uint32_t format;                                                                 
    uint32_t preamble;                                                               
    uint32_t channel_status;                                                         
    uint32_t user_bit;                                                               
    uint32_t validity;                                                               
    uint32_t parity_err;                                                             
    uint32_t data0;                                                                  
    uint32_t data1;                                                                  
} rspdif_data_struct;

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
 
void rspdif_deinit(void);
 
void rspdif_struct_para_init(rspdif_parameter_struct *rspdif_struct);
 
void rspdif_init(rspdif_parameter_struct *rspdif_struct);
 
void rspdif_enable(uint32_t mode);
 
void rspdif_disable(void);

 
 
void rspdif_symbol_clock_enable(void);
 
void rspdif_symbol_clock_disable(void);
 
void rspdif_backup_symbol_clock_enable(void);
 
void rspdif_backup_symbol_clock_disable(void);

 
 
void rspdif_dma_enable(void);
 
void rspdif_dma_disable(void);
 
void rspdif_control_buffer_dma_enable(void);
 
void rspdif_control_buffer_dma_disable(void);

 
 
void rspdif_data_read(rspdif_data_struct *data_struct);

 
 
uint32_t rspdif_duration_of_symbols_get(void);
 
uint32_t rspdif_user_data_get(void);
 
uint32_t rspdif_channel_status_get(void);
 
FlagStatus rspdif_start_block_status_get(void);
 
uint32_t rspdif_low_threshold_get(void);
 
uint32_t rspdif_high_threshold_get(void);

 
 
FlagStatus rspdif_flag_get(uint16_t flag);
 
void rspdif_flag_clear(uint16_t flag);
 
void rspdif_interrupt_enable(uint8_t interrupt);
 
void rspdif_interrupt_disable(uint8_t interrupt);
 
FlagStatus rspdif_interrupt_flag_get(uint16_t int_flag);
 
void rspdif_interrupt_flag_clear(uint16_t int_flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
typedef struct {
    uint8_t year;                                                                       
    uint8_t month;                                                                      
    uint8_t date;                                                                       
    uint8_t day_of_week;                                                                
    uint8_t hour;                                                                       
    uint8_t minute;                                                                     
    uint8_t second;                                                                     
    uint16_t factor_asyn;                                                               
    uint16_t factor_syn;                                                                
    uint32_t am_pm;                                                                     
    uint32_t display_format;                                                            
} rtc_parameter_struct;

 
typedef struct {
    uint32_t alarm_mask;                                                                
    uint32_t weekday_or_date;                                                           
    uint8_t alarm_day;                                                                  
    uint8_t alarm_hour;                                                                 
    uint8_t alarm_minute;                                                               
    uint8_t alarm_second;                                                               
    uint32_t am_pm;                                                                     
} rtc_alarm_struct;

 
typedef struct {
    uint8_t timestamp_month;                                                            
    uint8_t timestamp_date;                                                             
    uint8_t timestamp_day;                                                              
    uint8_t timestamp_hour;                                                             
    uint8_t timestamp_minute;                                                           
    uint8_t timestamp_second;                                                           
    uint32_t am_pm;                                                                     
} rtc_timestamp_struct;

 
typedef struct {
    uint32_t tamper_source;                                                             
    uint32_t tamper_trigger;                                                            
    uint32_t tamper_filter;                                                             
    uint32_t tamper_sample_frequency;                                                   
    ControlStatus tamper_precharge_enable;                                              
    uint32_t tamper_precharge_time;                                                     
    ControlStatus tamper_with_timestamp;                                                
} rtc_tamper_struct;

 




 











 


 






 

 

 


 



 



 

 










 


 

 

 

 

 

 
 
 
ErrStatus rtc_deinit(void);
 
ErrStatus rtc_para_init(rtc_parameter_struct *rtc_initpara_struct);
 
ErrStatus rtc_init_mode_enter(void);
 
void rtc_init_mode_exit(void);
 
ErrStatus rtc_register_sync_wait(void);

 
void rtc_current_time_get(rtc_parameter_struct *rtc_initpara_struct);
 
uint32_t rtc_subsecond_get(void);

 
 
void rtc_alarm_config(uint8_t rtc_alarm, rtc_alarm_struct *rtc_alarm_time);
 
void rtc_alarm_subsecond_config(uint8_t rtc_alarm, uint32_t mask_subsecond, uint32_t subsecond);
 
void rtc_alarm_get(uint8_t rtc_alarm, rtc_alarm_struct *rtc_alarm_time);
 
uint32_t rtc_alarm_subsecond_get(uint8_t rtc_alarm);
 
void rtc_alarm_enable(uint8_t rtc_alarm);
 
ErrStatus rtc_alarm_disable(uint8_t rtc_alarm);

 
 
void rtc_timestamp_enable(uint32_t edge);
 
void rtc_timestamp_disable(void);
 
void rtc_timestamp_get(rtc_timestamp_struct *rtc_timestamp);
 
void rtc_timestamp_internalevent_config(uint32_t mode);
 
uint32_t rtc_timestamp_subsecond_get(void);

 
void rtc_tamper_enable(rtc_tamper_struct *rtc_tamper);
 
void rtc_tamper_disable(uint32_t source);

 
void rtc_output_pin_select(uint32_t outputpin);
 
void rtc_alarm_output_config(uint32_t source, uint32_t mode);
 
void rtc_calibration_output_config(uint32_t source);

 
void rtc_hour_adjust(uint32_t operation);
 
ErrStatus rtc_second_adjust(uint32_t add, uint32_t minus);

 
void rtc_bypass_shadow_enable(void);
 
void rtc_bypass_shadow_disable(void);

 
ErrStatus rtc_refclock_detection_enable(void);
 
ErrStatus rtc_refclock_detection_disable(void);

 
void rtc_wakeup_enable(void);
 
ErrStatus rtc_wakeup_disable(void);
 
ErrStatus rtc_wakeup_clock_set(uint8_t wakeup_clock);
 
ErrStatus rtc_wakeup_timer_config(uint16_t wakeup_timer);
 
uint16_t rtc_wakeup_timer_get(void);

 
ErrStatus rtc_smooth_calibration_config(uint32_t window, uint32_t plus, uint32_t minus);

 
void rtc_interrupt_enable(uint32_t interrupt);
 
void rtc_interrupt_disable(uint32_t interrupt);
 
FlagStatus rtc_flag_get(uint32_t flag);
 
void rtc_flag_clear(uint32_t flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 
 

 

 

 

 
typedef struct {
    uint8_t access_mode;                     
    uint8_t key_crc;                         
    uint16_t fw_version;                     
    uint32_t *key;                           
    uint32_t *nonce;                         
    uint32_t start_addr;                     
    uint32_t end_addr;                       
} rtdec_parameter_struct;

 
 
 
void rtdec_deinit(uint32_t rtdec_periph);
 
void rtdec_struct_para_init(rtdec_parameter_struct* rtdec_struct);
 
ErrStatus rtdec_init(uint32_t rtdec_periph, uint32_t rtdec_area, rtdec_parameter_struct *rtdec_struct);
 
void rtdec_config(uint32_t rtdec_periph, uint32_t rtdec_area, uint8_t access_mode, uint16_t firmware_version);
 
void rtdec_lock(uint32_t rtdec_periph, uint32_t rtdec_area, uint32_t lock_type);
 
void rtdec_addr_init(uint32_t rtdec_periph, uint32_t rtdec_area, uint32_t saddr, uint32_t eaddr);
 
void rtdec_nonce_init(uint32_t rtdec_periph, uint32_t rtdec_area, uint32_t *nonce);
 
void rtdec_key_init(uint32_t rtdec_periph, uint32_t rtdec_area, uint32_t *key);
 
uint8_t rtdec_key_crc_get(uint32_t rtdec_periph, uint32_t rtdec_area);
 
void rtdec_enable(uint32_t rtdec_periph, uint32_t rtdec_area);
 
void rtdec_disable(uint32_t rtdec_periph, uint32_t rtdec_area);

 
 
FlagStatus rtdec_flag_get(uint32_t rtdec_periph, uint32_t flag);
 
void rtdec_flag_clear(uint32_t rtdec_periph, uint32_t flag);
 
void rtdec_interrupt_enable(uint32_t rtdec_periph, uint32_t interrupt);
 
void rtdec_interrupt_disable(uint32_t rtdec_periph, uint32_t interrupt);
 
FlagStatus rtdec_interrupt_flag_get(uint32_t rtdec_periph, uint32_t int_flag);
 
void rtdec_interrupt_flag_clear(uint32_t rtdec_periph, uint32_t int_flag);






 


























 



 

 


 
 

 

 

 

 

 

 

 

 

 

 

 
 
typedef struct {
    uint32_t operating_mode;                                                         
    uint32_t protocol;                                                               
    uint32_t data_width;                                                             
    uint32_t shift_dir;                                                              
    uint32_t sample_edge;                                                            
    uint32_t sync_mode;                                                              
    uint32_t output_drive;                                                           
    uint32_t clk_div_bypass;                                                         
    uint32_t mclk_div;                                                               
    uint32_t mclk_oversampling;                                                      
    uint32_t mclk_enable;                                                            
    uint32_t fifo_threshold;                                                         
} sai_parameter_struct;

 
typedef struct {
    uint32_t frame_width;                                                            
    uint32_t frame_sync_width;                                                       
    uint32_t frame_sync_function;                                                    
    uint32_t frame_sync_polarity;                                                    
    uint32_t frame_sync_offset;                                                      
} sai_frame_parameter_struct;

 
typedef struct {
    uint32_t slot_number;                                                            
    uint32_t slot_width;                                                             
    uint32_t data_offset;                                                            
    uint32_t slot_active;                                                            
} sai_slot_parameter_struct;

 
typedef enum {
    FIFO_EMPTY  = 0U,                                                                
    FIFO_EMPTY_TO_1_4_FULL,                                                          
    FIFO_1_4_FULL_TO_1_2_FULL,                                                       
    FIFO_1_2_FULL_TO_3_4_FULL,                                                       
    FIFO_3_4_FULL_TO_FULL,                                                           
    FIFO_FULL                                                                        
} sai_fifo_state_enum;

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 


 
 
 
void sai_deinit(uint32_t sai_periph);
 
void sai_struct_para_init(sai_parameter_struct *sai_init_stuct);
 
void sai_frame_struct_para_init(sai_frame_parameter_struct *sai_frame_init_struct);
 
void sai_slot_struct_para_init(sai_slot_parameter_struct *sai_slot_init_struct);
 
void sai_init(uint32_t sai_periph, uint32_t block, sai_parameter_struct *sai_struct);
 
void sai_frame_init(uint32_t sai_periph, uint32_t block, sai_frame_parameter_struct *sai_frame_struct);
 
void sai_slot_init(uint32_t sai_periph, uint32_t block, sai_slot_parameter_struct *sai_slot_struct);
 
void sai_enable(uint32_t sai_periph, uint32_t block);
 
void sai_disable(uint32_t sai_periph, uint32_t block);

 
 
void sai_sdoutput_config(uint32_t sai_periph, uint32_t block, uint32_t sdout);
 
void sai_monomode_config(uint32_t sai_periph, uint32_t block, uint32_t mono);
 
void sai_companding_config(uint32_t sai_periph, uint32_t block, uint32_t compander,
                           uint32_t complement);
 
void sai_mute_enable(uint32_t sai_periph, uint32_t block);
 
void sai_mute_disable(uint32_t sai_periph, uint32_t block);
 
void sai_mute_value_config(uint32_t sai_periph, uint32_t block, uint32_t value);
 
void sai_mute_count_config(uint32_t sai_periph, uint32_t block, uint32_t count);
 
void sai_data_transmit(uint32_t sai_periph, uint32_t block, uint32_t data);
 
uint32_t sai_data_receive(uint32_t sai_periph, uint32_t block);
 
sai_fifo_state_enum sai_fifo_status_get(uint32_t sai_periph, uint32_t block);
 
void sai_fifo_flush(uint32_t sai_periph, uint32_t block);

 
 
void sai_dma_enable(uint32_t sai_periph, uint32_t block);
 
void sai_dma_disable(uint32_t sai_periph, uint32_t block);

 
 
void sai_sync_input_config(uint32_t sai_periph, uint32_t input);
 
void sai_sync_output_config(uint32_t sai_periph, uint32_t output);

 
 
void sai_pdm_enable(uint32_t sai_periph);
 
void sai_pdm_disable(uint32_t sai_periph);
 
void sai_pdm_microphone_number_config(uint32_t sai_periph, uint32_t microphonenum);
 
void sai_pdm_delay_config(uint32_t sai_periph, uint32_t microphone, uint32_t delay);
 
void sai_pdm_clk0_enable(uint32_t sai_periph);
 
void sai_pdm_clk0_disable(uint32_t sai_periph);
 
void sai_pdm_clk1_enable(uint32_t sai_periph);
 
void sai_pdm_clk1_disable(uint32_t sai_periph);

 
 
void sai_interrupt_enable(uint32_t sai_periph, uint32_t block, uint32_t interrupt);
 
void sai_interrupt_disable(uint32_t sai_periph, uint32_t block, uint32_t interrupt);
 
FlagStatus sai_interrupt_flag_get(uint32_t sai_periph, uint32_t block, uint32_t interrupt);
 
void sai_interrupt_flag_clear(uint32_t sai_periph, uint32_t block, uint32_t interrupt);
 
FlagStatus sai_flag_get(uint32_t sai_periph, uint32_t block, uint32_t flag);
 
void sai_flag_clear(uint32_t sai_periph, uint32_t block, uint32_t flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 

 

 
 
 
void sdio_deinit(uint32_t sdio_periph);
 
void sdio_clock_config(uint32_t sdio_periph, uint32_t clock_edge, uint32_t clock_powersave, uint32_t clock_division);
 
void sdio_clock_receive_set(uint32_t sdio_periph, uint32_t clock_receive);
 
void sdio_hardware_clock_enable(uint32_t sdio_periph);
 
void sdio_hardware_clock_disable(uint32_t sdio_periph);
 
void sdio_bus_mode_set(uint32_t sdio_periph, uint32_t bus_mode);
 
void sdio_bus_speed_set(uint32_t sdio_periph, uint32_t bus_speed);
 
void sdio_data_rate_set(uint32_t sdio_periph, uint32_t data_rate);
 
void sdio_direction_polarity_set(uint32_t sdio_periph, uint32_t dirpl);
 
void sdio_power_state_set(uint32_t sdio_periph, uint32_t power_state);
 
uint32_t sdio_power_state_get(uint32_t sdio_periph);

 
 
void sdio_command_response_config(uint32_t sdio_periph, uint32_t cmd_index, uint32_t cmd_argument, uint32_t response_type);
 
void sdio_wait_type_set(uint32_t sdio_periph, uint32_t wait_type);
 
void sdio_trans_start_enable(uint32_t sdio_periph);
 
void sdio_trans_start_disable(uint32_t sdio_periph);
 
void sdio_trans_stop_enable(uint32_t sdio_periph);
 
void sdio_trans_stop_disable(uint32_t sdio_periph);
 
void sdio_csm_enable(uint32_t sdio_periph);
 
void sdio_csm_disable(uint32_t sdio_periph);
 
uint8_t sdio_command_index_get(uint32_t sdio_periph);
 
uint32_t sdio_response_get(uint32_t sdio_periph, uint32_t sdio_responsex);
 
void sdio_hold_enable(uint32_t sdio_periph);
 
void sdio_hold_disable(uint32_t sdio_periph);
 
void sdio_suspend_enable(uint32_t sdio_periph);
 
void sdio_suspend_disable(uint32_t sdio_periph);

 
 
void sdio_data_config(uint32_t sdio_periph, uint32_t data_timeout, uint32_t data_length, uint32_t data_blocksize);
 
void sdio_data_transfer_config(uint32_t sdio_periph, uint32_t transfer_mode, uint32_t transfer_direction);
 
void sdio_dsm_enable(uint32_t sdio_periph);
 
void sdio_dsm_disable(uint32_t sdio_periph);
 
void sdio_data_write(uint32_t sdio_periph, uint32_t data);
 
uint32_t sdio_data_read(uint32_t sdio_periph);
 
uint32_t sdio_data_counter_get(uint32_t sdio_periph);
 
void sdio_fifo_reset_enable(uint32_t sdio_periph);
 
void sdio_fifo_reset_disable(uint32_t sdio_periph);

 
 
void sdio_idma_set(uint32_t sdio_periph, uint32_t buffer_mode, uint32_t buffer_size);
 
void sdio_idma_buffer0_address_set(uint32_t sdio_periph, uint32_t buffer_address);
 
void sdio_idma_buffer1_address_set(uint32_t sdio_periph, uint32_t buffer_address);
 
uint32_t sdio_buffer_selection_get(uint32_t sdio_periph);
 
void sdio_idma_buffer_select(uint32_t sdio_periph, uint32_t buffer_select);
 
void sdio_idma_enable(uint32_t sdio_periph);
 
void sdio_idma_disable(uint32_t sdio_periph);

 
 
FlagStatus sdio_flag_get(uint32_t sdio_periph, uint32_t flag);
 
void sdio_flag_clear(uint32_t sdio_periph, uint32_t flag);
 
void sdio_interrupt_enable(uint32_t sdio_periph, uint32_t int_flag);
 
void sdio_interrupt_disable(uint32_t sdio_periph, uint32_t int_flag);
 
FlagStatus sdio_interrupt_flag_get(uint32_t sdio_periph, uint32_t int_flag);
 
void sdio_interrupt_flag_clear(uint32_t sdio_periph, uint32_t int_flag);

 
 
void sdio_voltage_switch_enable(uint32_t sdio_periph);
 
void sdio_voltage_switch_disable(uint32_t sdio_periph);
 
void sdio_voltage_switch_sequence_enable(uint32_t sdio_periph);
 
void sdio_voltage_switch_sequence_disable(uint32_t sdio_periph);

 
 
void sdio_boot_mode_set(uint32_t sdio_periph, uint32_t boot_mode);
 
void sdio_boot_ack_enable(uint32_t sdio_periph);
 
void sdio_boot_ack_disable(uint32_t sdio_periph);
 
void sdio_boot_acktimeout_set(uint32_t sdio_periph, uint32_t timeout);
 
void sdio_boot_enable(uint32_t sdio_periph);
 
void sdio_boot_disable(uint32_t sdio_periph);





 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
typedef struct
{   
    uint32_t device_mode;                                                        
    uint32_t trans_mode;                                                         
    uint32_t data_size;                                                          
    uint32_t nss;                                                                
    uint32_t endian;                                                             
    uint32_t clock_polarity_phase;                                               
    uint32_t prescale;                                                           
}spi_parameter_struct;

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

                                     

 

 

 

                                                   


 
 
 
void spi_i2s_deinit(uint32_t spi_periph);
 
void spi_struct_para_init(spi_parameter_struct* spi_struct);
 
void spi_init(uint32_t spi_periph, spi_parameter_struct* spi_struct);
 
void spi_enable(uint32_t spi_periph);
 
void spi_disable(uint32_t spi_periph);

 
void i2s_init(uint32_t spi_periph, uint32_t i2s_mode, uint32_t i2s_standard, uint32_t i2s_ckpl);
 
void i2s_psc_config(uint32_t spi_periph, uint32_t i2s_audiosample, uint32_t i2s_frameformat, uint32_t i2s_mckout);
 
void i2s_enable(uint32_t spi_periph);
 
void i2s_disable(uint32_t spi_periph);

 
 
void spi_io_config(uint32_t spi_periph, uint32_t io_cfg);

 
 
void spi_nss_idleness_delay_set(uint32_t spi_periph, uint32_t delay_cycle);
 
void spi_data_frame_delay_set(uint32_t spi_periph, uint32_t delay_cycle);
 
void spi_master_receive_clock_delay_set(uint32_t spi_periph, uint32_t delay_unit);
 
void spi_slave_receive_clock_delay_set(uint32_t spi_periph, uint32_t delay_unit);
 
void spi_master_receive_clock_delay_clear(uint32_t spi_periph);
 
void spi_slave_receive_clock_delay_clear(uint32_t spi_periph);

 
 
void spi_nss_output_control(uint32_t spi_periph, uint32_t nss_ctl);
 
void spi_nss_polarity_set(uint32_t spi_periph, uint32_t polarity);
 
void spi_nss_output_enable(uint32_t spi_periph);
 
void spi_nss_output_disable(uint32_t spi_periph);
 
void spi_nss_internal_high(uint32_t spi_periph);
 
void spi_nss_internal_low(uint32_t spi_periph);

 
 
void spi_dma_enable(uint32_t spi_periph, uint8_t spi_dma);
 
void spi_dma_disable(uint32_t spi_periph, uint8_t spi_dma);

 
 
void spi_i2s_data_frame_size_config(uint32_t spi_periph, uint32_t frame_size);
 
void spi_i2s_data_transmit(uint32_t spi_periph, uint32_t data);
 
uint32_t spi_i2s_data_receive(uint32_t spi_periph);
 
void spi_bidirectional_transfer_config(uint32_t spi_periph, uint32_t transfer_direction);
 
void spi_master_transfer_start(uint32_t spi_periph, uint32_t transfer_start);
 
void spi_current_data_num_config(uint32_t spi_periph, uint32_t current_num);
 
void spi_reload_data_num_config(uint32_t spi_periph, uint32_t reload_num);

 
 
void spi_crc_polynomial_set(uint32_t spi_periph, uint32_t crc_poly);
 
uint32_t spi_crc_polynomial_get(uint32_t spi_periph);
 
void spi_crc_length_config(uint32_t spi_periph, uint32_t crc_size);
 
void spi_crc_on(uint32_t spi_periph);
 
void spi_crc_off(uint32_t spi_periph);
 
uint32_t spi_crc_get(uint32_t spi_periph, uint8_t crc);
 
void spi_crc_full_size_enable(uint32_t spi_periph);
 
void spi_crc_full_size_disable(uint32_t spi_periph);
 
void spi_tcrc_init_pattern(uint32_t spi_periph, uint32_t init_pattern);
 
void spi_rcrc_init_pattern(uint32_t spi_periph, uint32_t init_pattern);

 
 
void spi_ti_mode_enable(uint32_t spi_periph);
 
void spi_ti_mode_disable(uint32_t spi_periph);

 
 
void spi_quad_enable(uint32_t spi_periph);
 
void spi_quad_disable(uint32_t spi_periph);
 
void spi_quad_write_enable(uint32_t spi_periph);
 
void spi_quad_read_enable(uint32_t spi_periph);
 
void spi_quad_io23_output_enable(uint32_t spi_periph);
 
void spi_quad_io23_output_disable(uint32_t spi_periph);

 
 
void spi_underrun_operation(uint32_t spi_periph, uint32_t ur_ope);
 
void spi_underrun_config(uint32_t spi_periph, uint32_t ur_cfg);
 
void spi_underrun_data_config(uint32_t spi_periph, uint32_t udata);

 
 
void spi_suspend_mode_config(uint32_t spi_periph, uint32_t sus_mode);
 
void spi_suspend_request(uint32_t spi_periph);

 
 
void spi_related_ios_af_enable(uint32_t spi_periph);
 
void spi_related_ios_af_disable(uint32_t spi_periph);
 
void spi_af_gpio_control(uint32_t spi_periph, uint32_t ctl);

 
 
void spi_i2s_interrupt_enable(uint32_t spi_periph, uint8_t interrupt);
 
void spi_i2s_interrupt_disable(uint32_t spi_periph, uint8_t interrupt);
 
FlagStatus spi_i2s_interrupt_flag_get(uint32_t spi_periph, uint8_t interrupt);
 
FlagStatus spi_i2s_flag_get(uint32_t spi_periph, uint32_t flag);
 
void spi_i2s_flag_clear(uint32_t spi_periph, uint32_t flag);
 
uint32_t spi_i2s_rxfifo_plevel_get(uint32_t spi_periph);
 
uint32_t spi_i2s_remain_data_num_get(uint32_t spi_periph);

 
 
void spi_fifo_threshold_level_set(uint32_t spi_periph, uint32_t fifo_thl);
 
void spi_word_access_enable(uint32_t spi_periph);
 
void spi_word_access_disable(uint32_t spi_periph);
 
void spi_byte_access_enable(uint32_t spi_periph);
 
void spi_byte_access_disable(uint32_t spi_periph);






 


























 



 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 

 
 

 

 

 

 

 

 

 

 

 

 
typedef enum {
    TIMER7_CI0_INPUT_TIMER7_CH0         = (((uint32_t)(((uint8_t)0x00U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(0U))),         
    TIMER7_CI0_INPUT_CMP1_OUT           = (((uint32_t)(((uint8_t)0x00U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(1U))),         
    TIMER7_CI1_INPUT_TIMER7_CH1         = (((uint32_t)(((uint8_t)0x00U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(0U))),         
    TIMER7_CI2_INPUT_TIMER7_CH2         = (((uint32_t)(((uint8_t)0x00U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(0U))),         
    TIMER7_CI3_INPUT_TIMER7_CH3         = (((uint32_t)(((uint8_t)0x00U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(0U))),        
    TIMER0_CI0_INPUT_TIMER0_CH0         = (((uint32_t)(((uint8_t)0x00U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(0U))),        
    TIMER0_CI0_INPUT_CMP0_OUT           = (((uint32_t)(((uint8_t)0x00U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(1U))),        
    TIMER0_CI1_INPUT_TIMER0_CH1         = (((uint32_t)(((uint8_t)0x00U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(0U))),        
    TIMER0_CI2_INPUT_TIMER0_CH2         = (((uint32_t)(((uint8_t)0x00U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(0U))),        
    TIMER0_CI3_INPUT_TIMER0_CH3         = (((uint32_t)(((uint8_t)0x00U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(0U))),        
    TIMER2_CI0_INPUT_TIMER2_CH0         = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(0U))),         
    TIMER2_CI0_INPUT_CMP0_OUT           = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(1U))),         
    TIMER2_CI0_INPUT_CMP1_OUT           = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(2U))),         
    TIMER2_CI0_INPUT_CMP0_OR_CMP1_OUT   = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(3U))),         
    TIMER2_CI1_INPUT_TIMER2_CH1         = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(0U))),         
    TIMER2_CI2_INPUT_TIMER2_CH2         = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(0U))),         
    TIMER2_CI3_INPUT_TIMER2_CH3         = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(0U))),        
    TIMER1_CI0_INPUT_TIMER1_CH0         = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(0U))),        
    TIMER1_CI1_INPUT_TIMER1_CH1         = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(0U))),        
    TIMER1_CI2_INPUT_TIMER1_CH2         = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(0U))),        
    TIMER1_CI3_INPUT_TIMER1_CH3         = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(0U))),        
    TIMER1_CI3_INPUT_CMP0_OUT           = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(1U))),        
    TIMER1_CI3_INPUT_CMP1_OUT           = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(2U))),        
    TIMER1_CI3_INPUT_CMP0_OR_CMP1_OUT   = (((uint32_t)(((uint8_t)0x01U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(3U))),        
    TIMER4_CI0_INPUT_TIMER4_CH0         = (((uint32_t)(((uint8_t)0x02U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(0U))),         
    TIMER4_CI1_INPUT_TIMER4_CH1         = (((uint32_t)(((uint8_t)0x02U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(0U))),         
    TIMER4_CI2_INPUT_TIMER4_CH2         = (((uint32_t)(((uint8_t)0x02U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(0U))),         
    TIMER4_CI3_INPUT_TIMER4_CH3         = (((uint32_t)(((uint8_t)0x02U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(0U))),        
    TIMER3_CI0_INPUT_TIMER3_CH0         = (((uint32_t)(((uint8_t)0x02U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(0U))),        
    TIMER3_CI1_INPUT_TIMER3_CH1         = (((uint32_t)(((uint8_t)0x02U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(0U))),        
    TIMER3_CI2_INPUT_TIMER3_CH2         = (((uint32_t)(((uint8_t)0x02U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(0U))),        
    TIMER3_CI3_INPUT_TIMER3_CH3         = (((uint32_t)(((uint8_t)0x02U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(0U))),        
    TIMER23_CI0_INPUT_TIMER23_CH0       = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(0U))),         
    TIMER23_CI1_INPUT_TIMER23_CH1       = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(0U))),         
    TIMER23_CI2_INPUT_TIMER23_CH2       = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(0U))),         
    TIMER23_CI3_INPUT_TIMER23_CH3       = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(0U))),        
    TIMER22_CI0_INPUT_TIMER22_CH0       = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(0U))),        
    TIMER22_CI1_INPUT_TIMER22_CH1       = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(0U))),        
    TIMER22_CI2_INPUT_TIMER22_CH2       = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(0U))),        
    TIMER22_CI3_INPUT_TIMER22_CH3       = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(0U))),        
    TIMER22_CI3_INPUT_CMP0_OUT          = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(1U))),        
    TIMER22_CI3_INPUT_CMP1_OUT          = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(2U))),        
    TIMER22_CI3_INPUT_CMP0_OR_CMP1_OUT  = (((uint32_t)(((uint8_t)0x03U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(3U))),        
    TIMER31_CI0_INPUT_TIMER31_CH0       = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(0U))),         
    TIMER31_CI0_INPUT_CMP0_OUT          = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(1U))),         
    TIMER31_CI0_INPUT_CMP1_OUT          = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(2U))),         
    TIMER31_CI0_INPUT_CMP0_OR_CMP1_OUT  = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(3U))),         
    TIMER31_CI1_INPUT_TIMER31_CH1       = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(0U))),         
    TIMER31_CI2_INPUT_TIMER31_CH2       = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(0U))),         
    TIMER31_CI3_INPUT_TIMER31_CH3       = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(0U))),        
    TIMER30_CI0_INPUT_TIMER30_CH0       = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(0U))),        
    TIMER30_CI0_INPUT_CMP0_OUT          = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(1U))),        
    TIMER30_CI0_INPUT_CMP1_OUT          = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(2U))),        
    TIMER30_CI0_INPUT_CMP0_OR_CMP1_OUT  = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(3U))),        
    TIMER30_CI1_INPUT_TIMER30_CH1       = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(0U))),        
    TIMER30_CI2_INPUT_TIMER30_CH2       = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(0U))),        
    TIMER30_CI3_INPUT_TIMER30_CH3       = (((uint32_t)(((uint8_t)0x04U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(0U))),        
    TIMER14_CI0_INPUT_TIMER14_CH0       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(0U))),         
    TIMER14_CI0_INPUT_TIMER1_CH0        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(1U))),         
    TIMER14_CI0_INPUT_TIMER2_CH0        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(2U))),         
    TIMER14_CI0_INPUT_TIMER3_CH0        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(3U))),         
    TIMER14_CI0_INPUT_LXTAL             = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(4U))),         
    TIMER14_CI0_INPUT_LPIRC4M           = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(5U))),         
    TIMER14_CI0_INPUT_CKOUT1            = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(6U))),         
    TIMER14_CI1_INPUT_TIMER14_CH1       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(0U))),         
    TIMER14_CI1_INPUT_TIMER1_CH1        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(1U))),         
    TIMER14_CI1_INPUT_TIMER2_CH1        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(2U))),         
    TIMER14_CI1_INPUT_TIMER3_CH1        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(3U))),         
    TIMER40_CI0_INPUT_TIMER40_CH0       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(0U))),         
    TIMER40_CI0_INPUT_TIMER2_CH0        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(1U))),         
    TIMER40_CI0_INPUT_TIMER3_CH0        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(2U))),         
    TIMER40_CI0_INPUT_TIMER4_CH0        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(3U))),         
    TIMER40_CI0_INPUT_LXTAL             = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(4U))),         
    TIMER40_CI0_INPUT_LPIRC4M           = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(5U))),         
    TIMER40_CI0_INPUT_CKOUT1            = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(6U))),         
    TIMER40_CI1_INPUT_TIMER40_CH1       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(0U))),        
    TIMER40_CI1_INPUT_TIMER2_CH1        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(1U))),        
    TIMER40_CI1_INPUT_TIMER3_CH1        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(2U))),        
    TIMER40_CI1_INPUT_TIMER4_CH1        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(3U))),        
    TIMER41_CI0_INPUT_TIMER41_CH0       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(0U))),        
    TIMER41_CI0_INPUT_TIMER3_CH0        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(1U))),        
    TIMER41_CI0_INPUT_TIMER4_CH0        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(2U))),        
    TIMER41_CI0_INPUT_TIMER22_CH0       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(3U))),        
    TIMER41_CI0_INPUT_LXTAL             = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(4U))),        
    TIMER41_CI0_INPUT_LPIRC4M           = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(5U))),        
    TIMER41_CI0_INPUT_CKOUT1            = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(6U))),        
    TIMER41_CI1_INPUT_TIMER41_CH1       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(0U))),        
    TIMER41_CI1_INPUT_TIMER3_CH1        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(1U))),        
    TIMER41_CI1_INPUT_TIMER4_CH1        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(2U))),        
    TIMER41_CI1_INPUT_TIMER22_CH1       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(3U))),        
    TIMER42_CI0_INPUT_TIMER42_CH0       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(0U))),        
    TIMER42_CI0_INPUT_TIMER4_CH0        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(1U))),        
    TIMER42_CI0_INPUT_TIMER22_CH0       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(2U))),        
    TIMER42_CI0_INPUT_TIMER23_CH0       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(3U))),        
    TIMER42_CI0_INPUT_LXTAL             = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(4U))),        
    TIMER42_CI0_INPUT_LPIRC4M           = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(5U))),        
    TIMER42_CI0_INPUT_CKOUT1            = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((24U) << 16U) | ((uint32_t)(6U))),        
    TIMER42_CI1_INPUT_TIMER42_CH1       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(0U))),        
    TIMER42_CI1_INPUT_TIMER4_CH1        = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(1U))),        
    TIMER42_CI1_INPUT_TIMER22_CH1       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(2U))),        
    TIMER42_CI1_INPUT_TIMER23_CH1       = (((uint32_t)(((uint8_t)0x05U)) << 24U) | (uint32_t)((28U) << 16U) | ((uint32_t)(3U))),        
    TIMER15_CI0_INPUT_TIMER15_CH0       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(0U))),         
    TIMER15_CI0_INPUT_IRC32K            = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(1U))),         
    TIMER15_CI0_INPUT_LXTAL             = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(2U))),         
    TIMER15_CI0_INPUT_WKUP_IT           = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((0U) << 16U) | ((uint32_t)(3U))),         
    TIMER16_CI0_INPUT_TIMER16_CH0       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(0U))),         
    TIMER16_CI0_INPUT_RSPDIF            = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(1U))),         
    TIMER16_CI0_INPUT_HXTAL_RTCDIV      = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(2U))),         
    TIMER16_CI0_INPUT_CKOUT0            = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((4U) << 16U) | ((uint32_t)(3U))),         
    TIMER43_CI0_INPUT_TIMER43_CH0       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(0U))),         
    TIMER43_CI0_INPUT_TIMER22_CH0       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(1U))),         
    TIMER43_CI0_INPUT_TIMER23_CH0       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(2U))),         
    TIMER43_CI0_INPUT_TIMER30_CH0       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(3U))),         
    TIMER43_CI0_INPUT_LXTAL             = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(4U))),         
    TIMER43_CI0_INPUT_LPIRC4M           = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(5U))),         
    TIMER43_CI0_INPUT_CKOUT1            = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((8U) << 16U) | ((uint32_t)(6U))),         
    TIMER43_CI1_INPUT_TIMER43_CH1       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(0U))),        
    TIMER43_CI1_INPUT_TIMER22_CH1       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(1U))),        
    TIMER43_CI1_INPUT_TIMER23_CH1       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(2U))),        
    TIMER43_CI1_INPUT_TIMER30_CH1       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((12U) << 16U) | ((uint32_t)(3U))),        
    TIMER44_CI0_INPUT_TIMER44_CH0       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(0U))),        
    TIMER44_CI0_INPUT_TIMER23_CH0       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(1U))),        
    TIMER44_CI0_INPUT_TIMER30_CH0       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(2U))),        
    TIMER44_CI0_INPUT_TIMER31_CH0       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(3U))),        
    TIMER44_CI0_INPUT_LXTAL             = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(4U))),        
    TIMER44_CI0_INPUT_LPIRC4M           = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(5U))),        
    TIMER44_CI0_INPUT_CKOUT1            = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((16U) << 16U) | ((uint32_t)(6U))),        
    TIMER44_CI1_INPUT_TIMER44_CH1       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(0U))),        
    TIMER44_CI1_INPUT_TIMER23_CH1       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(1U))),        
    TIMER44_CI1_INPUT_TIMER30_CH1       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(2U))),        
    TIMER44_CI1_INPUT_TIMER31_CH1       = (((uint32_t)(((uint8_t)0x06U)) << 24U) | (uint32_t)((20U) << 16U) | ((uint32_t)(3U))),        
} timer_channel_input_enum;

 

 



 

 

 

 

 

 

 

 

 

 

 
 
 
void syscfg_deinit(void);

 
 
void syscfg_i2c_fast_mode_plus_enable(uint32_t i2c_fmp);
 
void syscfg_i2c_fast_mode_plus_disable(uint32_t i2c_fmp);
 
void syscfg_analog_switch_enable(uint32_t gpio_answ);
 
void syscfg_analog_switch_disable(uint32_t gpio_answ);
 
void syscfg_enet_phy_interface_config(uint32_t ethernet, uint32_t phy_interface);
 
void syscfg_exti_line_config(uint8_t exti_port, uint8_t exti_pin);
 
void syscfg_lockup_enable(uint32_t lockup);
 
void syscfg_timer_input_source_select(timer_channel_input_enum timer_input);

 
 
void syscfg_io_compensation_config(uint32_t syscfg_cps);
 
void syscfg_io_low_voltage_speed_optimization_enable(void);
 
void syscfg_io_low_voltage_speed_optimization_disable(void);
 
void syscfg_pnmos_compensation_code_set(uint32_t mos, uint32_t code);

 
 
void syscfg_secure_sram_size_set(uint32_t size);
 
uint32_t syscfg_secure_sram_size_get(void);
 
uint32_t syscfg_bootmode_get(void);
 
void syscfg_tcm_wait_state_enable(void);
 
void syscfg_tcm_wait_state_disable(void);

 
 
void syscfg_fpu_interrupt_enable(uint32_t fpu_int);
 
void syscfg_fpu_interrupt_disable(uint32_t fpu_int);
 
FlagStatus syscfg_compensation_flag_get(uint32_t cps_flag);
 
uint32_t syscfg_cpu_cache_status_get(uint32_t cache, uint32_t status);
 
uint32_t syscfg_brownout_reset_threshold_level_get(void);





 


























 




 

 

 
 

 

 

 

 

 

 
 
 

 
 
 

 


 

 

 

 

 

 

 

 

 

 
 
 

 
 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
typedef enum {VALID_RESET = 0, VALID_SET = 1, INVALID = 2} UPIFBUStatus;

 
typedef struct {
    uint16_t prescaler;                                                              
    uint16_t alignedmode;                                                            
    uint16_t counterdirection;                                                       
    uint64_t period;                                                                 
    uint16_t clockdivision;                                                          
    uint32_t repetitioncounter;                                                      
} timer_parameter_struct;

 
typedef struct {
    uint32_t runoffstate;                                                            
    uint32_t ideloffstate;                                                           
    uint32_t deadtime;                                                               
    uint32_t outputautostate;                                                        
    uint32_t protectmode;                                                            
    uint32_t break0state;                                                            
    uint32_t break0filter;                                                           
    uint32_t break0polarity;                                                         
    uint32_t break0lock;                                                             
    uint32_t break0release;                                                          
    uint32_t break1state;                                                            
    uint32_t break1filter;                                                           
    uint32_t break1polarity;                                                         
    uint32_t break1lock;                                                             
    uint32_t break1release;                                                          
} timer_break_parameter_struct;

 
typedef struct {
    uint16_t outputstate;                                                            
    uint16_t outputnstate;                                                           
    uint16_t ocpolarity;                                                             
    uint16_t ocnpolarity;                                                            
    uint16_t ocidlestate;                                                            
    uint16_t ocnidlestate;                                                           
} timer_oc_parameter_struct;

 
typedef struct {
    uint16_t outputmode;                                                             
    uint16_t outputstate;                                                            
    uint16_t ocpolarity;                                                             
} timer_omc_parameter_struct;

 
typedef struct {
    uint16_t icpolarity;                                                             
    uint16_t icselection;                                                            
    uint16_t icprescaler;                                                            
    uint16_t icfilter;                                                               
} timer_ic_parameter_struct;

 
typedef struct {
    uint32_t freecomstate;                                                           
    uint32_t runoffstate;                                                            
    uint32_t ideloffstate;                                                           
    uint32_t deadtime;                                                               
} timer_free_complementary_parameter_struct;

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
 
void timer_deinit(uint32_t timer_periph);
 
void timer_struct_para_init(timer_parameter_struct *initpara);
 
void timer_init(uint32_t timer_periph, timer_parameter_struct *initpara);
 
void timer_enable(uint32_t timer_periph);
 
void timer_disable(uint32_t timer_periph);
 
void timer_auto_reload_shadow_enable(uint32_t timer_periph);
 
void timer_auto_reload_shadow_disable(uint32_t timer_periph);
 
void timer_update_event_enable(uint32_t timer_periph);
 
void timer_update_event_disable(uint32_t timer_periph);
 
void timer_counter_alignment(uint32_t timer_periph, uint16_t aligned);
 
void timer_counter_up_direction(uint32_t timer_periph);
 
void timer_counter_down_direction(uint32_t timer_periph);

 
void timer_prescaler_config(uint32_t timer_periph, uint16_t prescaler, uint32_t pscreload);
 
void timer_repetition_value_config(uint32_t timer_periph, uint16_t ccsel, uint32_t repetition);
 
uint32_t timer_runtime_repetition_value_read(uint32_t timer_periph);
 
void timer_autoreload_value_config(uint32_t timer_periph, uint64_t autoreload);
 
uint64_t timer_autoreload_value_read(uint32_t timer_periph);
 
void timer_counter_value_config(uint32_t timer_periph, uint64_t counter);
 
uint64_t timer_counter_read(uint32_t timer_periph);
 
uint16_t timer_prescaler_read(uint32_t timer_periph);
 
void timer_single_pulse_mode_config(uint32_t timer_periph, uint32_t spmode);
 
void timer_delayable_single_pulse_mode_config(uint32_t timer_periph, uint16_t channel, uint32_t dspmode, uint16_t cnt_dir);
 
void timer_update_source_config(uint32_t timer_periph, uint32_t update);

 
 
void timer_dma_enable(uint32_t timer_periph, uint32_t dma);
 
void timer_dma_disable(uint32_t timer_periph, uint32_t dma);
 
void timer_channel_dma_request_source_select(uint32_t timer_periph, uint32_t dma_request);
 
void timer_dma_transfer_config(uint32_t timer_periph, uint32_t dma_baseaddr, uint32_t dma_lenth);
 
void timer_event_software_generate(uint32_t timer_periph, uint32_t event);

 
 
void timer_break_struct_para_init(timer_break_parameter_struct *breakpara);
 
void timer_break_config(uint32_t timer_periph, timer_break_parameter_struct *breakpara);
 
void timer_break_enable(uint32_t timer_periph, uint16_t break_num);
 
void timer_break_disable(uint32_t timer_periph, uint16_t break_num);
 
void timer_automatic_output_enable(uint32_t timer_periph);
 
void timer_automatic_output_disable(uint32_t timer_periph);
 
void timer_primary_output_config(uint32_t timer_periph, ControlStatus newvalue);
 
void timer_channel_control_shadow_config(uint32_t timer_periph, ControlStatus newvalue);
 
void timer_channel_control_shadow_update_config(uint32_t timer_periph, uint32_t ccuctl);

 
 
void timer_channel_output_struct_para_init(timer_oc_parameter_struct *ocpara);
 
void timer_channel_output_config(uint32_t timer_periph, uint16_t channel, timer_oc_parameter_struct *ocpara);
 
void timer_channel_output_mode_config(uint32_t timer_periph, uint16_t channel, uint32_t ocmode);
 
void timer_channel_output_pulse_value_config(uint32_t timer_periph, uint16_t channel, uint32_t pulse);
 
void timer_channel_output_shadow_config(uint32_t timer_periph, uint16_t channel, uint16_t ocshadow);
 
void timer_channel_output_clear_config(uint32_t timer_periph, uint16_t channel, uint16_t occlear);
 
void timer_channel_output_polarity_config(uint32_t timer_periph, uint16_t channel, uint16_t ocpolarity);
 
void timer_channel_complementary_output_polarity_config(uint32_t timer_periph, uint16_t channel, uint16_t ocnpolarity);
 
void timer_channel_output_state_config(uint32_t timer_periph, uint16_t channel, uint32_t state);
 
void timer_channel_complementary_output_state_config(uint32_t timer_periph, uint16_t channel, uint16_t ocnstate);

 
 
void timer_channel_input_struct_para_init(timer_ic_parameter_struct *icpara);
 
void timer_input_capture_config(uint32_t timer_periph, uint16_t channel, timer_ic_parameter_struct *icpara);
 
void timer_channel_input_capture_prescaler_config(uint32_t timer_periph, uint16_t channel, uint16_t prescaler);
 
uint32_t timer_channel_capture_value_register_read(uint32_t timer_periph, uint16_t channel);
 
void timer_input_pwm_capture_config(uint32_t timer_periph, uint16_t channel, timer_ic_parameter_struct *icpwm);
 
void timer_hall_mode_config(uint32_t timer_periph, uint32_t hallmode);

 
 
void timer_multi_mode_channel_output_parameter_struct_init(timer_omc_parameter_struct *omcpara);
 
void timer_multi_mode_channel_output_config(uint32_t timer_periph, uint16_t channel, timer_omc_parameter_struct *omcpara);
 
void timer_multi_mode_channel_mode_config(uint32_t timer_periph, uint32_t channel, uint32_t multi_mode_sel);

 
 
void timer_input_trigger_source_select(uint32_t timer_periph, uint32_t intrigger);
 
void timer_master_output0_trigger_source_select(uint32_t timer_periph, uint32_t outrigger);
 
void timer_master_output1_trigger_source_select(uint32_t timer_periph, uint32_t outrigger);
 
void timer_slave_mode_select(uint32_t timer_periph, uint32_t slavemode);
 
void timer_master_slave_mode_config(uint32_t timer_periph, uint32_t masterslave);

 
void timer_external_trigger_config(uint32_t timer_periph, uint32_t extprescaler, uint32_t extpolarity, uint32_t extfilter);
 
void timer_quadrature_decoder_mode_config(uint32_t timer_periph, uint32_t decomode, uint16_t ic0polarity, uint16_t ic1polarity);
 
void timer_non_quadrature_decoder_mode_config(uint32_t timer_periph, uint32_t decomode, uint16_t ic1polarity);
 
void timer_internal_clock_config(uint32_t timer_periph);
 
void timer_internal_trigger_as_external_clock_config(uint32_t timer_periph, uint32_t intrigger);
 
void timer_external_trigger_as_external_clock_config(uint32_t timer_periph, uint32_t extrigger, uint16_t extpolarity, uint32_t extfilter);
 
void timer_external_clock_mode0_config(uint32_t timer_periph, uint32_t extprescaler, uint32_t extpolarity, uint32_t extfilter);
 
void timer_external_clock_mode1_config(uint32_t timer_periph, uint32_t extprescaler, uint32_t extpolarity, uint32_t extfilter);
 
void timer_external_clock_mode1_disable(uint32_t timer_periph);

 
 
void timer_write_chxval_register_config(uint32_t timer_periph, uint16_t ccsel);
 
void timer_output_value_selection_config(uint32_t timer_periph, uint16_t outsel);
 
void timer_commutation_control_shadow_register_config(uint32_t timer_periph, uint16_t ccssel);
 
void timer_output_match_pulse_select(uint32_t timer_periph, uint32_t channel, uint16_t pulsesel);

 
 
void timer_channel_composite_pwm_mode_config(uint32_t timer_periph, uint32_t channel, ControlStatus newvalue);
 
void timer_channel_composite_pwm_mode_output_pulse_value_config(uint32_t timer_periph, uint32_t channel, uint32_t pulse, uint32_t add_pulse);
 
void timer_channel_additional_compare_value_config(uint32_t timer_periph, uint16_t channel, uint32_t value);
 
void timer_channel_additional_output_shadow_config(uint32_t timer_periph, uint16_t channel, uint16_t aocshadow);
 
uint32_t timer_channel_additional_compare_value_read(uint32_t timer_periph, uint16_t channel);

 
 
void timer_break_external_source_config(uint32_t timer_periph, uint16_t break_num, uint32_t break_src, ControlStatus newvalue);
 
void timer_break_external_polarity_config(uint32_t timer_periph, uint16_t break_num, uint32_t break_src, uint16_t bkinpolarity);
 
void timer_break_lock_config(uint32_t timer_periph, uint16_t break_num, ControlStatus newvalue);
 
void timer_break_lock_release_config(uint32_t timer_periph, uint16_t break_num, ControlStatus newvalue);

 
 
void timer_channel_break_control_config(uint32_t timer_periph, uint32_t channel, ControlStatus newvalue);
 
void timer_channel_dead_time_config(uint32_t timer_periph, uint32_t channel, ControlStatus newvalue);
 
void timer_free_complementary_struct_para_init(timer_free_complementary_parameter_struct *freecompara);
 
void timer_channel_free_complementary_config(uint32_t timer_periph, uint16_t channel, timer_free_complementary_parameter_struct *fcpara);

 
 
void timer_watchdog_value_config(uint32_t timer_periph, uint32_t value);
 
uint32_t timer_watchdog_value_read(uint32_t timer_periph);
 
void timer_decoder_disconnection_detection_config(uint32_t timer_periph, ControlStatus newvalue);
 
void timer_decoder_jump_detection_config(uint32_t timer_periph, ControlStatus newvalue);

 
 
void timer_upif_backup_config(uint32_t timer_periph, ControlStatus newvalue);
 
UPIFBUStatus timer_upifbu_bit_get(uint32_t timer_periph);

 
 
FlagStatus timer_flag_get(uint32_t timer_periph, uint32_t flag);
 
void timer_flag_clear(uint32_t timer_periph, uint32_t flag);
 
void timer_interrupt_enable(uint32_t timer_periph, uint32_t interrupt);
 
void timer_interrupt_disable(uint32_t timer_periph, uint32_t interrupt);
 
FlagStatus timer_interrupt_flag_get(uint32_t timer_periph, uint32_t int_flag);
 
void timer_interrupt_flag_clear(uint32_t timer_periph, uint32_t int_flag);






 


























 



 
 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
typedef struct
{   
    uint16_t synpsz_vpsz;                                                    
    uint16_t synpsz_hpsz;                                                    
    uint16_t backpsz_vbpsz;                                                  
    uint16_t backpsz_hbpsz;                                                  
    uint32_t activesz_vasz;                                                  
    uint32_t activesz_hasz;                                                  
    uint32_t totalsz_vtsz;                                                   
    uint32_t totalsz_htsz;                                                   
    uint32_t backcolor_red;                                                  
    uint32_t backcolor_green;                                                
    uint32_t backcolor_blue;                                                 
    uint32_t signalpolarity_hs;                                              
    uint32_t signalpolarity_vs;                                              
    uint32_t signalpolarity_de;                                              
    uint32_t signalpolarity_pixelck;                                         
}tli_parameter_struct; 

 
typedef struct
{
    uint16_t layer_window_rightpos;                                          
    uint16_t layer_window_leftpos;                                           
    uint16_t layer_window_bottompos;                                         
    uint16_t layer_window_toppos;                                            
    uint32_t layer_ppf;                                                      
    uint8_t  layer_sa;                                                       
    uint8_t  layer_default_alpha;                                            
    uint8_t  layer_default_red;                                              
    uint8_t  layer_default_green;                                            
    uint8_t  layer_default_blue;                                             
    uint32_t layer_acf1;                                                     
    uint32_t layer_acf2;                                                     
    uint32_t layer_frame_bufaddr;                                            
    uint16_t layer_frame_buf_stride_offset;                                  
    uint16_t layer_frame_line_length;                                        
    uint16_t layer_frame_total_line_number;                                  
}tli_layer_parameter_struct; 

 
typedef struct
{
    uint32_t layer_table_addr;                                               
    uint8_t layer_lut_channel_red;                                           
    uint8_t layer_lut_channel_green;                                         
    uint8_t layer_lut_channel_blue;                                          
}tli_layer_lut_parameter_struct; 

 
typedef enum 
{
     LAYER_PPF_ARGB8888 = 0U,                                                
     LAYER_PPF_RGB888,                                                       
     LAYER_PPF_RGB565,                                                       
     LAYER_PPF_ARGB1555,                                                     
     LAYER_PPF_ARGB4444,                                                     
     LAYER_PPF_L8,                                                           
     LAYER_PPF_AL44,                                                         
     LAYER_PPF_AL88                                                          
}tli_layer_ppf_enum;

 

 

 

 

 

 

 

 

 

 

 

 
 
 
void tli_deinit(void);

 
void tli_struct_para_init(tli_parameter_struct *tli_struct);
 
void tli_init(tli_parameter_struct *tli_struct);
 
void tli_dither_config(uint8_t dither_stat);
 
void tli_enable(void);
 
void tli_disable(void);
 
void tli_reload_config(uint8_t reload_mod);

 

 
void tli_layer_struct_para_init(tli_layer_parameter_struct *layer_struct);
 
void tli_layer_init(uint32_t layerx,tli_layer_parameter_struct *layer_struct);
 
void tli_layer_window_offset_modify(uint32_t layerx,uint16_t offset_x,uint16_t offset_y);

 
void tli_lut_struct_para_init(tli_layer_lut_parameter_struct *lut_struct);
 
void tli_lut_init(uint32_t layerx,tli_layer_lut_parameter_struct *lut_struct);
 
void tli_color_key_init(uint32_t layerx,uint8_t redkey,uint8_t greenkey,uint8_t bluekey);
 
void tli_layer_enable(uint32_t layerx);
 
void tli_layer_disable(uint32_t layerx);
 
void tli_color_key_enable(uint32_t layerx);
 
void tli_color_key_disable(uint32_t layerx);
 
void tli_lut_enable(uint32_t layerx);
 
void tli_lut_disable(uint32_t layerx);

 
void tli_line_mark_set(uint16_t line_num);
 
uint32_t tli_current_pos_get(void);

 
 
void tli_interrupt_enable(uint32_t int_flag);
 
void tli_interrupt_disable(uint32_t int_flag);
 
FlagStatus tli_interrupt_flag_get(uint32_t int_flag);
 
void tli_interrupt_flag_clear(uint32_t int_flag);
 
FlagStatus tli_flag_get(uint32_t flag);






 


























 



 

 

 
 

 

 

 
 
typedef struct
{
    uint32_t mode;                                               
    uint32_t iterations_number;                                  
    uint32_t scale;                                              
    uint32_t dma_read;                                           
    uint32_t dma_write;                                          
    uint32_t read_times;                                         
    uint32_t write_times;                                        
    uint32_t output_width;                                       
    uint32_t input_width;                                        
}tmu_parameter_struct;

 

 

 

 

 

 

 

 

 

 
 
 
void tmu_deinit(void);
 
void tmu_struct_para_init(tmu_parameter_struct* init_struct);
 
void tmu_init(tmu_parameter_struct* init_struct);

 
 
void tmu_read_interrupt_enable(void);
 
void tmu_read_interrupt_disable(void);
 
void tmu_dma_read_enable(void);
 
void tmu_dma_read_disable(void);
 
void tmu_dma_write_enable(void);
 
void tmu_dma_write_disable(void);

 
 
void tmu_one_q31_write(uint32_t data);
 
void tmu_two_q31_write(uint32_t data1, uint32_t data2);
 
void tmu_two_q15_write(uint16_t data1, uint16_t data2);
 
void tmu_one_q31_read(uint32_t* p);
 
void tmu_two_q31_read(uint32_t* p1, uint32_t* p2);
 
void tmu_two_q15_read(uint16_t* p1, uint16_t* p2);






 


























 



 

 

 
 

 
 
typedef enum
{
    TRIGSEL_INPUT_0                       = ((uint8_t)0x00U),                
    TRIGSEL_INPUT_1                       = ((uint8_t)0x01U),                
    TRIGSEL_INPUT_TRIGSEL_IN0             = ((uint8_t)0x02U),                
    TRIGSEL_INPUT_TRIGSEL_IN1             = ((uint8_t)0x03U),                
    TRIGSEL_INPUT_TRIGSEL_IN2             = ((uint8_t)0x04U),                
    TRIGSEL_INPUT_TRIGSEL_IN3             = ((uint8_t)0x05U),                
    TRIGSEL_INPUT_TRIGSEL_IN4             = ((uint8_t)0x06U),                
    TRIGSEL_INPUT_TRIGSEL_IN5             = ((uint8_t)0x07U),                
    TRIGSEL_INPUT_TRIGSEL_IN6             = ((uint8_t)0x08U),                
    TRIGSEL_INPUT_TRIGSEL_IN7             = ((uint8_t)0x09U),                
    TRIGSEL_INPUT_TRIGSEL_IN8             = ((uint8_t)0x0AU),                
    TRIGSEL_INPUT_TRIGSEL_IN9             = ((uint8_t)0x0BU),                
    TRIGSEL_INPUT_TRIGSEL_IN10            = ((uint8_t)0x0CU),                
    TRIGSEL_INPUT_TRIGSEL_IN11            = ((uint8_t)0x0DU),                
    TRIGSEL_INPUT_TRIGSEL_IN12            = ((uint8_t)0x0EU),                
    TRIGSEL_INPUT_TRIGSEL_IN13            = ((uint8_t)0x0FU),                
    TRIGSEL_INPUT_LXTAL_TRG               = ((uint8_t)0x10U),                
    TRIGSEL_INPUT_TIMER0_TRGO0            = ((uint8_t)0x11U),                
    TRIGSEL_INPUT_TIMER0_TRGO1            = ((uint8_t)0x12U),                
    TRIGSEL_INPUT_TIMER0_CH0              = ((uint8_t)0x13U),                
    TRIGSEL_INPUT_TIMER0_CH1              = ((uint8_t)0x14U),                
    TRIGSEL_INPUT_TIMER0_CH2              = ((uint8_t)0x15U),                
    TRIGSEL_INPUT_TIMER0_CH3              = ((uint8_t)0x16U),                
    TRIGSEL_INPUT_TIMER0_MCH0             = ((uint8_t)0x17U),                
    TRIGSEL_INPUT_TIMER0_MCH1             = ((uint8_t)0x18U),                
    TRIGSEL_INPUT_TIMER0_MCH2             = ((uint8_t)0x19U),                
    TRIGSEL_INPUT_TIMER0_MCH3             = ((uint8_t)0x1AU),                
    TRIGSEL_INPUT_TIMER0_BRKIN0           = ((uint8_t)0x21U),                
    TRIGSEL_INPUT_TIMER0_BRKIN1           = ((uint8_t)0x22U),                
    TRIGSEL_INPUT_TIMER0_BRKIN2           = ((uint8_t)0x23U),                
    TRIGSEL_INPUT_TIMER0_ETI              = ((uint8_t)0x24U),                
    TRIGSEL_INPUT_TIMER1_TRGO0            = ((uint8_t)0x25U),                
    TRIGSEL_INPUT_TIMER1_CH0              = ((uint8_t)0x26U),                
    TRIGSEL_INPUT_TIMER1_CH1              = ((uint8_t)0x27U),                
    TRIGSEL_INPUT_TIMER1_CH2              = ((uint8_t)0x28U),                
    TRIGSEL_INPUT_TIMER1_CH3              = ((uint8_t)0x29U),                
    TRIGSEL_INPUT_TIMER1_ETI              = ((uint8_t)0x2AU),                
    TRIGSEL_INPUT_TIMER2_TRGO0            = ((uint8_t)0x2BU),                
    TRIGSEL_INPUT_TIMER2_CH0              = ((uint8_t)0x2CU),                
    TRIGSEL_INPUT_TIMER2_CH1              = ((uint8_t)0x2DU),                
    TRIGSEL_INPUT_TIMER2_CH2              = ((uint8_t)0x2EU),                
    TRIGSEL_INPUT_TIMER2_CH3              = ((uint8_t)0x2FU),                
    TRIGSEL_INPUT_TIMER2_ETI              = ((uint8_t)0x30U),                
    TRIGSEL_INPUT_TIMER3_TRGO0            = ((uint8_t)0x31U),                
    TRIGSEL_INPUT_TIMER3_CH0              = ((uint8_t)0x32U),                
    TRIGSEL_INPUT_TIMER3_CH1              = ((uint8_t)0x33U),                
    TRIGSEL_INPUT_TIMER3_CH2              = ((uint8_t)0x34U),                
    TRIGSEL_INPUT_TIMER3_CH3              = ((uint8_t)0x35U),                
    TRIGSEL_INPUT_TIMER3_ETI              = ((uint8_t)0x36U),                
    TRIGSEL_INPUT_TIMER4_TRGO0            = ((uint8_t)0x37U),                
    TRIGSEL_INPUT_TIMER4_CH0              = ((uint8_t)0x38U),                
    TRIGSEL_INPUT_TIMER4_CH1              = ((uint8_t)0x39U),                
    TRIGSEL_INPUT_TIMER4_CH2              = ((uint8_t)0x3AU),                
    TRIGSEL_INPUT_TIMER4_CH3              = ((uint8_t)0x3BU),                
    TRIGSEL_INPUT_TIMER4_ETI              = ((uint8_t)0x3CU),                
    TRIGSEL_INPUT_TIMER5_TRGO0            = ((uint8_t)0x3DU),                
    TRIGSEL_INPUT_TIMER6_TRGO0            = ((uint8_t)0x3EU),                
    TRIGSEL_INPUT_TIMER7_TRGO0            = ((uint8_t)0x3FU),                
    TRIGSEL_INPUT_TIMER7_TRGO1            = ((uint8_t)0x40U),                
    TRIGSEL_INPUT_TIMER7_CH0              = ((uint8_t)0x41U),                
    TRIGSEL_INPUT_TIMER7_CH1              = ((uint8_t)0x42U),                
    TRIGSEL_INPUT_TIMER7_CH2              = ((uint8_t)0x43U),                
    TRIGSEL_INPUT_TIMER7_CH3              = ((uint8_t)0x44U),                
    TRIGSEL_INPUT_TIMER7_MCH0             = ((uint8_t)0x45U),                
    TRIGSEL_INPUT_TIMER7_MCH1             = ((uint8_t)0x46U),                
    TRIGSEL_INPUT_TIMER7_MCH2             = ((uint8_t)0x47U),                
    TRIGSEL_INPUT_TIMER7_MCH3             = ((uint8_t)0x48U),                
    TRIGSEL_INPUT_TIMER7_BRKIN0           = ((uint8_t)0x4FU),                
    TRIGSEL_INPUT_TIMER7_BRKIN1           = ((uint8_t)0x50U),                
    TRIGSEL_INPUT_TIMER7_BRKIN2           = ((uint8_t)0x51U),                
    TRIGSEL_INPUT_TIMER7_ETI              = ((uint8_t)0x52U),                
    TRIGSEL_INPUT_TIMER14_TRGO0           = ((uint8_t)0x53U),                
    TRIGSEL_INPUT_TIMER14_CH0             = ((uint8_t)0x54U),                
    TRIGSEL_INPUT_TIMER14_CH1             = ((uint8_t)0x55U),                
    TRIGSEL_INPUT_TIMER14_MCH0            = ((uint8_t)0x56U),                
    TRIGSEL_INPUT_TIMER14_BRKIN0          = ((uint8_t)0x59U),                
    TRIGSEL_INPUT_TIMER15_CH0             = ((uint8_t)0x5AU),                
    TRIGSEL_INPUT_TIMER15_MCH0            = ((uint8_t)0x5BU),                
    TRIGSEL_INPUT_TIMER15_BRKIN0          = ((uint8_t)0x5EU),                
    TRIGSEL_INPUT_TIMER16_CH0             = ((uint8_t)0x5FU),                
    TRIGSEL_INPUT_TIMER16_MCH0            = ((uint8_t)0x60U),                
    TRIGSEL_INPUT_TIMER16_BRKIN0          = ((uint8_t)0x63U),                
    TRIGSEL_INPUT_TIMER22_TRGO0           = ((uint8_t)0x64U),                
    TRIGSEL_INPUT_TIMER22_CH0             = ((uint8_t)0x65U),                
    TRIGSEL_INPUT_TIMER22_CH1             = ((uint8_t)0x66U),                
    TRIGSEL_INPUT_TIMER22_CH2             = ((uint8_t)0x67U),                
    TRIGSEL_INPUT_TIMER22_CH3             = ((uint8_t)0x68U),                
    TRIGSEL_INPUT_TIMER22_ETI             = ((uint8_t)0x69U),                
    TRIGSEL_INPUT_TIMER23_TRGO0           = ((uint8_t)0x6AU),                
    TRIGSEL_INPUT_TIMER23_CH0             = ((uint8_t)0x6BU),                
    TRIGSEL_INPUT_TIMER23_CH1             = ((uint8_t)0x6CU),                
    TRIGSEL_INPUT_TIMER23_CH2             = ((uint8_t)0x6DU),                
    TRIGSEL_INPUT_TIMER23_CH3             = ((uint8_t)0x6EU),                
    TRIGSEL_INPUT_TIMER23_ETI             = ((uint8_t)0x6FU),                
    TRIGSEL_INPUT_TIMER30_TRGO0           = ((uint8_t)0x70U),                
    TRIGSEL_INPUT_TIMER30_CH0             = ((uint8_t)0x71U),                
    TRIGSEL_INPUT_TIMER30_CH1             = ((uint8_t)0x72U),                
    TRIGSEL_INPUT_TIMER30_CH2             = ((uint8_t)0x73U),                
    TRIGSEL_INPUT_TIMER30_CH3             = ((uint8_t)0x74U),                
    TRIGSEL_INPUT_TIMER30_ETI             = ((uint8_t)0x75U),                
    TRIGSEL_INPUT_TIMER31_TRGO0           = ((uint8_t)0x76U),                
    TRIGSEL_INPUT_TIMER31_CH0             = ((uint8_t)0x77U),                
    TRIGSEL_INPUT_TIMER31_CH1             = ((uint8_t)0x78U),                
    TRIGSEL_INPUT_TIMER31_CH2             = ((uint8_t)0x79U),                
    TRIGSEL_INPUT_TIMER31_CH3             = ((uint8_t)0x7AU),                
    TRIGSEL_INPUT_TIMER31_ETI             = ((uint8_t)0x7BU),                
    TRIGSEL_INPUT_TIMER40_TRGO0           = ((uint8_t)0x7CU),                
    TRIGSEL_INPUT_TIMER40_CH0             = ((uint8_t)0x7DU),                
    TRIGSEL_INPUT_TIMER40_CH1             = ((uint8_t)0x7EU),                
    TRIGSEL_INPUT_TIMER40_MCH0            = ((uint8_t)0x7FU),                
    TRIGSEL_INPUT_TIMER40_BRKIN0          = ((uint8_t)0x82U),                
    TRIGSEL_INPUT_TIMER41_TRGO0           = ((uint8_t)0x83U),                
    TRIGSEL_INPUT_TIMER41_CH0             = ((uint8_t)0x84U),                
    TRIGSEL_INPUT_TIMER41_CH1             = ((uint8_t)0x85U),                
    TRIGSEL_INPUT_TIMER41_MCH0            = ((uint8_t)0x86U),                
    TRIGSEL_INPUT_TIMER41_BRKIN0          = ((uint8_t)0x89U),                
    TRIGSEL_INPUT_TIMER42_TRGO0           = ((uint8_t)0x8AU),                
    TRIGSEL_INPUT_TIMER42_CH0             = ((uint8_t)0x8BU),                
    TRIGSEL_INPUT_TIMER42_CH1             = ((uint8_t)0x8CU),                
    TRIGSEL_INPUT_TIMER42_MCH0            = ((uint8_t)0x8DU),                
    TRIGSEL_INPUT_TIMER42_BRKIN0          = ((uint8_t)0x90U),                
    TRIGSEL_INPUT_TIMER43_TRGO0           = ((uint8_t)0x91U),                
    TRIGSEL_INPUT_TIMER43_CH0             = ((uint8_t)0x92U),                
    TRIGSEL_INPUT_TIMER43_CH1             = ((uint8_t)0x93U),                
    TRIGSEL_INPUT_TIMER43_MCH0            = ((uint8_t)0x94U),                
    TRIGSEL_INPUT_TIMER43_BRKIN0          = ((uint8_t)0x97U),                
    TRIGSEL_INPUT_TIMER44_TRGO0           = ((uint8_t)0x98U),                
    TRIGSEL_INPUT_TIMER44_CH0             = ((uint8_t)0x99U),                
    TRIGSEL_INPUT_TIMER44_CH1             = ((uint8_t)0x9AU),                
    TRIGSEL_INPUT_TIMER44_MCH0            = ((uint8_t)0x9BU),                
    TRIGSEL_INPUT_TIMER44_BRKIN0          = ((uint8_t)0x9EU),                
    TRIGSEL_INPUT_TIMER50_TRGO0           = ((uint8_t)0x9FU),                
    TRIGSEL_INPUT_TIMER51_TRGO0           = ((uint8_t)0xA0U),                
    TRIGSEL_INPUT_RTC_ALARM               = ((uint8_t)0xA1U),                
    TRIGSEL_INPUT_RTC_TPTS                = ((uint8_t)0xA2U),                
    TRIGSEL_INPUT_ADC0_WD0_OUT            = ((uint8_t)0xA3U),                
    TRIGSEL_INPUT_ADC0_WD1_OUT            = ((uint8_t)0xA4U),                
    TRIGSEL_INPUT_ADC0_WD2_OUT            = ((uint8_t)0xA5U),                
    TRIGSEL_INPUT_ADC1_WD0_OUT            = ((uint8_t)0xA6U),                
    TRIGSEL_INPUT_ADC1_WD1_OUT            = ((uint8_t)0xA7U),                
    TRIGSEL_INPUT_ADC1_WD2_OUT            = ((uint8_t)0xA8U),                
    TRIGSEL_INPUT_ADC2_WD0_OUT            = ((uint8_t)0xA9U),                
    TRIGSEL_INPUT_ADC2_WD1_OUT            = ((uint8_t)0xAAU),                
    TRIGSEL_INPUT_ADC2_WD2_OUT            = ((uint8_t)0xABU),                
    TRIGSEL_INPUT_CMP0_OUT                = ((uint8_t)0xACU),                
    TRIGSEL_INPUT_CMP1_OUT                = ((uint8_t)0xADU),                
    TRIGSEL_INPUT_SAI0_FS0                = ((uint8_t)0xAEU),                
    TRIGSEL_INPUT_SAI0_FS1                = ((uint8_t)0xAFU),                
    TRIGSEL_INPUT_SAI2_FS0                = ((uint8_t)0xB0U),                
    TRIGSEL_INPUT_SAI2_FS1                = ((uint8_t)0xB1U),                
}trigsel_source_enum;

 
typedef enum
{
    TRIGSEL_OUTPUT_TRIGSEL_OUT0           = ((uint8_t)0x00U),                
    TRIGSEL_OUTPUT_TRIGSEL_OUT1           = ((uint8_t)0x01U),                
    TRIGSEL_OUTPUT_TRIGSEL_OUT2           = ((uint8_t)0x04U),                
    TRIGSEL_OUTPUT_TRIGSEL_OUT3           = ((uint8_t)0x05U),                
    TRIGSEL_OUTPUT_TRIGSEL_OUT4           = ((uint8_t)0x08U),                
    TRIGSEL_OUTPUT_TRIGSEL_OUT5           = ((uint8_t)0x09U),                
    TRIGSEL_OUTPUT_TRIGSEL_OUT6           = ((uint8_t)0x0CU),                
    TRIGSEL_OUTPUT_TRIGSEL_OUT7           = ((uint8_t)0x0DU),                
    TRIGSEL_OUTPUT_ADC0_REGTRG            = ((uint8_t)0x10U),                
    TRIGSEL_OUTPUT_ADC0_INSTRG            = ((uint8_t)0x11U),                
    TRIGSEL_OUTPUT_ADC1_REGTRG            = ((uint8_t)0x14U),                
    TRIGSEL_OUTPUT_ADC1_INSTRG            = ((uint8_t)0x15U),                
    TRIGSEL_OUTPUT_ADC2_REGTRG            = ((uint8_t)0x18U),                
    TRIGSEL_OUTPUT_ADC2_INSTRG            = ((uint8_t)0x19U),                
    TRIGSEL_OUTPUT_DAC0_OUT0_EXTRG        = ((uint8_t)0x1CU),                
    TRIGSEL_OUTPUT_DAC0_OUT1_EXTRG        = ((uint8_t)0x20U),                
    TRIGSEL_OUTPUT_TIMER0_BRKIN0          = ((uint8_t)0x24U),                
    TRIGSEL_OUTPUT_TIMER0_BRKIN1          = ((uint8_t)0x25U),                
    TRIGSEL_OUTPUT_TIMER0_BRKIN2          = ((uint8_t)0x26U),                
    TRIGSEL_OUTPUT_TIMER7_BRKIN0          = ((uint8_t)0x28U),                
    TRIGSEL_OUTPUT_TIMER7_BRKIN1          = ((uint8_t)0x29U),                
    TRIGSEL_OUTPUT_TIMER7_BRKIN2          = ((uint8_t)0x2AU),                
    TRIGSEL_OUTPUT_TIMER14_BRKIN0         = ((uint8_t)0x2CU),                
    TRIGSEL_OUTPUT_TIMER15_BRKIN0         = ((uint8_t)0x30U),                
    TRIGSEL_OUTPUT_TIMER16_BRKIN0         = ((uint8_t)0x34U),                
    TRIGSEL_OUTPUT_TIMER40_BRKIN0         = ((uint8_t)0x38U),                
    TRIGSEL_OUTPUT_TIMER41_BRKIN0         = ((uint8_t)0x3CU),                
    TRIGSEL_OUTPUT_TIMER42_BRKIN0         = ((uint8_t)0x40U),                
    TRIGSEL_OUTPUT_TIMER43_BRKIN0         = ((uint8_t)0x44U),                
    TRIGSEL_OUTPUT_TIMER44_BRKIN0         = ((uint8_t)0x48U),                
    TRIGSEL_OUTPUT_CAN0_EX_TIME_TICK      = ((uint8_t)0x4CU),                
    TRIGSEL_OUTPUT_CAN1_EX_TIME_TICK      = ((uint8_t)0x50U),                
    TRIGSEL_OUTPUT_CAN2_EX_TIME_TICK      = ((uint8_t)0x54U),                
    TRIGSEL_OUTPUT_LPDTS_TRG              = ((uint8_t)0x58U),                
    TRIGSEL_OUTPUT_TIMER0_ETI             = ((uint8_t)0x5CU),                
    TRIGSEL_OUTPUT_TIMER1_ETI             = ((uint8_t)0x60U),                
    TRIGSEL_OUTPUT_TIMER2_ETI             = ((uint8_t)0x64U),                
    TRIGSEL_OUTPUT_TIMER3_ETI             = ((uint8_t)0x68U),                
    TRIGSEL_OUTPUT_TIMER4_ETI             = ((uint8_t)0x6CU),                
    TRIGSEL_OUTPUT_TIMER7_ETI             = ((uint8_t)0x70U),                
    TRIGSEL_OUTPUT_TIMER22_ETI            = ((uint8_t)0x74U),                
    TRIGSEL_OUTPUT_TIMER23_ETI            = ((uint8_t)0x78U),                
    TRIGSEL_OUTPUT_TIMER30_ETI            = ((uint8_t)0x7CU),                
    TRIGSEL_OUTPUT_TIMER31_ETI            = ((uint8_t)0x80U),                
    TRIGSEL_OUTPUT_EDOUT_TRG              = ((uint8_t)0x84U),                
    TRIGSEL_OUTPUT_HPDF_ITR               = ((uint8_t)0x88U),                
    TRIGSEL_OUTPUT_TIMER0_ITI14           = ((uint8_t)0x8CU),                
    TRIGSEL_OUTPUT_TIMER1_ITI14           = ((uint8_t)0x90U),                
    TRIGSEL_OUTPUT_TIMER2_ITI14           = ((uint8_t)0x94U),                
    TRIGSEL_OUTPUT_TIMER3_ITI14           = ((uint8_t)0x98U),                
    TRIGSEL_OUTPUT_TIMER4_ITI14           = ((uint8_t)0x9CU),                
    TRIGSEL_OUTPUT_TIMER7_ITI14           = ((uint8_t)0xA0U),                
    TRIGSEL_OUTPUT_TIMER14_ITI14          = ((uint8_t)0xA4U),                
    TRIGSEL_OUTPUT_TIMER22_ITI14          = ((uint8_t)0xA8U),                
    TRIGSEL_OUTPUT_TIMER23_ITI14          = ((uint8_t)0xACU),                
    TRIGSEL_OUTPUT_TIMER30_ITI14          = ((uint8_t)0xB0U),                
    TRIGSEL_OUTPUT_TIMER31_ITI14          = ((uint8_t)0xB4U),                
    TRIGSEL_OUTPUT_TIMER40_ITI14          = ((uint8_t)0xB8U),                
    TRIGSEL_OUTPUT_TIMER41_ITI14          = ((uint8_t)0xBCU),                
    TRIGSEL_OUTPUT_TIMER42_ITI14          = ((uint8_t)0xC0U),                
    TRIGSEL_OUTPUT_TIMER43_ITI14          = ((uint8_t)0xC4U),                
    TRIGSEL_OUTPUT_TIMER44_ITI14          = ((uint8_t)0xC8U),                
}trigsel_periph_enum;

 
 
void trigsel_deinit(void);
 
void trigsel_init(trigsel_periph_enum target_periph, trigsel_source_enum trigger_source);
 
trigsel_source_enum trigsel_trigger_source_get(trigsel_periph_enum target_periph);
 
void trigsel_register_lock_set(trigsel_periph_enum target_periph);
 
FlagStatus trigsel_register_lock_get(trigsel_periph_enum target_periph);






 


























 



 

 

 
 

 

 

 

 

 

 

 
 
typedef enum
{
    TRNG_INMOD_256BIT = 0,                                      
    TRNG_INMOD_440BIT = ((uint32_t)((uint32_t)0x01U << (15)))                          
}trng_inmod_enum;

 
typedef enum
{
    TRNG_OUTMOD_128BIT = 0,                                     
    TRNG_OUTMOD_256BIT = ((uint32_t)((uint32_t)0x01U << (14)))                        
}trng_outmod_enum;

 
typedef enum
{
    TRNG_MODSEL_LFSR = 0,                                       
    TRNG_MODSEL_NIST = ((uint32_t)((uint32_t)0x01U << (4)))                          
}trng_modsel_enum;

 
typedef enum
{ 
    TRNG_FLAG_DRDY = ((uint32_t)((uint32_t)0x01U << (0))),                            
    TRNG_FLAG_CECS = ((uint32_t)((uint32_t)0x01U << (1))),                            
    TRNG_FLAG_SECS = ((uint32_t)((uint32_t)0x01U << (2)))                             
}trng_flag_enum;

 
typedef enum
{
    TRNG_INT_FLAG_CEIF = ((uint32_t)((uint32_t)0x01U << (5))),                        
    TRNG_INT_FLAG_SEIF = ((uint32_t)((uint32_t)0x01U << (6)))                         
}trng_int_flag_enum;


 
 
 
void trng_deinit(void);
 
void trng_enable(void);
 
void trng_disable(void);
 
void trng_lock(void);
 
void trng_mode_config(trng_modsel_enum mode_select);
 
void trng_postprocessing_enable(void);
 
void trng_postprocessing_disable(void);
 
void trng_conditioning_enable(void);
 
void trng_conditioning_disable(void);
 
void trng_conditioning_input_bitwidth(trng_inmod_enum input_bitwidth);
 
void trng_conditioning_output_bitwidth(trng_outmod_enum output_bitwidth);
 
void trng_replace_test_enable(void);
 
void trng_replace_test_disable(void);
 
void trng_hash_init_enable(void);
 
void trng_hash_init_disable(void);
 
void trng_powermode_config(uint32_t powermode);
 
void trng_clockdiv_config(uint32_t clkdiv);
 
void trng_clockerror_detection_enable(void);
 
void trng_clockerror_detection_disable(void);
 
uint32_t trng_get_true_random_data(void);

 
 
void trng_conditioning_reset_enable(void);
 
void trng_conditioning_reset_disable(void);
 
void trng_conditioning_algo_config(uint32_t module_algo);
 
void trng_health_tests_config(uint32_t adpo_threshold, uint8_t rep_threshold);

 
 
FlagStatus trng_flag_get(trng_flag_enum flag);
 
void trng_interrupt_enable(void);
 
void trng_interrupt_disable(void);
 
FlagStatus trng_interrupt_flag_get(trng_int_flag_enum int_flag);
 
void trng_interrupt_flag_clear(trng_int_flag_enum int_flag);






 


























 



 

 

 
 

 

 

 

 

 

 

 

 

 

 

 

 

 
 

 

 
typedef enum{
     
    USART_FLAG_REA = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(22U)),           
    USART_FLAG_TEA = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(21U)),           
    USART_FLAG_WU = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(20U)),            
    USART_FLAG_RWU = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(19U)),           
    USART_FLAG_SB = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(18U)),            
    USART_FLAG_AM0 = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(17U)),           
    USART_FLAG_BSY = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(16U)),           
    USART_FLAG_AM1 = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(13U)),           
    USART_FLAG_EB = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(12U)),            
    USART_FLAG_RT = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(11U)),            
    USART_FLAG_CTS = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(10U)),           
    USART_FLAG_CTSF = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(9U)),           
    USART_FLAG_LBD = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(8U)),            
    USART_FLAG_TBE = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(7U)),            
    USART_FLAG_TFNF = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(7U)),           
    USART_FLAG_TC = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(6U)),             
    USART_FLAG_RBNE = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(5U)),           
    USART_FLAG_RFNE = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(5U)),           
    USART_FLAG_IDLE = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(4U)),           
    USART_FLAG_ORERR = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(3U)),          
    USART_FLAG_NERR = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(2U)),           
    USART_FLAG_FERR = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(1U)),           
    USART_FLAG_PERR = (((uint32_t)(((uint32_t)0x0000001CU)) << 6) | (uint32_t)(0U)),           
     
    USART_FLAG_EPERR = (((uint32_t)(((uint32_t)0x000000C0U)) << 6) | (uint32_t)(8U)),           
     
    USART_FLAG_RFF = (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(11U)),            
    USART_FLAG_RFE = (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(10U)),            
    USART_FLAG_TFF = (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(7U)),             
    USART_FLAG_TFE = (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(6U)),             
    USART_FLAG_TFT = (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(5U)),             
    USART_FLAG_RFT = (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(4U))              
}usart_flag_enum;

 
typedef enum
{
     
    USART_INT_FLAG_AM1 = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((13U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(31U))),       
    USART_INT_FLAG_EB = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((12U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(27U))),        
    USART_INT_FLAG_RT = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((11U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(26U))),        
    USART_INT_FLAG_AM0 = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((17U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(14U))),       
    USART_INT_FLAG_PERR = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((0U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(8U))),        
    USART_INT_FLAG_TBE = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((7U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(7U))),         
    USART_INT_FLAG_TFNF = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((7U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(7U))),        
    USART_INT_FLAG_TC = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((6U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(6U))),          
    USART_INT_FLAG_RBNE = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((5U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(5U))),        
    USART_INT_FLAG_RFNE = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((5U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(5U))),        
    USART_INT_FLAG_RBNE_ORERR = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((3U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(5U))),  
    USART_INT_FLAG_RFNE_ORERR = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((3U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(5U))),  
    USART_INT_FLAG_IDLE = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((4U) << 16) | (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(4U))),        
     
    USART_INT_FLAG_LBD = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((8U) << 16) | (((uint32_t)(((uint32_t)0x00000004U)) << 6) | (uint32_t)(6U))),         
     
    USART_INT_FLAG_WU = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((20U) << 16) | (((uint32_t)(((uint32_t)0x00000008U)) << 6) | (uint32_t)(22U))),        
    USART_INT_FLAG_CTS = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((9U) << 16) | (((uint32_t)(((uint32_t)0x00000008U)) << 6) | (uint32_t)(10U))),        
    USART_INT_FLAG_ERR_NERR = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((2U) << 16) | (((uint32_t)(((uint32_t)0x00000008U)) << 6) | (uint32_t)(0U))),    
    USART_INT_FLAG_ERR_ORERR = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((3U) << 16) | (((uint32_t)(((uint32_t)0x00000008U)) << 6) | (uint32_t)(0U))),   
    USART_INT_FLAG_ERR_FERR = (((uint32_t)(((uint32_t)0x0000001CU)) << 22) | (uint32_t)((1U) << 16) | (((uint32_t)(((uint32_t)0x00000008U)) << 6) | (uint32_t)(0U))),    
     
    USART_INT_FLAG_TFT = (((uint32_t)(((uint32_t)0x000000D0U)) << 22) | (uint32_t)((25U) << 16) | (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(29U))),         
    USART_INT_FLAG_TFE = (((uint32_t)(((uint32_t)0x000000D0U)) << 22) | (uint32_t)((24U) << 16) | (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(31U))),         
    USART_INT_FLAG_RFT = (((uint32_t)(((uint32_t)0x000000D0U)) << 22) | (uint32_t)((22U) << 16) | (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(27U))),         
    USART_INT_FLAG_RFF = (((uint32_t)(((uint32_t)0x000000D0U)) << 22) | (uint32_t)((15U) << 16) | (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(9U)))           
}usart_interrupt_flag_enum;

 
typedef enum
{
     
    USART_INT_AM1 = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(31U)),           
    USART_INT_EB = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(27U)),            
    USART_INT_RT = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(26U)),            
    USART_INT_AM0 = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(14U)),           
    USART_INT_PERR = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(8U)),           
    USART_INT_TBE = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(7U)),            
    USART_INT_TFNF = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(7U)),           
    USART_INT_TC = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(6U)),             
    USART_INT_RBNE = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(5U)),           
    USART_INT_RFNE = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(5U)),           
    USART_INT_IDLE = (((uint32_t)(((uint32_t)0x00000000U)) << 6) | (uint32_t)(4U)),           
     
    USART_INT_LBD = (((uint32_t)(((uint32_t)0x00000004U)) << 6) | (uint32_t)(6U)),            
     
    USART_INT_WU = (((uint32_t)(((uint32_t)0x00000008U)) << 6) | (uint32_t)(22U)),            
    USART_INT_CTS = (((uint32_t)(((uint32_t)0x00000008U)) << 6) | (uint32_t)(10U)),           
    USART_INT_ERR = (((uint32_t)(((uint32_t)0x00000008U)) << 6) | (uint32_t)(0U)),            
     
    USART_INT_TFE = (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(31U)),            
    USART_INT_TFT = (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(29U)),            
    USART_INT_RFT = (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(27U)),            
    USART_INT_RFF = (((uint32_t)(((uint32_t)0x000000D0U)) << 6) | (uint32_t)(9U))              
}usart_interrupt_enum;

 
typedef enum {
     
    USART_DINV_ENABLE,                                                      
    USART_DINV_DISABLE,                                                     
     
    USART_TXPIN_ENABLE,                                                     
    USART_TXPIN_DISABLE,                                                    
     
    USART_RXPIN_ENABLE,                                                     
    USART_RXPIN_DISABLE,                                                    
     
    USART_SWAP_ENABLE,                                                      
    USART_SWAP_DISABLE                                                      
}usart_invert_enum;

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 
 
 
void usart_deinit(uint32_t usart_periph);
 
void usart_baudrate_set(uint32_t usart_periph, uint32_t baudval);
 
void usart_parity_config(uint32_t usart_periph, uint32_t paritycfg);
 
void usart_word_length_set(uint32_t usart_periph, uint32_t wlen);
 
void usart_stop_bit_set(uint32_t usart_periph, uint32_t stblen);
 
void usart_enable(uint32_t usart_periph);
 
void usart_disable(uint32_t usart_periph);
 
void usart_transmit_config(uint32_t usart_periph, uint32_t txconfig);
 
void usart_receive_config(uint32_t usart_periph, uint32_t rxconfig);

 
 
void usart_data_first_config(uint32_t usart_periph, uint32_t msbf);
 
void usart_invert_config(uint32_t usart_periph, usart_invert_enum invertpara);
 
void usart_overrun_enable(uint32_t usart_periph);
 
void usart_overrun_disable(uint32_t usart_periph);
 
void usart_oversample_config(uint32_t usart_periph, uint32_t oversamp);
 
void usart_sample_bit_config(uint32_t usart_periph, uint32_t osb);
 
void usart_receiver_timeout_enable(uint32_t usart_periph);
 
void usart_receiver_timeout_disable(uint32_t usart_periph);
 
void usart_receiver_timeout_threshold_config(uint32_t usart_periph, uint32_t rtimeout);
 
void usart_data_transmit(uint32_t usart_periph, uint16_t data);
 
uint16_t usart_data_receive(uint32_t usart_periph);
 
void usart_command_enable(uint32_t usart_periph, uint32_t cmdtype);

 
 
void usart_address_0_match_mode_enable(uint32_t usart_periph);
 
void usart_address_0_match_mode_disable(uint32_t usart_periph);
 
void usart_address_1_match_mode_enable(uint32_t usart_periph);
 
void usart_address_1_match_mode_disable(uint32_t usart_periph);
 
void usart_address_0_config(uint32_t usart_periph, uint8_t addr);
 
void usart_address_1_config(uint32_t usart_periph, uint8_t addr);
 
void usart_address_0_detection_mode_config(uint32_t usart_periph, uint32_t addmod);
 
void usart_address_1_detection_mode_config(uint32_t usart_periph, uint32_t addmod);
 
void usart_mute_mode_enable(uint32_t usart_periph);
 
void usart_mute_mode_disable(uint32_t usart_periph);
 
void usart_mute_mode_wakeup_config(uint32_t usart_periph, uint32_t wmethod);

 
 
void usart_lin_mode_enable(uint32_t usart_periph);
 
void usart_lin_mode_disable(uint32_t usart_periph);
 
void usart_lin_break_detection_length_config(uint32_t usart_periph, uint32_t lblen);

 
 
void usart_halfduplex_enable(uint32_t usart_periph);
 
void usart_halfduplex_disable(uint32_t usart_periph);

 
 
void usart_clock_enable(uint32_t usart_periph);
 
void usart_clock_disable(uint32_t usart_periph);
 
void usart_synchronous_clock_config(uint32_t usart_periph, uint32_t clen, uint32_t cph, uint32_t cpl);

 
 
void usart_guard_time_config(uint32_t usart_periph, uint32_t guat);
 
void usart_smartcard_mode_enable(uint32_t usart_periph);
 
void usart_smartcard_mode_disable(uint32_t usart_periph);
 
void usart_smartcard_mode_nack_enable(uint32_t usart_periph);
 
void usart_smartcard_mode_nack_disable(uint32_t usart_periph);
 
void usart_smartcard_mode_early_nack_enable(uint32_t usart_periph);
 
void usart_smartcard_mode_early_nack_disable(uint32_t usart_periph);
 
void usart_smartcard_autoretry_config(uint32_t usart_periph, uint32_t scrtnum);
 
void usart_block_length_config(uint32_t usart_periph, uint32_t bl);

 
 
void usart_irda_mode_enable(uint32_t usart_periph);
 
void usart_irda_mode_disable(uint32_t usart_periph);
 
void usart_prescaler_config(uint32_t usart_periph, uint32_t psc);
 
void usart_irda_lowpower_config(uint32_t usart_periph, uint32_t irlp);

 
 
void usart_hardware_flow_rts_config(uint32_t usart_periph, uint32_t rtsconfig);
 
void usart_hardware_flow_cts_config(uint32_t usart_periph, uint32_t ctsconfig);

 
 
void usart_hardware_flow_coherence_config(uint32_t usart_periph, uint32_t hcm);

 
void usart_rs485_driver_enable(uint32_t usart_periph);
 
void usart_rs485_driver_disable(uint32_t usart_periph);
 
void usart_driver_assertime_config(uint32_t usart_periph, uint32_t deatime);
 
void usart_driver_deassertime_config(uint32_t usart_periph, uint32_t dedtime);
 
void usart_depolarity_config(uint32_t usart_periph, uint32_t dep);

 
 
void usart_dma_receive_config(uint32_t usart_periph, uint32_t dmacmd);
 
void usart_dma_transmit_config(uint32_t usart_periph, uint32_t dmacmd);
 
void usart_reception_error_dma_disable(uint32_t usart_periph);
 
void usart_reception_error_dma_enable(uint32_t usart_periph);

 
void usart_wakeup_enable(uint32_t usart_periph);
 
void usart_wakeup_disable(uint32_t usart_periph);
 
void usart_wakeup_mode_config(uint32_t usart_periph, uint32_t wum);

 
 
void usart_fifo_enable(uint32_t usart_periph);
 
void usart_fifo_disable(uint32_t usart_periph);
 
void usart_transmit_fifo_threshold_config(uint32_t usart_periph, uint32_t txthreshold);
 
void usart_receive_fifo_threshold_config(uint32_t usart_periph, uint32_t rxthreshold);
 
uint8_t usart_receive_fifo_counter_number(uint32_t usart_periph);

 
 
FlagStatus usart_flag_get(uint32_t usart_periph, usart_flag_enum flag);
 
void usart_flag_clear(uint32_t usart_periph, usart_flag_enum flag);
 
void usart_interrupt_enable(uint32_t usart_periph, usart_interrupt_enum interrupt);
 
void usart_interrupt_disable(uint32_t usart_periph, usart_interrupt_enum interrupt);
 
FlagStatus usart_interrupt_flag_get(uint32_t usart_periph, usart_interrupt_flag_enum int_flag);
 
void usart_interrupt_flag_clear(uint32_t usart_periph, usart_interrupt_flag_enum int_flag);






 


























 



 

 

 
 

 

 
 

 

 
 
void vref_deinit(void);
 
void vref_enable(void);
 
void vref_disable(void);
 
void vref_high_impedance_mode_enable(void);
 
void vref_high_impedance_mode_disable(void);
 
FlagStatus vref_status_get(void);
 
void vref_voltage_select(uint32_t vref_voltage);
 
void vref_calib_value_set(uint8_t value);
 
uint8_t vref_calib_value_get(void);






 


























 



 

 

 
 

 

 

 

 
 
void wwdgt_deinit(void);
 
void wwdgt_enable(void);

 
void wwdgt_counter_update(uint16_t counter_value);
 
void wwdgt_config(uint16_t counter, uint16_t window, uint32_t prescaler);

 
void wwdgt_interrupt_enable(void);
 
FlagStatus wwdgt_flag_get(void);
 
void wwdgt_flag_clear(void);





 

 

 
 

 

 

 

 

 

 

 

 
 

 
typedef enum {
    SEM0 = 0U,                                                              
    SEM1,                                                                   
    SEM2,                                                                   
    SEM3,                                                                   
    SEM4,                                                                   
    SEM5,                                                                   
    SEM6,                                                                   
    SEM7,                                                                   
    SEM8,                                                                   
    SEM9,                                                                   
    SEM10,                                                                  
    SEM11,                                                                  
    SEM12,                                                                  
    SEM13,                                                                  
    SEM14,                                                                  
    SEM15,                                                                  
    SEM16,                                                                  
    SEM17,                                                                  
    SEM18,                                                                  
    SEM19,                                                                  
    SEM20,                                                                  
    SEM21,                                                                  
    SEM22,                                                                  
    SEM23,                                                                  
    SEM24,                                                                  
    SEM25,                                                                  
    SEM26,                                                                  
    SEM27,                                                                  
    SEM28,                                                                  
    SEM29,                                                                  
    SEM30,                                                                  
    SEM31                                                                   
} hwsem_semaphore_enum;

 

 

 

 

 
 
 
ErrStatus hwsem_lock_set(hwsem_semaphore_enum semaphore, uint8_t process);
 
ErrStatus hwsem_lock_release(hwsem_semaphore_enum semaphore, uint8_t process);
 
ErrStatus hwsem_lock_by_reading(hwsem_semaphore_enum semaphore);
 
ErrStatus hwsem_unlock_all(uint16_t key);

 
 
uint32_t hwsem_process_id_get(hwsem_semaphore_enum semaphore);
 
uint32_t hwsem_master_id_get(hwsem_semaphore_enum semaphore);
 
FlagStatus hwsem_lock_status_get(hwsem_semaphore_enum semaphore);
 
void hwsem_key_set(uint16_t key);
 
uint16_t hwsem_key_get(void);

 
 
FlagStatus hwsem_flag_get(hwsem_semaphore_enum semaphore);
 
void hwsem_flag_clear(hwsem_semaphore_enum semaphore);
 
FlagStatus hwsem_interrupt_flag_get(hwsem_semaphore_enum semaphore);
 
void hwsem_interrupt_flag_clear(hwsem_semaphore_enum semaphore);
 
void hwsem_interrupt_enable(hwsem_semaphore_enum semaphore);
 
void hwsem_interrupt_disable(hwsem_semaphore_enum semaphore);












 
ErrStatus hwsem_lock_set(hwsem_semaphore_enum semaphore, uint8_t process)
{
    uint32_t temp_mid = 0U, temp_pid = 0U;
    ErrStatus ret = ERROR;

     
    (*(volatile uint32_t *)(uint32_t)((((((uint32_t)0x58020000U) + 0x00006400U)) + 0x0U) + 0x4U * (semaphore))) = (uint32_t)(((uint32_t)((uint32_t)0x01U << (31))) | (((0xFFFFFFFFUL << (8)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(11)))) & ((uint32_t)(0x0BU) << 8)) | (((0xFFFFFFFFUL << (0)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(7)))) & ((uint32_t)(process) << 0)));

     
    temp_mid = hwsem_master_id_get(semaphore);
    temp_pid = hwsem_process_id_get(semaphore);
    if((0x0BU == temp_mid) && (process == temp_pid)) {
        ret = SUCCESS;
    }

    return ret;
}











 
ErrStatus hwsem_lock_release(hwsem_semaphore_enum semaphore, uint8_t process)
{
    uint32_t lock_state = 0U;
    ErrStatus ret = ERROR;

    (*(volatile uint32_t *)(uint32_t)((((((uint32_t)0x58020000U) + 0x00006400U)) + 0x0U) + 0x4U * (semaphore))) = (uint32_t)((((0xFFFFFFFFUL << (8)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(11)))) & ((uint32_t)(0x0BU) << 8)) | (((0xFFFFFFFFUL << (0)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(7)))) & ((uint32_t)(process) << 0)));

    lock_state = (*(volatile uint32_t *)(uint32_t)((((((uint32_t)0x58020000U) + 0x00006400U)) + 0x0U) + 0x4U * (semaphore))) & ((uint32_t)((uint32_t)0x01U << (31)));
    if(0U == lock_state) {
        ret = SUCCESS;
    }

    return ret;
}








 
ErrStatus hwsem_lock_by_reading(hwsem_semaphore_enum semaphore)
{
    ErrStatus ret = ERROR;

    if((uint32_t)(((uint32_t)((uint32_t)0x01U << (31))) | (((0xFFFFFFFFUL << (8)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(11)))) & ((uint32_t)(0x0BU) << 8))) == (*(volatile uint32_t *)(uint32_t)((((((uint32_t)0x58020000U) + 0x00006400U)) + 0x80U) + 0x4U * (semaphore)))) {
        ret = SUCCESS;
    }

    return ret;
}







 
ErrStatus hwsem_unlock_all(uint16_t key)
{
    ErrStatus ret = ERROR;

    (*(volatile uint32_t *)(uint32_t)((((uint32_t)0x58020000U) + 0x00006400U) + 0x00000140U)) = (((0xFFFFFFFFUL << (16)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(31)))) & ((uint32_t)(key) << 16)) | (((0xFFFFFFFFUL << (8)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(11)))) & ((uint32_t)(0x0BU) << 8));

    if(key == hwsem_key_get()) {
        ret = SUCCESS;
    }
    return ret;
}








 
uint32_t hwsem_process_id_get(hwsem_semaphore_enum semaphore)
{
    return (uint32_t)((((((*(volatile uint32_t *)(uint32_t)((((((uint32_t)0x58020000U) + 0x00006400U)) + 0x0U) + 0x4U * (semaphore))))) & ((0xFFFFFFFFUL << ((0))) & (0xFFFFFFFFUL >> (31U - (uint32_t)((7)))))) >> (0)));
}








 
uint32_t hwsem_master_id_get(hwsem_semaphore_enum semaphore)
{
    return (uint32_t)((((((*(volatile uint32_t *)(uint32_t)((((((uint32_t)0x58020000U) + 0x00006400U)) + 0x0U) + 0x4U * (semaphore))))) & ((0xFFFFFFFFUL << ((8))) & (0xFFFFFFFFUL >> (31U - (uint32_t)((11)))))) >> (8)));
}








 
FlagStatus hwsem_lock_status_get(hwsem_semaphore_enum semaphore)
{
    FlagStatus ret = RESET;

    if(0U != ((*(volatile uint32_t *)(uint32_t)((((((uint32_t)0x58020000U) + 0x00006400U)) + 0x0U) + 0x4U * (semaphore))) & ((uint32_t)((uint32_t)0x01U << (31))))) {
        ret = SET;
    }

    return ret;
}







 
void hwsem_key_set(uint16_t key)
{
    (*(volatile uint32_t *)(uint32_t)((((uint32_t)0x58020000U) + 0x00006400U) + 0x00000144U)) = (((0xFFFFFFFFUL << (16)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(31)))) & ((uint32_t)(key) << 16));
}






 
uint16_t hwsem_key_get(void)
{
    return ((uint16_t)(((((*(volatile uint32_t *)(uint32_t)((((uint32_t)0x58020000U) + 0x00006400U) + 0x00000144U)))) & ((0xFFFFFFFFUL << ((16))) & (0xFFFFFFFFUL >> (31U - (uint32_t)((31)))))) >> (16)));
}








 
FlagStatus hwsem_flag_get(hwsem_semaphore_enum semaphore)
{
    FlagStatus ret = RESET;

    if(RESET != (((*(volatile uint32_t *)(uint32_t)((((uint32_t)0x58020000U) + 0x00006400U) + 0x00000108U)) >> semaphore) & 0x1U)) {
        return SET;
    }

    return ret;
}








 
void hwsem_flag_clear(hwsem_semaphore_enum semaphore)
{
    (*(volatile uint32_t *)(uint32_t)((((uint32_t)0x58020000U) + 0x00006400U) + 0x00000104U)) = (1U << semaphore);
}








 
FlagStatus hwsem_interrupt_flag_get(hwsem_semaphore_enum semaphore)
{
    FlagStatus ret = RESET;

    if(RESET != (((*(volatile uint32_t *)(uint32_t)((((uint32_t)0x58020000U) + 0x00006400U) + 0x0000010CU)) >> semaphore) & 0x1U)) {
        ret = SET;
    }

    return ret;
}








 
void hwsem_interrupt_flag_clear(hwsem_semaphore_enum semaphore)
{
    (*(volatile uint32_t *)(uint32_t)((((uint32_t)0x58020000U) + 0x00006400U) + 0x00000104U)) = (1U << semaphore);
}








 
void hwsem_interrupt_enable(hwsem_semaphore_enum semaphore)
{
    (*(volatile uint32_t *)(uint32_t)((((uint32_t)0x58020000U) + 0x00006400U) + 0x00000100U)) |= (1U << semaphore);
}








 
void hwsem_interrupt_disable(hwsem_semaphore_enum semaphore)
{
    (*(volatile uint32_t *)(uint32_t)((((uint32_t)0x58020000U) + 0x00006400U) + 0x00000100U)) &= (uint32_t)(~((uint32_t)1U << semaphore));
}
