#Generating source browse information for project gd32h7_test


#Abbreviations
cc = D$:\Program$ Files$ (x86)\IAR$ Systems\Embedded$ Workbench$ 8.4\common\bin\SourceIndexer.exe
ll = D$:\Program$ Files$ (x86)\IAR$ Systems\Embedded$ Workbench$ 8.4\common\bin\PbdLink.exe
bd = D$:\Program$ Files$ (x86)\IAR$ Systems\Embedded$ Workbench$ 8.4\common\bin\makeBrowseData.exe



#Rules
rule index
  depfile = $out.dep
  command = $cc -out=$out -f $in
rule link
  command = $ll -M $out $in
rule browsedata
  command = $bd $in -output  $out



#Build steps
build AnalogIn.pbi : index AnalogIn.xcl
build AnalogOut.pbi : index AnalogOut.xcl
build BufferedSerial.pbi : index BufferedSerial.xcl
build CAN.pbi : index CAN.xcl
build DigitalIn.pbi : index DigitalIn.xcl
build DigitalInOut.pbi : index DigitalInOut.xcl
build DigitalOut.pbi : index DigitalOut.xcl
build FlashIAP.pbi : index FlashIAP.xcl
build I2C.pbi : index I2C.xcl
build InterruptIn.pbi : index InterruptIn.xcl
build PwmOut.pbi : index PwmOut.xcl
build SerialBase.pbi : index SerialBase.xcl
build SPI.pbi : index SPI.xcl
build TimerEvent.pbi : index TimerEvent.xcl
build LowPowerTickerWrapper.pbi : index LowPowerTickerWrapper.xcl
build mbed_compat.pbi : index mbed_compat.xcl
build mbed_critical_section_api.pbi : index mbed_critical_section_api.xcl
build mbed_flash_api.pbi : index mbed_flash_api.xcl
build mbed_gpio.pbi : index mbed_gpio.xcl
build mbed_gpio_irq.pbi : index mbed_gpio_irq.xcl
build mbed_itm_api.pbi : index mbed_itm_api.xcl
build mbed_lp_ticker_api.pbi : index mbed_lp_ticker_api.xcl
build mbed_lp_ticker_wrapper.pbi : index mbed_lp_ticker_wrapper.xcl
build mbed_pinmap_common.pbi : index mbed_pinmap_common.xcl
build mbed_pinmap_default.pbi : index mbed_pinmap_default.xcl
build mbed_ticker_api.pbi : index mbed_ticker_api.xcl
build mbed_us_ticker_api.pbi : index mbed_us_ticker_api.xcl
build static_pinmap.pbi : index static_pinmap.xcl
build ATCmdParser.pbi : index ATCmdParser.xcl
build CriticalSectionLock.pbi : index CriticalSectionLock.xcl
build CThunkBase.pbi : index CThunkBase.xcl
build DeepSleepLock.pbi : index DeepSleepLock.xcl
build FileBase.pbi : index FileBase.xcl
build FileHandle.pbi : index FileHandle.xcl
build FilePath.pbi : index FilePath.xcl
build FileSystemHandle.pbi : index FileSystemHandle.xcl
build LocalFileSystem.pbi : index LocalFileSystem.xcl
build mbed_alloc_wrappers.pbi : index mbed_alloc_wrappers.xcl
build mbed_application.pbi : index mbed_application.xcl
build mbed_assert.pbi : index mbed_assert.xcl
build mbed_atomic_impl.pbi : index mbed_atomic_impl.xcl
build mbed_board.pbi : index mbed_board.xcl
build mbed_critical.pbi : index mbed_critical.xcl
build mbed_error.pbi : index mbed_error.xcl
build mbed_error_hist.pbi : index mbed_error_hist.xcl
build mbed_interface.pbi : index mbed_interface.xcl
build mbed_mem_trace.pbi : index mbed_mem_trace.xcl
build mbed_mktime.pbi : index mbed_mktime.xcl
build mbed_mpu_mgmt.pbi : index mbed_mpu_mgmt.xcl
build mbed_os_timer.pbi : index mbed_os_timer.xcl
build mbed_poll.pbi : index mbed_poll.xcl
build mbed_power_mgmt.pbi : index mbed_power_mgmt.xcl
build mbed_retarget.pbi : index mbed_retarget.xcl
build mbed_rtc_time.pbi : index mbed_rtc_time.xcl
build mbed_sdk_boot.pbi : index mbed_sdk_boot.xcl
build mbed_semihost_api.pbi : index mbed_semihost_api.xcl
build mbed_stats.pbi : index mbed_stats.xcl
build mbed_thread.pbi : index mbed_thread.xcl
build mbed_wait_api_no_rtos.pbi : index mbed_wait_api_no_rtos.xcl
build newlib_nano_malloc_workaround.pbi : index newlib_nano_malloc_workaround.xcl
build Stream.pbi : index Stream.xcl
build SysTimer.pbi : index SysTimer.xcl
build FlashIAPBlockDevice.pbi : index FlashIAPBlockDevice.xcl
build BufferedBlockDevice.pbi : index BufferedBlockDevice.xcl
build ChainingBlockDevice.pbi : index ChainingBlockDevice.xcl
build ExhaustibleBlockDevice.pbi : index ExhaustibleBlockDevice.xcl
build FlashSimBlockDevice.pbi : index FlashSimBlockDevice.xcl
build HeapBlockDevice.pbi : index HeapBlockDevice.xcl
build MBRBlockDevice.pbi : index MBRBlockDevice.xcl
build ObservingBlockDevice.pbi : index ObservingBlockDevice.xcl
build ProfilingBlockDevice.pbi : index ProfilingBlockDevice.xcl
build ReadOnlyBlockDevice.pbi : index ReadOnlyBlockDevice.xcl
build SFDP.pbi : index SFDP.xcl
build SlicingBlockDevice.pbi : index SlicingBlockDevice.xcl
build analogin_api.pbi : index analogin_api.xcl
build analogout_api.pbi : index analogout_api.xcl
build can_api.pbi : index can_api.xcl
build flash_api.pbi : index flash_api.xcl
build gpio_api.pbi : index gpio_api.xcl
build gpio_irq_api.pbi : index gpio_irq_api.xcl
build i2c_api.pbi : index i2c_api.xcl
build input_capture_api.pbi : index input_capture_api.xcl
build mbed_overrides.pbi : index mbed_overrides.xcl
build pinmap.pbi : index pinmap.xcl
build port_api.pbi : index port_api.xcl
build rtc_api.pbi : index rtc_api.xcl
build serial_api.pbi : index serial_api.xcl
build sleep.pbi : index sleep.xcl
build spi_api.pbi : index spi_api.xcl
build trng_api.pbi : index trng_api.xcl
build us_ticker.pbi : index us_ticker.xcl
build watchdog_api.pbi : index watchdog_api.xcl
build system_gd32h7xx.pbi : index system_gd32h7xx.xcl
build gd32h7xx_adc.pbi : index gd32h7xx_adc.xcl
build gd32h7xx_can.pbi : index gd32h7xx_can.xcl
build gd32h7xx_cau.pbi : index gd32h7xx_cau.xcl
build gd32h7xx_cau_aes.pbi : index gd32h7xx_cau_aes.xcl
build gd32h7xx_cau_des.pbi : index gd32h7xx_cau_des.xcl
build gd32h7xx_cau_tdes.pbi : index gd32h7xx_cau_tdes.xcl
build gd32h7xx_cmp.pbi : index gd32h7xx_cmp.xcl
build gd32h7xx_cpdm.pbi : index gd32h7xx_cpdm.xcl
build gd32h7xx_crc.pbi : index gd32h7xx_crc.xcl
build gd32h7xx_ctc.pbi : index gd32h7xx_ctc.xcl
build gd32h7xx_dac.pbi : index gd32h7xx_dac.xcl
build gd32h7xx_dbg.pbi : index gd32h7xx_dbg.xcl
build gd32h7xx_dci.pbi : index gd32h7xx_dci.xcl
build gd32h7xx_dma.pbi : index gd32h7xx_dma.xcl
build gd32h7xx_edout.pbi : index gd32h7xx_edout.xcl
build gd32h7xx_efuse.pbi : index gd32h7xx_efuse.xcl
build gd32h7xx_enet.pbi : index gd32h7xx_enet.xcl
build gd32h7xx_exmc.pbi : index gd32h7xx_exmc.xcl
build gd32h7xx_exti.pbi : index gd32h7xx_exti.xcl
build gd32h7xx_fac.pbi : index gd32h7xx_fac.xcl
build gd32h7xx_fmc.pbi : index gd32h7xx_fmc.xcl
build gd32h7xx_fwdgt.pbi : index gd32h7xx_fwdgt.xcl
build gd32h7xx_gpio.pbi : index gd32h7xx_gpio.xcl
build gd32h7xx_hau.pbi : index gd32h7xx_hau.xcl
build gd32h7xx_hau_sha_md5.pbi : index gd32h7xx_hau_sha_md5.xcl
build gd32h7xx_hpdf.pbi : index gd32h7xx_hpdf.xcl
build gd32h7xx_hwsem.pbi : index gd32h7xx_hwsem.xcl
build gd32h7xx_i2c.pbi : index gd32h7xx_i2c.xcl
build gd32h7xx_ipa.pbi : index gd32h7xx_ipa.xcl
build gd32h7xx_lpdts.pbi : index gd32h7xx_lpdts.xcl
build gd32h7xx_mdio.pbi : index gd32h7xx_mdio.xcl
build gd32h7xx_mdma.pbi : index gd32h7xx_mdma.xcl
build gd32h7xx_misc.pbi : index gd32h7xx_misc.xcl
build gd32h7xx_ospi.pbi : index gd32h7xx_ospi.xcl
build gd32h7xx_ospim.pbi : index gd32h7xx_ospim.xcl
build gd32h7xx_pmu.pbi : index gd32h7xx_pmu.xcl
build gd32h7xx_rameccmu.pbi : index gd32h7xx_rameccmu.xcl
build gd32h7xx_rcu.pbi : index gd32h7xx_rcu.xcl
build gd32h7xx_rspdif.pbi : index gd32h7xx_rspdif.xcl
build gd32h7xx_rtc.pbi : index gd32h7xx_rtc.xcl
build gd32h7xx_rtdec.pbi : index gd32h7xx_rtdec.xcl
build gd32h7xx_sai.pbi : index gd32h7xx_sai.xcl
build gd32h7xx_sdio.pbi : index gd32h7xx_sdio.xcl
build gd32h7xx_spi.pbi : index gd32h7xx_spi.xcl
build gd32h7xx_syscfg.pbi : index gd32h7xx_syscfg.xcl
build gd32h7xx_timer.pbi : index gd32h7xx_timer.xcl
build gd32h7xx_tli.pbi : index gd32h7xx_tli.xcl
build gd32h7xx_tmu.pbi : index gd32h7xx_tmu.xcl
build gd32h7xx_trigsel.pbi : index gd32h7xx_trigsel.xcl
build gd32h7xx_trng.pbi : index gd32h7xx_trng.xcl
build gd32h7xx_usart.pbi : index gd32h7xx_usart.xcl
build gd32h7xx_vref.pbi : index gd32h7xx_vref.xcl
build gd32h7xx_wwdgt.pbi : index gd32h7xx_wwdgt.xcl
build PeripheralPins.pbi : index PeripheralPins.xcl
build main.pbi : index main.xcl
build gd32h7_test_part0.pbi : link AnalogIn.pbi AnalogOut.pbi BufferedSerial.pbi CAN.pbi DigitalIn.pbi DigitalInOut.pbi DigitalOut.pbi FlashIAP.pbi I2C.pbi InterruptIn.pbi PwmOut.pbi SerialBase.pbi
build gd32h7_test_part1.pbi : link SPI.pbi TimerEvent.pbi LowPowerTickerWrapper.pbi mbed_compat.pbi mbed_critical_section_api.pbi mbed_flash_api.pbi mbed_gpio.pbi mbed_gpio_irq.pbi mbed_itm_api.pbi mbed_lp_ticker_api.pbi mbed_lp_ticker_wrapper.pbi mbed_pinmap_common.pbi
build gd32h7_test_part2.pbi : link mbed_pinmap_default.pbi mbed_ticker_api.pbi mbed_us_ticker_api.pbi static_pinmap.pbi ATCmdParser.pbi CriticalSectionLock.pbi CThunkBase.pbi DeepSleepLock.pbi FileBase.pbi FileHandle.pbi FilePath.pbi FileSystemHandle.pbi
build gd32h7_test_part3.pbi : link LocalFileSystem.pbi mbed_alloc_wrappers.pbi mbed_application.pbi mbed_assert.pbi mbed_atomic_impl.pbi mbed_board.pbi mbed_critical.pbi mbed_error.pbi mbed_error_hist.pbi mbed_interface.pbi mbed_mem_trace.pbi mbed_mktime.pbi
build gd32h7_test_part4.pbi : link mbed_mpu_mgmt.pbi mbed_os_timer.pbi mbed_poll.pbi mbed_power_mgmt.pbi mbed_retarget.pbi mbed_rtc_time.pbi mbed_sdk_boot.pbi mbed_semihost_api.pbi mbed_stats.pbi mbed_thread.pbi mbed_wait_api_no_rtos.pbi newlib_nano_malloc_workaround.pbi
build gd32h7_test_part5.pbi : link Stream.pbi SysTimer.pbi FlashIAPBlockDevice.pbi BufferedBlockDevice.pbi ChainingBlockDevice.pbi ExhaustibleBlockDevice.pbi FlashSimBlockDevice.pbi HeapBlockDevice.pbi MBRBlockDevice.pbi ObservingBlockDevice.pbi ProfilingBlockDevice.pbi ReadOnlyBlockDevice.pbi
build gd32h7_test_part6.pbi : link SFDP.pbi SlicingBlockDevice.pbi analogin_api.pbi analogout_api.pbi can_api.pbi flash_api.pbi gpio_api.pbi gpio_irq_api.pbi i2c_api.pbi input_capture_api.pbi mbed_overrides.pbi pinmap.pbi
build gd32h7_test_part7.pbi : link port_api.pbi rtc_api.pbi serial_api.pbi sleep.pbi spi_api.pbi trng_api.pbi us_ticker.pbi watchdog_api.pbi system_gd32h7xx.pbi gd32h7xx_adc.pbi gd32h7xx_can.pbi gd32h7xx_cau.pbi
build gd32h7_test_part8.pbi : link gd32h7xx_cau_aes.pbi gd32h7xx_cau_des.pbi gd32h7xx_cau_tdes.pbi gd32h7xx_cmp.pbi gd32h7xx_cpdm.pbi gd32h7xx_crc.pbi gd32h7xx_ctc.pbi gd32h7xx_dac.pbi gd32h7xx_dbg.pbi gd32h7xx_dci.pbi gd32h7xx_dma.pbi gd32h7xx_edout.pbi
build gd32h7_test_part9.pbi : link gd32h7xx_efuse.pbi gd32h7xx_enet.pbi gd32h7xx_exmc.pbi gd32h7xx_exti.pbi gd32h7xx_fac.pbi gd32h7xx_fmc.pbi gd32h7xx_fwdgt.pbi gd32h7xx_gpio.pbi gd32h7xx_hau.pbi gd32h7xx_hau_sha_md5.pbi gd32h7xx_hpdf.pbi gd32h7xx_hwsem.pbi
build gd32h7_test_part10.pbi : link gd32h7xx_i2c.pbi gd32h7xx_ipa.pbi gd32h7xx_lpdts.pbi gd32h7xx_mdio.pbi gd32h7xx_mdma.pbi gd32h7xx_misc.pbi gd32h7xx_ospi.pbi gd32h7xx_ospim.pbi gd32h7xx_pmu.pbi gd32h7xx_rameccmu.pbi gd32h7xx_rcu.pbi gd32h7xx_rspdif.pbi
build gd32h7_test_part11.pbi : link gd32h7xx_rtc.pbi gd32h7xx_rtdec.pbi gd32h7xx_sai.pbi gd32h7xx_sdio.pbi gd32h7xx_spi.pbi gd32h7xx_syscfg.pbi gd32h7xx_timer.pbi gd32h7xx_tli.pbi gd32h7xx_tmu.pbi gd32h7xx_trigsel.pbi gd32h7xx_trng.pbi gd32h7xx_usart.pbi
build gd32h7_test_part12.pbi : link gd32h7xx_vref.pbi gd32h7xx_wwdgt.pbi PeripheralPins.pbi main.pbi
build gd32h7_test.pbd : link gd32h7_test_part0.pbi gd32h7_test_part1.pbi gd32h7_test_part2.pbi gd32h7_test_part3.pbi gd32h7_test_part4.pbi gd32h7_test_part5.pbi gd32h7_test_part6.pbi gd32h7_test_part7.pbi gd32h7_test_part8.pbi gd32h7_test_part9.pbi gd32h7_test_part10.pbi gd32h7_test_part11.pbi gd32h7_test_part12.pbi
build gd32h7_test.pbw : browsedata gd32h7_test.pbd


