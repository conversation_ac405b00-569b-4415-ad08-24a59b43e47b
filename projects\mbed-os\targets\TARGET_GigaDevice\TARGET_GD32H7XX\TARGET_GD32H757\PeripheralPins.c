/* mbed Microcontroller Library
 * Copyright (c) 2018 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "PeripheralPins.h"

/* void pin_function(PinName pin, int function);
   configure the mode, output mode, pull, speed, af function of pins
   the parameter function contains the configuration information,show as below
   bit 0:2     gpio mode input / output / af / analog
   bit 3       output push-pull / open drain
   bit 5:4     no pull, pull-up, pull-down
   bit 9:6     channel af function
   bit 11:10   gpio speed
   bit 16:12   channel of adc/timer/dac
   bit 17      PWM channel-ON
   bit 31:18   reserved
*/

/* Additional macros for UART, SPI, and CAN pin functions */
#define SET_UART_PIN_FUNCTION(MODE, OTYPE, PUPD, AF, CHANNEL) \
    SET_PIN_FUNCTION(MODE, OTYPE, PUPD, AF)

#define SET_SPI_PIN_FUNCTION(MODE, OTYPE, PUPD, AF, CHANNEL) \
    SET_PIN_FUNCTION(MODE, OTYPE, PUPD, AF)

#define SET_CAN_PIN_FUNCTION(MODE, OTYPE, PUPD, AF, CHANNEL) \
    SET_PIN_FUNCTION(MODE, OTYPE, PUPD, AF)

/* GPIO MODE */
const int GD_GPIO_MODE[] = {
    GPIO_MODE_INPUT,        /* 0 */
    GPIO_MODE_OUTPUT,       /* 1 */
    GPIO_MODE_AF,           /* 2 */
    GPIO_MODE_ANALOG,       /* 3 */
};

/* GPIO pull_up_down */
const int GD_GPIO_PULL_UP_DOWN[] = {
    GPIO_PUPD_NONE,        /* 0 */
    GPIO_PUPD_PULLUP,      /* 1 */
    GPIO_PUPD_PULLDOWN,    /* 2 */
};

/* GPIO otype */
const int GD_GPIO_OUTPUT_MODE[] = {
    GPIO_OTYPE_PP,        /* 0 */
    GPIO_OTYPE_OD,        /* 1 */
};

/* GPIO SPEED */
const int GD_GPIO_SPEED[] = {
    GPIO_OSPEED_12MHZ,           /* 0 */
    GPIO_OSPEED_60MHZ,           /* 1 */
    GPIO_OSPEED_85MHZ,           /* 2 */
    GPIO_OSPEED_100_220MHZ,      /* 3 */
};

/* GPIO AF */
const int GD_GPIO_AF[] = {
    GPIO_AF_0,              /* 0 */
    GPIO_AF_1,              /* 1 */
    GPIO_AF_2,              /* 2 */
    GPIO_AF_3,              /* 3 */
    GPIO_AF_4,              /* 4 */
    GPIO_AF_5,              /* 5 */
    GPIO_AF_6,              /* 6 */
    GPIO_AF_7,              /* 7 */
    GPIO_AF_8,              /* 8 */
    GPIO_AF_9,              /* 9 */
    GPIO_AF_10,             /* 10 */
    GPIO_AF_11,             /* 11 */
    GPIO_AF_12,             /* 12 */
    GPIO_AF_13,             /* 13 */
    GPIO_AF_14,             /* 14 */
    GPIO_AF_15,             /* 15 */
};

/* ADC PinMap */
const PinMap PinMap_ADC[] = {
/* ADC0 */    
    {PORTF_11, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(2)},    /* ADC0_IN2 */   
    {PORTA_6, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(3)},    /* ADC0_IN3 */   
    {PORTC_4, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(4)},    /* ADC0_IN4 */    
    {PORTB_1, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(5)},    /* ADC0_IN5 */     
    {PORTF_12, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(6)},    /* ADC0_IN6 */      
    {PORTA_7, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(7)},    /* ADC0_IN7 */  
    {PORTC_5, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(8)},    /* ADC0_IN8 */                              
    {PORTB_0, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(9)},    /* ADC0_IN9 */          
    {PORTC_0, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(10)},    /* ADC0_IN10 */
    {PORTC_1, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(11)},    /* ADC0_IN11 */
    {PORTA_2, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(14)},    /* ADC0_IN14 */   
    {PORTA_3, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(15)},    /* ADC0_IN15 */       
    {PORTA_0, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(16)},    /* ADC0_IN16 */   
    {PORTA_1, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(17)},    /* ADC0_IN17 */ 
    {PORTA_4, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(18)},    /* ADC0_IN18 */
    {PORTA_5, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(19)},    /* ADC0_IN19 */                      
/* ADC1*/
    {PORTF_13, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(2)},    /*  ADC1_IN2 */  
    {PORTA_6_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(3)},    /*  ADC1_IN3 */  
    {PORTC_4_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(4)},    /*  ADC1_IN4 */   
    {PORTB_1_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(5)},    /*  ADC1_IN5 */                              
    {PORTF_14, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(6)},    /*  ADC1_IN6 */  
    {PORTA_7_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(7)},    /*  ADC1_IN7 */                                                         
    {PORTC_5_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(8)},    /*  ADC1_IN8 */                              
    {PORTB_0_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(9)},    /*  ADC1_IN9 */      
    {PORTC_0_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(10)},    /*  ADC1_IN10 */
    {PORTC_1_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(11)},    /*  ADC1_IN11 */
    {PORTA_2_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(14)},    /*  ADC1_IN14 */
    {PORTA_3_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(15)},    /*  ADC1_IN15 */
    {PORTA_4_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(18)},    /*  ADC1_IN18 */ 
    {PORTA_5_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(19)},    /*  ADC1_IN19 */ 
/* ADC2 */
    {PORTC_2, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(0)},    /*  ADC2_IN0 */    
    {PORTC_3, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(1)},    /*  ADC2_IN1 */ 
    {PORTF_9, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(2)},    /*  ADC2_IN2 */    
    {PORTF_7, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(3)},    /*  ADC2_IN3 */
    {PORTF_5, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(4)},    /*  ADC2_IN4 */
    {PORTF_3, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(5)},    /*  ADC2_IN5 */
    {PORTF_10, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(6)},    /*  ADC2_IN6 */
    {PORTF_8, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(7)},    /*  ADC2_IN7 */
    {PORTF_6, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(8)},    /*  ADC2_IN8 */
    {PORTF_4, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(9)},    /*  ADC2_IN9 */
    {PORTC_0_MUL1, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(10)},    /*  ADC2_IN10 */
    {PORTC_1_MUL1, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(11)},    /*  ADC2_IN11 */
                          
    {NC,   NC,    0}
};

/* DAC PinMap */
const PinMap PinMap_DAC[] = {
    {PORTA_4,       DAC_0, SET_PIN_FUNCTION_DAC_CHANNEL(0)},    /* DAC_OUT0 */
    {PORTA_5,       DAC_0, SET_PIN_FUNCTION_DAC_CHANNEL(1)},    /* DAC_OUT1 */
    {NC, NC, 0}
};


/* I2C PinMap */
const PinMap PinMap_I2C_SDA[] = {
    {PORTB_3,       I2C_1, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_7,       I2C_0, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_9,       I2C_0, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_11,      I2C_1, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTC_9,       I2C_2, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTF_0,       I2C_1, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTF_15,      I2C_3, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTH_5,       I2C_2, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {NC,    NC,    0}
};

const PinMap PinMap_I2C_SCL[] = {
    {PORTA_8,       I2C_2, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_6,       I2C_0, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_8,       I2C_0, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_10,      I2C_1, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTF_1,       I2C_1, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTF_14,      I2C_3, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTH_4,       I2C_2, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {NC,    NC,    0}
};

/* PWM PinMap */
const PinMap PinMap_PWM[] = {
    {PORTA_0,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 0)},
    {PORTA_1,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 0)},
    {PORTA_2,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 0)},
    {PORTA_3,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 3, 0)},
    {PORTA_5,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 0)},
    {PORTA_5_MUL0,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 1)},
    {PORTA_6,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0, 0)},
    {PORTA_7,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 1)},
    {PORTA_7_MUL0,  PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 1, 0)},
    {PORTA_7_MUL1,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 1)},
    {PORTA_8,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 0)},
    {PORTA_9,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 0)},
    {PORTA_10,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 0)},
    {PORTA_11,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 3, 0)},
    {PORTA_15,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 0)},
    {PORTB_0,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 1)},
    {PORTB_0_MUL0,  PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 2, 0)},
    {PORTB_0_MUL1,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 1, 1)},
    {PORTB_1,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 1)},
    {PORTB_1_MUL0,  PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 3, 0)},
    {PORTB_1_MUL1,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 2, 1)},
    {PORTB_3,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 0)},
    {PORTB_4,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0, 0)},
    {PORTB_5,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 1, 0)},
    {PORTB_6,       PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0, 0)},
    {PORTB_7,       PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 1, 0)},
    {PORTB_8,       PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 2, 0)},
    {PORTB_9,       PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 3, 0)},
    {PORTB_10,      PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 0)},
    {PORTB_11,      PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 3, 0)},
    {PORTB_13,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 1)},
    {PORTB_14,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 1)},
    {PORTB_14_MUL0, PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 1, 1)},
    {PORTB_15,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 1)},
    {PORTB_15_MUL0, PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 2, 1)},
    {PORTC_6,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0, 0)},
    {PORTC_6_MUL0,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 0)},
    {PORTC_7,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 1, 0)},
    {PORTC_7_MUL0,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 1, 0)},
    {PORTC_8,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 2, 0)},
    {PORTC_8_MUL0,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 2, 0)},
    {PORTC_9,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 3, 0)},
    {PORTC_9_MUL0,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 3, 0)},
    {PORTD_12,      PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0, 0)},
    {PORTD_13,      PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 1, 0)},
    {PORTD_14,      PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 2, 0)},
    {PORTD_15,      PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 3, 0)},
    {PORTE_8,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 1)},
    {PORTE_9,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 0)},
    {PORTE_10,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 1)},
    {PORTE_11,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 0)},
    {PORTE_12,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 1)},
    {PORTE_13,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 0)},
    {PORTE_14,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 3, 0)},
    {NC,    NC,    0}
};

/* UART PinMap */
const PinMap PinMap_UART_TX[] = {
    {PORTA_0,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_2,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_9,       UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_6,       UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_10,      UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTC_6,       UART_5,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTC_10,      UART_3,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTC_12,      UART_4,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTD_5,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTD_8,       UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTE_0,       UART_7,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTE_8,       UART_6,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTF_7,       UART_6,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTG_14,      UART_5,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_UART_RX[] = {
    {PORTA_1,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_3,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_10,      UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_7,       UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_11,      UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTC_7,       UART_5,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTC_11,      UART_3,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTD_2,       UART_4,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTD_6,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTD_9,       UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTE_1,       UART_7,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTE_9,       UART_6,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTF_6,       UART_6,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTG_9,       UART_5,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_UART_RTS[] = {
    {PORTA_1,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_12,      UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_14,      UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTC_8,       UART_5,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTD_4,       UART_3,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTD_12,      UART_4,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTE_2,       UART_7,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTE_10,      UART_6,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_UART_CTS[] = {
    {PORTA_0,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_11,      UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_13,      UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTC_9,       UART_5,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTD_3,       UART_3,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTD_11,      UART_4,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTE_3,       UART_7,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {PORTE_11,      UART_6,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_8, 0)},
    {NC,    NC,    0}
};

/* SPI PinMap */
const PinMap PinMap_SPI_MOSI[] = {
    {PORTA_7,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_7_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_2,       SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_5,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_5_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_15,      SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_15_MUL0, SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_1,       SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_3,       SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_12,      SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_6, 0)},
    {PORTD_6,       SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTD_7,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTE_6,       SPI_3,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTF_9,       SPI_4,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTG_3,       SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTG_14,      SPI_5,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_SPI_MISO[] = {
    {PORTA_6,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_6_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_4,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_4_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_14,      SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_14_MUL0, SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_2,       SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_11,      SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_6, 0)},
    {PORTE_5,       SPI_3,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTF_8,       SPI_4,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTG_2,       SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTG_12,      SPI_5,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_SPI_SCLK[] = {
    {PORTA_5,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_5_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_3,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_3_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_13,      SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_10,      SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_6, 0)},
    {PORTD_3,       SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTE_2,       SPI_3,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTF_7,       SPI_4,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTG_13,      SPI_5,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_SPI_SSEL[] = {
    {PORTA_4,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_4_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_15,      SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_15_MUL0, SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_9,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_9_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_9,       SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_6, 0)},
    {PORTD_0,       SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTE_4,       SPI_3,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTF_6,       SPI_4,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTG_8,       SPI_5,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {NC,    NC,    0}
};

/* CAN PinMap */
const PinMap PinMap_CAN_RD[] = {
    {PORTA_13,      CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTB_8,       CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)}, 
    {PORTD_0,       CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},    
    {PORTB_5,       CAN_1,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTB_12,      CAN_1,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},    
    {PORTD_12,      CAN_2,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)}, 
    {PORTF_6,       CAN_2,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0)}, 
    {PORTG_10,      CAN_2,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0)}, 
    {NC,    NC,    0}
};

const PinMap PinMap_CAN_TD[] = {
    {PORTA_14,      CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTB_9,       CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},    
    {PORTD_1,       CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},    
    {PORTB_6,       CAN_1,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTB_13,      CAN_1,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},    
    {PORTD_13,      CAN_2,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)}, 
    {PORTF_7,       CAN_2,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0)}, 
    {PORTG_9,       CAN_2,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0)}, 
    {NC,    NC,    0}
};
