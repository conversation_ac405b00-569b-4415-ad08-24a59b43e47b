<?xml version="1.0"?>
<settings>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <JLinkDriver>
        <jlinkResetStyle>12</jlinkResetStyle>
        <jlinkResetStrategy>0</jlinkResetStrategy>
        <CStepIntDis>_ 0</CStepIntDis>
        <LeaveTargetRunning>_ 0</LeaveTargetRunning>
    </JLinkDriver>
    <DebugChecksum>
        <Checksum>3091987489</Checksum>
    </DebugChecksum>
    <Exceptions>
        <StopOnUncaught>_ 0</StopOnUncaught>
        <StopOnThrow>_ 0</StopOnThrow>
    </Exceptions>
    <Disassembly>
        <InstrCount>0</InstrCount>
        <MixedMode>1</MixedMode>
    </Disassembly>
    <CodeCoverage>
        <Enabled>0</Enabled>
        <ShowSource>0</ShowSource>
        <HideCovered>0</HideCovered>
    </CodeCoverage>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <SWOTraceHWSettings>
        <OverrideDefaultClocks>0</OverrideDefaultClocks>
        <CpuClock>72000000</CpuClock>
        <ClockAutoDetect>1</ClockAutoDetect>
        <ClockWanted>11250000</ClockWanted>
        <JtagSpeed>5625000</JtagSpeed>
        <Prescaler>13</Prescaler>
        <TimeStampPrescIndex>0</TimeStampPrescIndex>
        <TimeStampPrescData>0</TimeStampPrescData>
        <PcSampCYCTAP>1</PcSampCYCTAP>
        <PcSampPOSTCNT>15</PcSampPOSTCNT>
        <PcSampIndex>0</PcSampIndex>
        <DataLogMode>0</DataLogMode>
        <ITMportsEnable>0</ITMportsEnable>
        <ITMportsTermIO>0</ITMportsTermIO>
        <ITMportsLogFile>0</ITMportsLogFile>
        <ITMlogFile>$PROJ_DIR$\ITM.log</ITMlogFile>
    </SWOTraceHWSettings>
    <PlDriver>
        <MemConfigValue />
    </PlDriver>
    <Jet>
        <JetConnSerialNo>CMSIS-DAP:</JetConnSerialNo>
        <JetConnFoundProbes />
        <DisableInterrupts>0</DisableInterrupts>
        <LeaveRunning>0</LeaveRunning>
        <MultiCoreRunAll>0</MultiCoreRunAll>
    </Jet>
    <ArmDriver>
        <EnableCache>1</EnableCache>
    </ArmDriver>
    <PlCacheRanges>
        <CustomRanges0>0 0 536870912 1 0</CustomRanges0>
        <CustomRangesText0>Code</CustomRangesText0>
        <CustomRanges1>0 134217728 524288 1 2048</CustomRanges1>
        <CustomRangesText1 />
        <CustomRanges2>0 536870912 33554432 0 0</CustomRanges2>
        <CustomRangesText2>SRAM</CustomRangesText2>
        <CustomRanges3>0 1073741824 33554432 2 0</CustomRanges3>
        <CustomRangesText3>Peripheral</CustomRangesText3>
        <CustomRanges4>0 3758096384 536870912 2 0</CustomRanges4>
        <CustomRangesText4>Private peripheral</CustomRangesText4>
    </PlCacheRanges>
    <Trace2>
        <Enabled>0</Enabled>
        <ShowSource>0</ShowSource>
    </Trace2>
    <SWOTraceWindow>
        <PcSampling>0</PcSampling>
        <InterruptLogs>0</InterruptLogs>
        <ForcedTimeStamps>0</ForcedTimeStamps>
        <EventCPI>0</EventCPI>
        <EventEXC>0</EventEXC>
        <EventFOLD>0</EventFOLD>
        <EventLSU>0</EventLSU>
        <EventSLEEP>0</EventSLEEP>
    </SWOTraceWindow>
    <PowerLog>
        <Title_0>I0</Title_0>
        <Symbol_0>0 4 0</Symbol_0>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <LiveEnabled>0</LiveEnabled>
        <LiveFile>PowerLogLive.log</LiveFile>
    </PowerLog>
    <DataLog>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
    </DataLog>
    <InterruptLog>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </InterruptLog>
    <EventLog>
        <Title_0>Ch3</Title_0>
        <Symbol_0>0 0 1</Symbol_0>
        <Title_1>Ch2</Title_1>
        <Symbol_1>0 0 1</Symbol_1>
        <Title_2>Ch1</Title_2>
        <Symbol_2>0 0 1</Symbol_2>
        <Title_3>Ch0</Title_3>
        <Symbol_3>0 0 1</Symbol_3>
        <LogEnabled>0</LogEnabled>
        <GraphEnabled>0</GraphEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </EventLog>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <LogFile>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
        <Category>_ 0</Category>
    </LogFile>
    <DriverProfiling>
        <Enabled>0</Enabled>
        <Mode>3</Mode>
        <Graph>0</Graph>
        <Symbiont>0</Symbiont>
        <Exclusions />
    </DriverProfiling>
    <CallStackLog>
        <Enabled>0</Enabled>
    </CallStackLog>
    <CallStackStripe>
        <ShowTiming>1</ShowTiming>
    </CallStackStripe>
    <PowerProbe>
        <Frequency>10000</Frequency>
        <Probe0>I0</Probe0>
        <ProbeSetup0>2 1 1 2 0 0</ProbeSetup0>
    </PowerProbe>
    <DisassembleMode>
        <mode>0</mode>
    </DisassembleMode>
    <Breakpoints2>
        <Bp0>_ 0 "EMUL_CODE" "{$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\DigitalIn.h}.70.9" 0 0 1 "" 0 "" 0</Bp0>
        <Bp1>_ 0 "EMUL_DATA_LOG" "GD_PIN_GET" 0 0</Bp1>
        <Bp2>_ 0 "EMUL_DATA" "GD_GPIO_MODE" 0 0 0 0 4294967295</Bp2>
        <Bp3>_ 1 "EMUL_CODE" "{$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_adc.c}.335.5" 0 0 1 "" 0 "" 0</Bp3>
        <Bp4>_ 1 "EMUL_CODE" "{$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\gpio_irq_api.c}.138.5" 0 0 1 "" 0 "" 0</Bp4>
        <Bp5>_ 1 "EMUL_CODE" "{$PROJ_DIR$\..\..\..\mbed-os\drivers\source\InterruptIn.cpp}.44.5" 0 0 1 "" 0 "" 0</Bp5>
        <Bp6>_ 1 "EMUL_CODE" "{G:\work\GD32\mbed\projects\source\main.cpp}.95.5" 0 0 1 "" 0 "" 0</Bp6>
        <Bp7>_ 1 "EMUL_CODE" "{G:\work\GD32\mbed\projects\source\main.cpp}.55.19" 0 0 1 "" 0 "" 0</Bp7>
        <Count>8</Count>
    </Breakpoints2>
    <Aliases>
        <Count>0</Count>
        <SuppressDialog>0</SuppressDialog>
    </Aliases>
</settings>
