














 

























































































































































































































































 
















 


 
 

  #pragma system_include

 
 

 

  #pragma system_include














 


 
 


  #pragma system_include

 



 

 

 

 
#pragma rtmodel = "__dlib_version", "6"

 


 



























 


  #pragma system_include

 
 
 


  #pragma system_include

 

 

 

 

   

 
 


   #pragma system_include






 




 


 


 


 

 


 

 

 

 

 

 

 

 

 















 



















 











 























 





 



 










 














 













 








 













 













 















 











 








 








 






 





 












 





 













 






 


   


  







 







 




 






 




 




 













 

   




 







 







 







 










 





 

















 


 


 













 

   


 


 



 

 

 
  typedef unsigned int _Wchart;
  typedef unsigned int _Wintt;

 

 
typedef unsigned int     _Sizet;

 
typedef signed char   __int8_t;
typedef unsigned char  __uint8_t;
typedef signed short int   __int16_t;
typedef unsigned short int  __uint16_t;
typedef signed int   __int32_t;
typedef unsigned int  __uint32_t;
   typedef signed long long int   __int64_t;
   typedef unsigned long long int  __uint64_t;
typedef signed int   __intptr_t;
typedef unsigned int  __uintptr_t;

 
typedef struct _Mbstatet
{  
    unsigned int _Wchar;   
    unsigned int _State;   

} _Mbstatet;

 

 
  typedef struct __va_list __Va_list;


    typedef struct __FILE _Filet;

 
typedef struct
{
    long long _Off;     
  _Mbstatet _Wstate;
} _Fpost;


 

 
  
   
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_StaticGuard(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_StaticGuard(void);

      _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockfilelock(_Filet *);
      _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockfilelock(_Filet *);

  typedef void *__iar_Rmtx;

  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Initdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Dstdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockdynamiclock(__iar_Rmtx *);

  






 
 
 


  #pragma system_include

 
 

 

  #pragma system_include














 



 
  typedef _Sizet size_t;

typedef unsigned int __data_size_t;



 

 

 
 
 

  #pragma system_include

 
 

 

  #pragma system_include














 




    typedef unsigned int   __time32_t;
    typedef unsigned int   clock_t;

typedef __time32_t         time_t;


struct tm
{        
  int tm_sec;
  int tm_min;
  int tm_hour;
  int tm_mday;
  int tm_mon;
  int tm_year;
  int tm_wday;
  int tm_yday;
  int tm_isdst;
    int __BSD_bug_filler1;
    int __BSD_bug_filler2;
};

struct __timespec32
{
  __time32_t tv_sec;
  long tv_nsec;
};

struct timespec
{
  time_t tv_sec;
  long tv_nsec;
};




   
  __intrinsic __nounwind time_t time(time_t *);

   
   __intrinsic __nounwind char *          asctime(const struct tm *);
  __intrinsic __nounwind   clock_t         clock(void);
   __intrinsic __nounwind char *          ctime(const time_t *);
  _Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double   difftime(time_t, time_t);
   __intrinsic __nounwind struct tm *     gmtime(const time_t *);
   __intrinsic __nounwind struct tm *     localtime(const time_t *);
  __intrinsic __nounwind   time_t          mktime(struct tm *);
  __intrinsic __nounwind   int             timespec_get(struct timespec *, int);

    __intrinsic __nounwind __time32_t      __time32(__time32_t *);
    __intrinsic __nounwind char *          __ctime32(const __time32_t *);
    _Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __difftime32(__time32_t, __time32_t);
    __intrinsic __nounwind struct tm *     __gmtime32(const __time32_t *);
    __intrinsic __nounwind struct tm *     __localtime32(const __time32_t *);
    __intrinsic __nounwind __time32_t      __mktime32(struct tm *);
    __intrinsic __nounwind int             __timespec_get32(struct __timespec32 *, int);
  __intrinsic __nounwind size_t              strftime(char *restrict, size_t,
                                            const char *restrict,
                                            const struct tm *restrict);








 





 
 
 


  #pragma system_include








 
 
 

  #pragma system_include

 
 

 

  #pragma system_include














 




 
  typedef signed char          int8_t;
  typedef unsigned char        uint8_t;

  typedef signed short int         int16_t;
  typedef unsigned short int       uint16_t;

  typedef signed int         int32_t;
  typedef unsigned int       uint32_t;

  typedef signed long long int         int64_t;
  typedef unsigned long long int       uint64_t;


 
typedef signed char      int_least8_t;
typedef unsigned char    uint_least8_t;

typedef signed short int     int_least16_t;
typedef unsigned short int   uint_least16_t;

typedef signed int     int_least32_t;
typedef unsigned int   uint_least32_t;

 
  typedef signed long long int   int_least64_t;
  typedef unsigned long long int uint_least64_t;



 
typedef signed int       int_fast8_t;
typedef unsigned int     uint_fast8_t;

typedef signed int      int_fast16_t;
typedef unsigned int    uint_fast16_t;

typedef signed int      int_fast32_t;
typedef unsigned int    uint_fast32_t;

  typedef signed long long int    int_fast64_t;
  typedef unsigned long long int  uint_fast64_t;

 
typedef signed long long int          intmax_t;
typedef unsigned long long int        uintmax_t;


 
typedef signed int          intptr_t;
typedef unsigned int        uintptr_t;

 
typedef int __data_intptr_t; typedef unsigned int __data_uintptr_t;

 






















 











 


 
 




 









 
typedef enum {
    RTC_FULL_LEAP_YEAR_SUPPORT,
    RTC_4_YEAR_LEAP_YEAR_SUPPORT
} rtc_leap_year_support_t;













 
_Bool _rtc_is_leap_year(int year, rtc_leap_year_support_t leap_year_support);




























 
_Bool _rtc_maketime(const struct tm *time, time_t *seconds, rtc_leap_year_support_t leap_year_support);


























 
_Bool _rtc_localtime(time_t timestamp, struct tm *time_info, rtc_leap_year_support_t leap_year_support);

 



 

 

 







 
static const uint32_t seconds_before_month[2][12] = {
    {
        0,
        31 * ((60 * 60) * 24),
        (31 + 28) *((60 * 60) * 24),
        (31 + 28 + 31) *((60 * 60) * 24),
        (31 + 28 + 31 + 30) *((60 * 60) * 24),
        (31 + 28 + 31 + 30 + 31) *((60 * 60) * 24),
        (31 + 28 + 31 + 30 + 31 + 30) *((60 * 60) * 24),
        (31 + 28 + 31 + 30 + 31 + 30 + 31) *((60 * 60) * 24),
        (31 + 28 + 31 + 30 + 31 + 30 + 31 + 31) *((60 * 60) * 24),
        (31 + 28 + 31 + 30 + 31 + 30 + 31 + 31 + 30) *((60 * 60) * 24),
        (31 + 28 + 31 + 30 + 31 + 30 + 31 + 31 + 30 + 31) *((60 * 60) * 24),
        (31 + 28 + 31 + 30 + 31 + 30 + 31 + 31 + 30 + 31 + 30) *((60 * 60) * 24),
    },
    {
        0,
        31 * ((60 * 60) * 24),
        (31 + 29) *((60 * 60) * 24),
        (31 + 29 + 31) *((60 * 60) * 24),
        (31 + 29 + 31 + 30) *((60 * 60) * 24),
        (31 + 29 + 31 + 30 + 31) *((60 * 60) * 24),
        (31 + 29 + 31 + 30 + 31 + 30) *((60 * 60) * 24),
        (31 + 29 + 31 + 30 + 31 + 30 + 31) *((60 * 60) * 24),
        (31 + 29 + 31 + 30 + 31 + 30 + 31 + 31) *((60 * 60) * 24),
        (31 + 29 + 31 + 30 + 31 + 30 + 31 + 31 + 30) *((60 * 60) * 24),
        (31 + 29 + 31 + 30 + 31 + 30 + 31 + 31 + 30 + 31) *((60 * 60) * 24),
        (31 + 29 + 31 + 30 + 31 + 30 + 31 + 31 + 30 + 31 + 30) *((60 * 60) * 24),
    }
};

_Bool _rtc_is_leap_year(int year, rtc_leap_year_support_t leap_year_support)
{
    














 
    if (leap_year_support == RTC_FULL_LEAP_YEAR_SUPPORT && year == 200) {
        return 0; 
    }

    return (year) % 4 ? 0 : 1;
}

_Bool _rtc_maketime(const struct tm *time, time_t *seconds, rtc_leap_year_support_t leap_year_support)
{
    if (seconds == 0 || time == 0) {
        return 0;
    }

    

 
    if ((time->tm_year < 70) || (time->tm_year > 206)) {
        return 0;
    }

    uint32_t result = time->tm_sec;
    result += time->tm_min * 60;
    result += time->tm_hour * (60 * 60);
    result += (time->tm_mday - 1) * ((60 * 60) * 24);
    result += seconds_before_month[_rtc_is_leap_year(time->tm_year, leap_year_support)][time->tm_mon];

     
    if (time->tm_year == 206) {
        if ((leap_year_support == RTC_FULL_LEAP_YEAR_SUPPORT && result > 3220095) ||
                (leap_year_support == RTC_4_YEAR_LEAP_YEAR_SUPPORT && result > 3133695)) {
            return 0;
        }
    }

    if (time->tm_year > 70) {
         
        uint32_t count_of_leap_days = ((time->tm_year - 1) / 4) - (70 / 4);
        if (leap_year_support == RTC_FULL_LEAP_YEAR_SUPPORT) {
            if (time->tm_year > 200) {
                count_of_leap_days--; 
            }
        }

        result += (((time->tm_year - 70) * 365) + count_of_leap_days) * ((60 * 60) * 24);
    }

    *seconds = result;

    return 1;
}

_Bool _rtc_localtime(time_t timestamp, struct tm *time_info, rtc_leap_year_support_t leap_year_support)
{
    if (time_info == 0) {
        return 0;
    }

    uint32_t seconds = (uint32_t)timestamp;

    time_info->tm_sec = seconds % 60;
    seconds = seconds / 60;   
    time_info->tm_min = seconds % 60;
    seconds = seconds / 60;  
    time_info->tm_hour = seconds % 24;
    seconds = seconds / 24;  

    

 
    time_info->tm_wday = (seconds + 4) % 7;

     
    time_info->tm_year = 70;
    while (1) {
        if (_rtc_is_leap_year(time_info->tm_year, leap_year_support) && seconds >= 366) {
            ++time_info->tm_year;
            seconds -= 366;
        } else if (!_rtc_is_leap_year(time_info->tm_year, leap_year_support) && seconds >= 365) {
            ++time_info->tm_year;
            seconds -= 365;
        } else {
             
            break;
        }
    }

    time_info->tm_yday = seconds;

     
    seconds *= ((60 * 60) * 24);
    time_info->tm_mon = 11;
    _Bool leap = _rtc_is_leap_year(time_info->tm_year, leap_year_support);
    for (uint32_t i = 0; i < 12; ++i) {
        if ((uint32_t) seconds < seconds_before_month[leap][i]) {
            time_info->tm_mon = i - 1;
            break;
        }
    }

    

 
    seconds -= seconds_before_month[leap][time_info->tm_mon];
    time_info->tm_mday = (seconds / ((60 * 60) * 24)) + 1;

    return 1;
}
