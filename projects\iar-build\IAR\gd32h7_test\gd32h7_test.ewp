<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>debug</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>31</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>ExePath</name>
                    <state>$PROJ_DIR$/debug</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>$PROJ_DIR$/debug/obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>$PROJ_DIR$/debug/list</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>Use the full configuration of the C/C++ runtime library. Full locale interface, C locale, file descriptor support, multibytes in printf and scanf, and hex floats in strtod.</state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>6.50.6.4952</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state>8.50.4.26131</state>
                </option>
                <option>
                    <name>GeneralEnableMisra</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVerbose</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>GD32H757xM	GD GD32H757xM</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>GeneralMisraVer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules04</name>
                    <version>0</version>
                    <state>011111110111111110111111110011011101111010111010111110001010101111110111111111010101111111101111111111110111110011111111011111111110111101111</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Full.h</state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>28</version>
                    <state>41</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>28</version>
                    <state>41</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>GD32H757xM	GD GD32H757xM</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>7</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>28</version>
                    <state>41</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>DSPExtension</name>
                    <state>1</state>
                </option>
                <option>
                    <name>TrustZone</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZoneModes</name>
                    <version>0</version>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>36</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CCDefines</name>
                    <state>DEBUG</state>
                    <state>GD32H7XX</state>
                    <state>TARGET_CORTEX_M</state>
                    <state>USE_STDPERIPH_DRIVER</state>
                    <state>DEVICE_ANALOGIN</state>
                    <state>DEVICE_ANALOGOUT</state>
                    <state>DEVICE_CAN</state>
                    <state>DEVICE_INTERRUPTIN</state>
                    <state>DEVICE_SPI</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state>Pa082,Pa050,Pe111,Pa089</state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>00000000</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state>$PROJ_DIR$\..\..\..\source\mbed_config.h</state>
                </option>
                <option>
                    <name>CompilerMisraOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$\..\..\..\source</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice</state>
                    <state>G:\work\GD32\mbed\projects\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX</state>
                    <state>G:\work\GD32\mbed\projects\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\TARGET_GD32H757</state>
                    <state>G:\work\GD32\mbed\projects\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\device</state>
                    <state>G:\work\GD32\mbed\projects\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\hal</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\hal\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\drivers</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\drivers\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\cxxsupport</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\internal</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\mbed-trace</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\mbed-trace\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\mbed-trace\include\mbed-trace</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\randlib</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\randlib\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\randlib\include\mbed-client-randlib</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\randlib\include\mbed-client-randlib\platform</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\source</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\source\minimal-printf</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os</state>
                    <state>$PROJ_DIR$\..\..\..</state>
                    <state>$PROJ_DIR$\..</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\internal</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\COMPONENT_FLASHIAP\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\COMPONENT_FLASHIAP\source</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\COMPONENT_FLASHIAP\include\FlashIAP</state>
                    <state>$PROJ_DIR$\..\..\..\git_version\</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CompilerMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>CompilerMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>2</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OICompilerExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCStackProtection</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>10</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state></state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>3</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>main_board_debug.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
            </data>
        </settings>
        <settings>
            <name>BICOMP</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>23</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XLinkMisraHandler</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>main_board.elf</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32F4XX\device\TOOLCHAIN_IAR\GD32F450xK-0x8000000-0x807FFFF.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>__iar_program_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>4</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0xFFFFFFFF</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkTrustzoneImportLibraryOut</name>
                    <state>main_board_import_lib.o</state>
                </option>
                <option>
                    <name>OILinkExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>$PROJ_DIR$\debug\main_board.a</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BILINK</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <configuration>
        <name>app</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>31</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>ExePath</name>
                    <state>app\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>app\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>app\List</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>No specifier n, no float nor long long, no scan set, no assignment suppressing, without multibyte support.</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Full formatting, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>Use the normal configuration of the C/C++ runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>6.50.6.4952</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state>8.50.4.26131</state>
                </option>
                <option>
                    <name>GeneralEnableMisra</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVerbose</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>GD32F450xK	GD GD32F450xK</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>GeneralMisraVer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules04</name>
                    <version>0</version>
                    <state>011111110111111110111111110011011101111010111010111110001010101111110111111111010101111111101111111111110111110011111111011111111110111101111</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>28</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>28</version>
                    <state>39</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>GD32F450xK	GD GD32F450xK</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>4</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>28</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>3</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>DSPExtension</name>
                    <state>1</state>
                </option>
                <option>
                    <name>TrustZone</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZoneModes</name>
                    <version>0</version>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>36</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CCDefines</name>
                    <state>USE_MAIN_BOARD</state>
                    <state>USE_GIT_INJECT</state>
                    <state>MBED_CONF_TARGET_DEFAULT_ADC_VREF</state>
                    <state>USE_STDPERIPH_DRIVER</state>
                    <state>GD32F450</state>
                    <state>MODEL_C</state>
                    <state>TARGET_CORTEX_M</state>
                    <state>__CORTEX_M4</state>
                    <state>DEVICE_PWMOUT</state>
                    <state>DEVICE_ANALOGIN</state>
                    <state>DEVICE_CAN</state>
                    <state>DEVICE_SPI</state>
                    <state>DEVICE_SPI_ASYNCH</state>
                    <state>DEVICE_I2C</state>
                    <state>DEVICE_USTICKER</state>
                    <state>DEVICE_INTERRUPTIN</state>
                    <state>DEVICE_RTC</state>
                    <state>DEVICE_FLASH</state>
                    <state>TARGET_M4</state>
                    <state>TARGET_CORTEX</state>
                    <state>TARGET_LIKE_CORTEX_M4</state>
                    <state>USE_TILT_MEASUREMENT</state>
                    <state>DEVICE_ENCODER_PLUSE</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state>Pa082,Pa050,Pe111,Pa089</state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>10010100</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state>$PROJ_DIR$\..\..\..\source\yr\apps\main_board\opt_main_board.h</state>
                </option>
                <option>
                    <name>CompilerMisraOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$\..\..\..\source</state>
                    <state>$PROJ_DIR$\..\..\..\source\TaskScheduler</state>
                    <state>$PROJ_DIR$\..\..\..\source\FastLED\src</state>
                    <state>$PROJ_DIR$\..\..\..\source\Embedded_RingBuf_CPP</state>
                    <state>$PROJ_DIR$\..\..\..\source\ros_lib</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\comm</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\drivers</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\utils</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\apps</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\apps\main_board</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\apps\main_board\rego_ank_1s</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\apps\main_board\rego_ank_1s\comm</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\apps\main_board\rego_ank_1s\modules</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\apps\main_board\shared</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\apps\leg_board</state>
                    <state>$PROJ_DIR$\..\..\..\source\ADC</state>
                    <state>$PROJ_DIR$\..\..\..\source\I2C</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\utils\proto</state>
                    <state>$PROJ_DIR$\..\..\..\middleware\asyncfatfs\lib</state>
                    <state>$PROJ_DIR$\..\..\..\middleware\fatfs\source</state>
                    <state>$PROJ_DIR$\..\..\..\middleware\fatfs\source\gd32_sd_disk</state>
                    <state>$PROJ_DIR$\..\..\..\middleware\sdmmc\inc</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\utils\segger_rtt</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\algs\control</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\algs\motion_tracking</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\algs\obd</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\algs\</state>
                    <state>$PROJ_DIR$\..\..\..\source\sw_version</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32F4XX</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32F4XX\device</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32F4XX\GD32F4xx_standard_peripheral\Include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32F4XX\TARGET_GD32F450ZI</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\hal</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\hal\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\drivers</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\drivers\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\cxxsupport</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\internal</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\mbed-trace</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\mbed-trace\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\mbed-trace\include\mbed-trace</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\randlib</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\randlib\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\randlib\include\mbed-client-randlib</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\randlib\include\mbed-client-randlib\platform</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\source</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\platform\source\minimal-printf</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os</state>
                    <state>$PROJ_DIR$\..\GD32F450</state>
                    <state>$PROJ_DIR$\..\..\..</state>
                    <state>$PROJ_DIR$\..</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\bootutil\include</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\bootutil\include\bootutil</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\bootutil\include\bootutil\crypto</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\bootutil\src</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\ext\tinycrypt\lib\include</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\ext\tinycrypt\lib\source</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\ext\tinycrypt-sha512\lib\include</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\ext\tinycrypt-sha512\lib\source</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\mbed</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\mbed\include</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\mbed\include\flash_map_backend</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\mbed\include\mcuboot_config</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\mbed\include\os</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\mbed\include\sysflash</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\mbed\include\utils</state>
                    <state>$PROJ_DIR$\..\..\..\mcuboot\mbed\src</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\internal</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\COMPONENT_FLASHIAP\include</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\COMPONENT_FLASHIAP\source</state>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\COMPONENT_FLASHIAP\include\FlashIAP</state>
                    <state>$PROJ_DIR$\..\..\..\source\FreeRTOS\Source</state>
                    <state>$PROJ_DIR$\..\..\..\source\FreeRTOS\Source\include</state>
                    <state>$PROJ_DIR$\..\..\..\source\FreeRTOS\Source\portable\IAR\ARM_CM4F</state>
                    <state>$PROJ_DIR$\..\..\..\source\FreeRTOS</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr_ftp\cpp</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr_ftp\cpp\src</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr_ftp\cpp\src\crc</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\comm\file_transfer</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\utils\testing</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\apps\main_board\shared</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\apps\leg_board</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\algs\</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\algs\control_kits</state>
                    <state>$PROJ_DIR$\..\..\..\source\yr\algs\versions_keep</state>
                    <state>$PROJ_DIR$\..\..\..\source/protobuf-ws/nanopb</state>
                    <state>$PROJ_DIR$\..\..\..\source/protobuf-ws/src</state>
                    <state>$PROJ_DIR$\..\..\..\source/protobuf-ws/smf/cpp</state>
                    <state>$PROJ_DIR$\..\..\..\source/protobuf-ws/gen/cpp</state>
                    <state>$PROJ_DIR$\..\..\..\source\protobuf-ws\smf</state>
                    <state>$PROJ_DIR$\..\..\..\source\protobuf-ws\smf\cpp</state>
                    <state>$PROJ_DIR$\..\..\..\source\protobuf-ws\smf\proto\nanopb</state>
                    <state>$PROJ_DIR$\..\..\..\source\protobuf-ws\smf\git_version</state>
                    <state>$PROJ_DIR$\..\..\..\git_version\</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>2</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>2</state>
                </option>
                <option>
                    <name>CompilerMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>CompilerMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>2</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OICompilerExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCStackProtection</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>10</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>3</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state>DEBUG</state>
                    <state>__STARTUP_INITIALIZE_NONCACHEDATA</state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32F4XX\device</state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>3</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>main_board.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
            </data>
        </settings>
        <settings>
            <name>BICOMP</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild>$PROJ_DIR$\..\scripts\global_pre_build_script.bat</prebuild>
                <postbuild>$PROJ_DIR$\run_imgtool_gd32f450.bat</postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>23</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XLinkMisraHandler</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>main_board.elf</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32F4XX\device\TOOLCHAIN_IAR\GD32F450xK-0x8020200-0x807FFFF.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>__iar_program_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>4</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0xFFFFFFFF</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkTrustzoneImportLibraryOut</name>
                    <state>main_board_import_lib.o</state>
                </option>
                <option>
                    <name>OILinkExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BILINK</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <group>
        <name>mbed-os</name>
        <group>
            <name>drivers</name>
            <group>
                <name>include</name>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\AnalogIn.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\AnalogOut.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\BufferedSerial.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\CAN.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\DigitalIn.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\DigitalInOut.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\DigitalOut.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\FlashIAP.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\I2C.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\interfaces\InterfaceCAN.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\interfaces\InterfaceDigitalIn.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\interfaces\InterfaceDigitalInOut.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\interfaces\InterfaceDigitalOut.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\InterruptIn.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\PwmOut.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\SerialBase.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\SPI.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\include\drivers\TimerEvent.h</name>
                </file>
            </group>
            <group>
                <name>source</name>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\AnalogIn.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\AnalogOut.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\BufferedSerial.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\CAN.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\DigitalIn.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\DigitalInOut.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\DigitalOut.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\FlashIAP.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\I2C.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\InterruptIn.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\PwmOut.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\SerialBase.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\SPI.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\drivers\source\TimerEvent.cpp</name>
                </file>
            </group>
        </group>
        <group>
            <name>hal</name>
            <group>
                <name>include </name>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\analogin_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\analogout_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\buffer.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\can_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\can_helper.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\crc_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\critical_section_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\dma_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\flash_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\gpio_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\gpio_irq_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\i2c_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\itm_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\LowPowerTickerWrapper.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\lp_ticker_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\mbed_lp_ticker_wrapper.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\mpu_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\ospi_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\pinmap.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\PinNameAliases.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\port_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\pwmout_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\qspi_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\reset_reason_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\rtc_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\serial_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\sleep_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\spi_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\static_pinmap.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\ticker_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\trng_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\us_ticker_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\include\hal\watchdog_api.h</name>
                </file>
            </group>
            <group>
                <name>source</name>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\LowPowerTickerWrapper.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_compat.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_critical_section_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_flash_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_gpio.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_gpio_irq.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_itm_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_lp_ticker_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_lp_ticker_wrapper.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_pinmap_common.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_pinmap_default.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_ticker_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\mbed_us_ticker_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\hal\source\static_pinmap.cpp</name>
                </file>
            </group>
        </group>
        <group>
            <name>platform</name>
            <group>
                <name>include</name>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\ATCmdParser.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\Callback.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\CircularBuffer.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\CriticalSectionLock.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\CThunk.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\internal\CThunkBase.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\DeepSleepLock.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\DirHandle.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\FileBase.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\FileHandle.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\FileLike.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\FilePath.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\FileSystemHandle.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\FileSystemLike.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\LocalFileSystem.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_application.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_assert.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_atomic.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\internal\mbed_atomic_impl.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_chrono.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_critical.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_debug.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_enum_flags.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_error.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\internal\mbed_error_hist.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\internal\mbed_fault_handler.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_interface.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_mem_trace.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_mktime.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_mpu_mgmt.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\internal\mbed_os_timer.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_poll.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_power_mgmt.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_preprocessor.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_retarget.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_rtc_time.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_semihost_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_stats.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_thread.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_toolchain.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_version.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\mbed_wait_api.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\NonCopyable.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\platform.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\PlatformMutex.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\ScopedLock.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\ScopedRamExecutionLock.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\ScopedRomWriteLock.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\SharedPtr.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\SingletonPtr.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\Span.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\Stream.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\internal\SysTimer.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\include\platform\Transaction.h</name>
                </file>
            </group>
            <group>
                <name>source</name>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\ATCmdParser.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\CriticalSectionLock.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\CThunkBase.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\DeepSleepLock.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\FileBase.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\FileHandle.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\FilePath.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\FileSystemHandle.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\LocalFileSystem.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_alloc_wrappers.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_application.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_assert.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_atomic_impl.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_board.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_crash_data_offsets.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_critical.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_error.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_error_hist.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_interface.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_mem_trace.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_mktime.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_mpu_mgmt.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_os_timer.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_poll.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_power_mgmt.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_retarget.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_rtc_time.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_sdk_boot.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_semihost_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_stats.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_thread.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\mbed_wait_api_no_rtos.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\newlib_nano_malloc_workaround.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\Stream.cpp</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\platform\source\SysTimer.cpp</name>
                </file>
            </group>
        </group>
        <group>
            <name>storage</name>
            <group>
                <name>blockdevice</name>
                <group>
                    <name>component_flashiap</name>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\COMPONENT_FLASHIAP\source\FlashIAPBlockDevice.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\COMPONENT_FLASHIAP\include\FlashIAP\FlashIAPBlockDevice.h</name>
                    </file>
                </group>
                <group>
                    <name>include</name>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\BlockDevice.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\BufferedBlockDevice.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\ChainingBlockDevice.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\ExhaustibleBlockDevice.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\FlashSimBlockDevice.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\HeapBlockDevice.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\MBRBlockDevice.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\ObservingBlockDevice.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\ProfilingBlockDevice.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\ReadOnlyBlockDevice.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\internal\SFDP.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\include\blockdevice\SlicingBlockDevice.h</name>
                    </file>
                </group>
                <group>
                    <name>source</name>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\BufferedBlockDevice.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\ChainingBlockDevice.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\ExhaustibleBlockDevice.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\FlashSimBlockDevice.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\HeapBlockDevice.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\MBRBlockDevice.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\ObservingBlockDevice.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\ProfilingBlockDevice.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\ReadOnlyBlockDevice.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\SFDP.cpp</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\storage\blockdevice\source\SlicingBlockDevice.cpp</name>
                    </file>
                </group>
            </group>
        </group>
        <group>
            <name>targets</name>
            <group>
                <name>api</name>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\analogin_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\analogout_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\can_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\device.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\flash_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\gpio_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\gpio_irq_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\i2c_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\input_capture_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\mbed_overrides.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\objects.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\PeripheralPins.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\pinmap.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\port_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\PortNames.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\rtc_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\serial_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\sleep.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\spi_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\trng_api.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\us_ticker.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\watchdog_api.c</name>
                </file>
            </group>
            <group>
                <name>device</name>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\device\IAR\GD32H757xM.icf</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\device\gd32h7xx.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\device\hal_tick.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\device\IAR\startup_gd32h7xx.s</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\device\system_gd32h7xx.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\device\system_gd32h7xx.h</name>
                </file>
            </group>
            <group>
                <name>library</name>
                <group>
                    <name>include</name>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_adc.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_axiim.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_can.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_cau.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_cmp.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_cpdm.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_crc.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_ctc.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_dac.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_dbg.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_dci.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_dma.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_edout.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_efuse.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_enet.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_exmc.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_exti.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_fac.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_fmc.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_fwdgt.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_gpio.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_hau.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_hpdf.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_hwsem.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_i2c.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_ipa.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_lpdts.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_mdio.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_mdma.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_misc.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_ospi.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_ospim.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_pmu.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_rameccmu.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_rcu.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_rspdif.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_rtc.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_rtdec.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_sai.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_sdio.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_spi.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_syscfg.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_timer.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_tli.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_tmu.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_trigsel.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_trng.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_usart.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_vref.h</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Include\gd32h7xx_wwdgt.h</name>
                    </file>
                </group>
                <group>
                    <name>source</name>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_adc.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_can.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_cau.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_cau_aes.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_cau_des.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_cau_tdes.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_cmp.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_cpdm.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_crc.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_ctc.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_dac.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_dbg.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_dci.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_dma.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_edout.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_efuse.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_enet.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_exmc.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_exti.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_fac.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_fmc.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_fwdgt.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_gpio.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_hau.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_hau_sha_md5.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_hpdf.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_hwsem.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_i2c.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_ipa.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_lpdts.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_mdio.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_mdma.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_misc.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_ospi.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_ospim.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_pmu.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_rameccmu.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_rcu.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_rspdif.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_rtc.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_rtdec.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_sai.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_sdio.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_spi.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_syscfg.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_timer.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_tli.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_tmu.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_trigsel.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_trng.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_usart.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_vref.c</name>
                    </file>
                    <file>
                        <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\GD32H7xx_standard_peripheral\Source\gd32h7xx_wwdgt.c</name>
                    </file>
                </group>
            </group>
            <group>
                <name>pin_map</name>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\TARGET_GD32H757\PeripheralNames.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\TARGET_GD32H757\PeripheralPins.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32H7XX\TARGET_GD32H757\PinNames.h</name>
                </file>
            </group>
        </group>
    </group>
    <group>
        <name>source</name>
        <group>
            <name>yr</name>
            <file>
                <name>$PROJ_DIR$\..\..\..\source\main.cpp</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\..\..\source\mbed_config.h</name>
            </file>
        </group>
    </group>
</project>
