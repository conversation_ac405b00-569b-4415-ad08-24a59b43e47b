/* mbed Microcontroller Library
 * Copyright (c) 2024 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include <stddef.h>
#include "cmsis.h"
#include "gpio_irq_api.h"
#include "pinmap.h"
#include "mbed_error.h"

#define EDGE_NONE (0)
#define EDGE_RISE (1)
#define EDGE_FALL (2)

extern uint32_t gpio_clock_enable(uint32_t port_idx);
static gpio_irq_handler irq_handler;
static const exti_line_enum EXIT_LINE[38] = {
  EXTI_0, EXTI_1, EXTI_2, EXTI_3, EXTI_4, EXTI_5, EXTI_6, EXTI_7, EXTI_8, EXTI_9,
  EXTI_10, EXTI_11, EXTI_12, EXTI_13, EXTI_14, EXTI_15, EXTI_16, EXTI_17, EXTI_18, EXTI_19,
  EXTI_20, EXTI_21, EXTI_22, EXTI_23, EXTI_24, EXTI_25, EXTI_26, EXTI_27, EXTI_28, EXTI_29,
  EXTI_30, EXTI_31, EXTI_32, EXTI_33, EXTI_34, EXTI_35, EXTI_36, EXTI_37  
};
gpio_irq_event irq_event[38] = {IRQ_NONE};


typedef struct {
    uintptr_t exti_contextx;
    uint32_t exti_gpiox; /* base address of gpio */
    uint32_t exti_pinx;  /* pin number */
} gpio_exti_info_struct;

/* EXTI0...EXTI15 */
static gpio_exti_info_struct exti_info_array[16] = {0};

/** handle EXTI interrupt in EXTI0 to EXTI15
 * @param irq_index     the line of EXTI(0~15)
 */
static void exti_handle_interrupt(uint32_t irq_index)
{
    gpio_exti_info_struct *gpio_exti = &exti_info_array[irq_index];
    exti_line_enum exti_line = EXIT_LINE[irq_index];
    if (RESET != exti_interrupt_flag_get(exti_line)) {
        if (irq_handler) {
//            if (RESET != exti_interrupt_flag_get(exti_line)) {
//                irq_handler(gpio_exti->exti_contextx, IRQ_RISE);
//            } else {
//                irq_handler(gpio_exti->exti_contextx, IRQ_FALL);
//            }
          irq_handler(gpio_exti->exti_contextx, irq_event[irq_index]);
        }
        exti_interrupt_flag_clear(exti_line);        
    }
}

void EXTI0_IRQHandler(void)
{
    exti_handle_interrupt(0);
}

void EXTI1_IRQHandler(void)
{
    exti_handle_interrupt(1);
}

void EXTI2_IRQHandler(void)
{
    exti_handle_interrupt(2);
}

void EXTI3_IRQHandler(void)
{
    exti_handle_interrupt(3);
}

void EXTI4_IRQHandler(void)
{
    exti_handle_interrupt(4);
}

void EXTI5_9_IRQHandler(void)
{
    if (RESET != exti_interrupt_flag_get(EXTI_5)) {
        exti_handle_interrupt(5);
    } else if (RESET != exti_interrupt_flag_get(EXTI_6)) {
        exti_handle_interrupt(6);
    } else if (RESET != exti_interrupt_flag_get(EXTI_7)) {
        exti_handle_interrupt(7);
    } else if (RESET != exti_interrupt_flag_get(EXTI_8)) {
        exti_handle_interrupt(8);
    } else if (RESET != exti_interrupt_flag_get(EXTI_9)) {
        exti_handle_interrupt(9);
    }
}

void EXTI10_15_IRQHandler(void)
{
    if (RESET != exti_interrupt_flag_get(EXTI_10)) {
        exti_handle_interrupt(10);
    } else if (RESET != exti_interrupt_flag_get(EXTI_11)) {
        exti_handle_interrupt(11);
    } else if (RESET != exti_interrupt_flag_get(EXTI_12)) {
        exti_handle_interrupt(12);
    } else if (RESET != exti_interrupt_flag_get(EXTI_13)) {
        exti_handle_interrupt(13);
    } else if (RESET != exti_interrupt_flag_get(EXTI_14)) {
        exti_handle_interrupt(14);
    } else if (RESET != exti_interrupt_flag_get(EXTI_15)) {
        exti_handle_interrupt(15);
    }
}

/** Initialize the GPIO IRQ pin
 *
 * @param obj     The GPIO object to initialize
 * @param pin     The GPIO pin name
 * @param handler The handler to be attached to GPIO IRQ
 * @param context The context to be passed back to the handler (context != 0, 0 is reserved)
 * @return -1 if pin is NC, 0 otherwise
 */
int gpio_irq_init(gpio_irq_t *obj, PinName pin, gpio_irq_handler handler, uintptr_t context)
{
    uint32_t vector = 0;
    gpio_exti_info_struct *gpio_exti;

    if (pin == NC) {
        return -1;
    }

    /* enable SYSCFG clock */
    rcu_periph_clock_enable(RCU_SYSCFG);

    uint32_t port_index = GD_PORT_GET(pin);
    uint32_t pin_index = GD_PIN_GET(pin);

    /* fill EXTI information according to pin_index */
    if (pin_index == 0) {
        vector = (uint32_t)&EXTI0_IRQHandler;
        obj->irq_index = 0;
        obj->irq_n = EXTI0_IRQn;
    } else if (pin_index == 1) {
        vector = (uint32_t)&EXTI1_IRQHandler;
        obj->irq_index = 1;
        obj->irq_n = EXTI1_IRQn;
    } else if (pin_index == 2) {
        vector = (uint32_t)&EXTI2_IRQHandler;
        obj->irq_index = 2;
        obj->irq_n = EXTI2_IRQn;
    } else if (pin_index == 3) {
        vector = (uint32_t)&EXTI3_IRQHandler;
        obj->irq_index = 3;
        obj->irq_n = EXTI3_IRQn;
    } else if (pin_index == 4) {
        vector = (uint32_t)&EXTI4_IRQHandler;
        obj->irq_index = 4;
        obj->irq_n = EXTI4_IRQn;
    } else if (pin_index == 5) {
        vector = (uint32_t)&EXTI5_9_IRQHandler;
        obj->irq_index = 5;
        obj->irq_n = EXTI5_9_IRQn;
    } else if (pin_index == 6) {
        vector = (uint32_t)&EXTI5_9_IRQHandler;
        obj->irq_index = 6;
        obj->irq_n = EXTI5_9_IRQn;
    } else if (pin_index == 7) {
        vector = (uint32_t)&EXTI5_9_IRQHandler;
        obj->irq_index = 7;
        obj->irq_n = EXTI5_9_IRQn;
    } else if (pin_index == 8) {
        vector = (uint32_t)&EXTI5_9_IRQHandler;
        obj->irq_index = 8;
        obj->irq_n = EXTI5_9_IRQn;
    } else if (pin_index == 9) {
        vector = (uint32_t)&EXTI5_9_IRQHandler;
        obj->irq_index = 9;
        obj->irq_n = EXTI5_9_IRQn;
    } else if (pin_index == 10) {
        vector = (uint32_t)&EXTI10_15_IRQHandler;
        obj->irq_index = 10;
        obj->irq_n = EXTI10_15_IRQn;
    } else if (pin_index == 11) {
        vector = (uint32_t)&EXTI10_15_IRQHandler;
        obj->irq_index = 11;
        obj->irq_n = EXTI10_15_IRQn;
    } else if (pin_index == 12) {
        vector = (uint32_t)&EXTI10_15_IRQHandler;
        obj->irq_index = 12;
        obj->irq_n = EXTI10_15_IRQn;
    } else if (pin_index == 13) {
        vector = (uint32_t)&EXTI10_15_IRQHandler;
        obj->irq_index = 13;
        obj->irq_n = EXTI10_15_IRQn;
    } else if (pin_index == 14) {
        vector = (uint32_t)&EXTI10_15_IRQHandler;
        obj->irq_index = 14;
        obj->irq_n = EXTI10_15_IRQn;
    } else if (pin_index == 15) {
        vector = (uint32_t)&EXTI10_15_IRQHandler;
        obj->irq_index = 15;
        obj->irq_n = EXTI10_15_IRQn;
    } else {
        error("pin not supported for interrupt in.\n");
        return -1;
    }

    /* enable GPIO clock */
    uint32_t gpio_add = gpio_clock_enable(port_index);

    /* save informations for future use */
    obj->event = EDGE_NONE;
    obj->pin = pin;

    gpio_exti = &exti_info_array[obj->irq_index];
    gpio_exti->exti_contextx = context;
    gpio_exti->exti_gpiox = gpio_add;
    gpio_exti->exti_pinx = pin_index;

    irq_handler = handler;

    /* enable EXTI interrupt */
    NVIC_SetVector(obj->irq_n, vector);
    gpio_irq_enable(obj);
//    gpio_irq_set(obj, IRQ_RISE, 1);
    return 0;
}

/** Release the GPIO IRQ PIN
 *
 * @param obj The gpio object
 */
void gpio_irq_free(gpio_irq_t *obj)
{
    gpio_exti_info_struct *gpio_exti = &exti_info_array[obj->irq_index];

    /* disable EXTI interrupt */
    gpio_irq_disable(obj);
    /* reset struct of EXTI information */
    gpio_exti->exti_contextx = 0;
    gpio_exti->exti_gpiox = 0;
    gpio_exti->exti_pinx = 0;
}

/** Enable/disable pin IRQ event
 *
 * @param obj    The GPIO object
 * @param event  The GPIO IRQ event
 * @param enable The enable flag
 */
void gpio_irq_set(gpio_irq_t *obj, gpio_irq_event event, uint32_t enable)
{
    /* enable / disable edge triggered interrupt and store event */
    obj->event = event;
    irq_event[obj->irq_index] = event;
    if (event == IRQ_RISE) {
        if (enable) {
            exti_init(EXIT_LINE[obj->irq_index], EXTI_INTERRUPT, EXTI_TRIG_RISING);
//            exti_init((exti_line_enum)(1 << GD_PIN_GET(obj->pin)), EXTI_INTERRUPT, EXTI_TRIG_BOTH);
//            /* clear interrupt enable bit, rising/falling bit */
//        } else {
//            EXTI_INTEN &= ~(uint32_t)(exti_line_enum)(1 << GD_PIN_GET(obj->pin));
//            EXTI_RTEN &= ~(uint32_t)(exti_line_enum)(1 << GD_PIN_GET(obj->pin));
//            EXTI_FTEN &= ~(uint32_t)(exti_line_enum)(1 << GD_PIN_GET(obj->pin));
        }
    }
    if (event == IRQ_FALL) {
        if (enable) {
            exti_init(EXIT_LINE[obj->irq_index], EXTI_INTERRUPT, EXTI_TRIG_FALLING);
          
//            exti_init((exti_line_enum)(1 << (GD_PIN_GET(obj->pin))), EXTI_INTERRUPT, EXTI_TRIG_BOTH);
//            /* clear interrupt enable bit, rising/falling bit */
//        } else {
//            EXTI_INTEN &= ~(uint32_t)(exti_line_enum)(1 << GD_PIN_GET(obj->pin));
//            EXTI_RTEN &= ~(uint32_t)(exti_line_enum)(1 << GD_PIN_GET(obj->pin));
//            EXTI_FTEN &= ~(uint32_t)(exti_line_enum)(1 << GD_PIN_GET(obj->pin));
        }
    }
}

/** Enable GPIO IRQ
 *
 * This is target dependent, as it might enable the entire port or just a pin
 * @param obj The GPIO object
 */
void gpio_irq_enable(gpio_irq_t *obj)
{
    /* select EXTI source  */
    syscfg_exti_line_config(GD_PORT_GET(obj->pin), GD_PIN_GET(obj->pin));
    exti_interrupt_enable((exti_line_enum)(1 << GD_PIN_GET(obj->pin)));
    NVIC_EnableIRQ(obj->irq_n);
}

/** Disable GPIO IRQ
 *
 * This is target dependent, as it might disable the entire port or just a pin
 * @param obj The GPIO object
 */
void gpio_irq_disable(gpio_irq_t *obj)
{
    /* clear EXTI line configuration */
    exti_interrupt_disable((exti_line_enum)(1 << GD_PIN_GET(obj->pin)));
    NVIC_DisableIRQ(obj->irq_n);
    NVIC_ClearPendingIRQ(obj->irq_n);
    obj->event = EDGE_NONE;
} 