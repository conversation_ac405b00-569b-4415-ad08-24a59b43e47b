#include "mbed_config.h"
#include "PinNames.h"
#include "DigitalIn.h"
#include "DigitalOut.h"
#include "AnalogIn.h"
#include "AnalogOut.h"
#include "CAN.h"
#include "us_ticker_api.h"
#include "InterruptIn.h"
#include "SPI.h"

mbed::DigitalIn key_1(PORTA_0, PullUp);

mbed::DigitalOut gpio_out_test(PORTA_5, PullUp);

mbed::DigitalOut led_3(PORTE_6, PullUp);
mbed::DigitalOut led_2(PORTE_5, PullUp);
mbed::DigitalOut led_1(PORTE_4, PullUp);

mbed::AnalogIn  analog_in(PORTA_6);
mbed::AnalogOut  analog_out(PORTA_4);

mbed::CAN can0(PORTB_5, PORTB_13, 1000000);

mbed::InterruptIn gpio_in_interrupt(PORTB_1, PullUp);

static void init();
static uint32_t millis();
static uint32_t micros();
static void sendCan(mbed::CANMessage msg);
static void canProcess();
static void gpioInterruptCallBack();

int main(void) {
  uint8_t run_count = 0;
  uint32_t dt = 0;
  uint32_t last_time = 0;
  float temp = 0;
  init();
  mbed::CANMessage msg;
  
  can0.attach(&canProcess);
  gpio_in_interrupt.rise(&gpioInterruptCallBack);
  while (1) {
    if (dt >= 1000) {
      run_count++;
      temp = 0.2;
      led_2 = key_1;  
      led_1 = run_count %2;
      analog_in.read();
      analog_out.write((float)run_count / 255.00);
      msg.id  = 1;
      msg.data[0] = run_count;
      msg.len = 1;
      sendCan(msg);
      last_time = millis();
      gpio_out_test = run_count % 2;
    }
    dt = millis() - last_time;
  }
}

void cache_enable(void)
{
    /* Enable I-Cache */
    SCB_EnableICache();

    /* Enable D-Cache */
    SCB_EnableDCache();
}

void init() {
   cache_enable();
   us_ticker_init();
}

uint32_t millis() {
  return (ms_ticker_read());
}

uint32_t micros() {
  return (us_ticker_read());
}

void sendCan(mbed::CANMessage msg) {
  can0.write(msg);
}

void canProcess() {
  mbed::CANMessage rx_msg;
  can0.read(rx_msg);
}

void gpioInterruptCallBack() {
    uint8_t test  = 0;
    test++;
}


