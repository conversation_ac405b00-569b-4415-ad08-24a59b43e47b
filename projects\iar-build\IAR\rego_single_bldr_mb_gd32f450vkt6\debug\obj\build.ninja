#Generating source browse information for project rego1s_bldr_mb


#Abbreviations
cc = D$:\Program$ Files$ (x86)\IAR$ Systems\Embedded$ Workbench$ 8.4\common\bin\SourceIndexer.exe
ll = D$:\Program$ Files$ (x86)\IAR$ Systems\Embedded$ Workbench$ 8.4\common\bin\PbdLink.exe
bd = D$:\Program$ Files$ (x86)\IAR$ Systems\Embedded$ Workbench$ 8.4\common\bin\makeBrowseData.exe



#Rules
rule index
  depfile = $out.dep
  command = $cc -out=$out -f $in
rule link
  command = $ll -M $out $in
rule browsedata
  command = $bd $in -output  $out



#Build steps
build AnalogIn.pbi : index AnalogIn.xcl
build BufferedSerial.pbi : index BufferedSerial.xcl
build CAN.pbi : index CAN.xcl
build DigitalIn.pbi : index DigitalIn.xcl
build DigitalInOut.pbi : index DigitalInOut.xcl
build DigitalOut.pbi : index DigitalOut.xcl
build FlashIAP.pbi : index FlashIAP.xcl
build I2C.pbi : index I2C.xcl
build InterruptIn.pbi : index InterruptIn.xcl
build PwmOut.pbi : index PwmOut.xcl
build SerialBase.pbi : index SerialBase.xcl
build SPI.pbi : index SPI.xcl
build Ticker.pbi : index Ticker.xcl
build TimerEvent.pbi : index TimerEvent.xcl
build LowPowerTickerWrapper.pbi : index LowPowerTickerWrapper.xcl
build mbed_compat.pbi : index mbed_compat.xcl
build mbed_critical_section_api.pbi : index mbed_critical_section_api.xcl
build mbed_flash_api.pbi : index mbed_flash_api.xcl
build mbed_gpio.pbi : index mbed_gpio.xcl
build mbed_gpio_irq.pbi : index mbed_gpio_irq.xcl
build mbed_itm_api.pbi : index mbed_itm_api.xcl
build mbed_lp_ticker_api.pbi : index mbed_lp_ticker_api.xcl
build mbed_lp_ticker_wrapper.pbi : index mbed_lp_ticker_wrapper.xcl
build mbed_pinmap_common.pbi : index mbed_pinmap_common.xcl
build mbed_pinmap_default.pbi : index mbed_pinmap_default.xcl
build mbed_ticker_api.pbi : index mbed_ticker_api.xcl
build mbed_us_ticker_api.pbi : index mbed_us_ticker_api.xcl
build static_pinmap.pbi : index static_pinmap.xcl
build ATCmdParser.pbi : index ATCmdParser.xcl
build CriticalSectionLock.pbi : index CriticalSectionLock.xcl
build CThunkBase.pbi : index CThunkBase.xcl
build DeepSleepLock.pbi : index DeepSleepLock.xcl
build FileBase.pbi : index FileBase.xcl
build FileHandle.pbi : index FileHandle.xcl
build FilePath.pbi : index FilePath.xcl
build FileSystemHandle.pbi : index FileSystemHandle.xcl
build LocalFileSystem.pbi : index LocalFileSystem.xcl
build mbed_alloc_wrappers.pbi : index mbed_alloc_wrappers.xcl
build mbed_application.pbi : index mbed_application.xcl
build mbed_assert.pbi : index mbed_assert.xcl
build mbed_atomic_impl.pbi : index mbed_atomic_impl.xcl
build mbed_board.pbi : index mbed_board.xcl
build mbed_critical.pbi : index mbed_critical.xcl
build mbed_error.pbi : index mbed_error.xcl
build mbed_error_hist.pbi : index mbed_error_hist.xcl
build mbed_interface.pbi : index mbed_interface.xcl
build mbed_mem_trace.pbi : index mbed_mem_trace.xcl
build mbed_mktime.pbi : index mbed_mktime.xcl
build mbed_mpu_mgmt.pbi : index mbed_mpu_mgmt.xcl
build mbed_os_timer.pbi : index mbed_os_timer.xcl
build mbed_poll.pbi : index mbed_poll.xcl
build mbed_power_mgmt.pbi : index mbed_power_mgmt.xcl
build mbed_retarget.pbi : index mbed_retarget.xcl
build mbed_rtc_time.pbi : index mbed_rtc_time.xcl
build mbed_sdk_boot.pbi : index mbed_sdk_boot.xcl
build mbed_semihost_api.pbi : index mbed_semihost_api.xcl
build mbed_stats.pbi : index mbed_stats.xcl
build mbed_thread.pbi : index mbed_thread.xcl
build mbed_wait_api_no_rtos.pbi : index mbed_wait_api_no_rtos.xcl
build newlib_nano_malloc_workaround.pbi : index newlib_nano_malloc_workaround.xcl
build Stream.pbi : index Stream.xcl
build SysTimer.pbi : index SysTimer.xcl
build FlashIAPBlockDevice.pbi : index FlashIAPBlockDevice.xcl
build BufferedBlockDevice.pbi : index BufferedBlockDevice.xcl
build ChainingBlockDevice.pbi : index ChainingBlockDevice.xcl
build ExhaustibleBlockDevice.pbi : index ExhaustibleBlockDevice.xcl
build FlashSimBlockDevice.pbi : index FlashSimBlockDevice.xcl
build HeapBlockDevice.pbi : index HeapBlockDevice.xcl
build MBRBlockDevice.pbi : index MBRBlockDevice.xcl
build ObservingBlockDevice.pbi : index ObservingBlockDevice.xcl
build ProfilingBlockDevice.pbi : index ProfilingBlockDevice.xcl
build ReadOnlyBlockDevice.pbi : index ReadOnlyBlockDevice.xcl
build SFDP.pbi : index SFDP.xcl
build SlicingBlockDevice.pbi : index SlicingBlockDevice.xcl
build analogin_api.pbi : index analogin_api.xcl
build analogout_api.pbi : index analogout_api.xcl
build can_api.pbi : index can_api.xcl
build flash_api.pbi : index flash_api.xcl
build gpio_api.pbi : index gpio_api.xcl
build gpio_irq_api.pbi : index gpio_irq_api.xcl
build i2c_api.pbi : index i2c_api.xcl
build mbed_overrides.pbi : index mbed_overrides.xcl
build pinmap.pbi : index pinmap.xcl
build port_api.pbi : index port_api.xcl
build pwmout_api.pbi : index pwmout_api.xcl
build rtc_api.pbi : index rtc_api.xcl
build serial_api.pbi : index serial_api.xcl
build sleep.pbi : index sleep.xcl
build spi_api.pbi : index spi_api.xcl
build trng_api.pbi : index trng_api.xcl
build us_ticker.pbi : index us_ticker.xcl
build system_gd32f4xx.pbi : index system_gd32f4xx.xcl
build gd32f4xx_adc.pbi : index gd32f4xx_adc.xcl
build gd32f4xx_can.pbi : index gd32f4xx_can.xcl
build gd32f4xx_crc.pbi : index gd32f4xx_crc.xcl
build gd32f4xx_ctc.pbi : index gd32f4xx_ctc.xcl
build gd32f4xx_dac.pbi : index gd32f4xx_dac.xcl
build gd32f4xx_dbg.pbi : index gd32f4xx_dbg.xcl
build gd32f4xx_dci.pbi : index gd32f4xx_dci.xcl
build gd32f4xx_dma.pbi : index gd32f4xx_dma.xcl
build gd32f4xx_enet.pbi : index gd32f4xx_enet.xcl
build gd32f4xx_exmc.pbi : index gd32f4xx_exmc.xcl
build gd32f4xx_exti.pbi : index gd32f4xx_exti.xcl
build gd32f4xx_fmc.pbi : index gd32f4xx_fmc.xcl
build gd32f4xx_fwdgt.pbi : index gd32f4xx_fwdgt.xcl
build gd32f4xx_gpio.pbi : index gd32f4xx_gpio.xcl
build gd32f4xx_i2c.pbi : index gd32f4xx_i2c.xcl
build gd32f4xx_ipa.pbi : index gd32f4xx_ipa.xcl
build gd32f4xx_iref.pbi : index gd32f4xx_iref.xcl
build gd32f4xx_misc.pbi : index gd32f4xx_misc.xcl
build gd32f4xx_pmu.pbi : index gd32f4xx_pmu.xcl
build gd32f4xx_rcu.pbi : index gd32f4xx_rcu.xcl
build gd32f4xx_rtc.pbi : index gd32f4xx_rtc.xcl
build gd32f4xx_sdio.pbi : index gd32f4xx_sdio.xcl
build gd32f4xx_spi.pbi : index gd32f4xx_spi.xcl
build gd32f4xx_syscfg.pbi : index gd32f4xx_syscfg.xcl
build gd32f4xx_timer.pbi : index gd32f4xx_timer.xcl
build gd32f4xx_tli.pbi : index gd32f4xx_tli.xcl
build gd32f4xx_trng.pbi : index gd32f4xx_trng.xcl
build gd32f4xx_usart.pbi : index gd32f4xx_usart.xcl
build gd32f4xx_wwdgt.pbi : index gd32f4xx_wwdgt.xcl
build PeripheralPins.pbi : index PeripheralPins.xcl
build boot_record.pbi : index boot_record.xcl
build bootutil_misc.pbi : index bootutil_misc.xcl
build bootutil_public.pbi : index bootutil_public.xcl
build caps.pbi : index caps.xcl
build encrypted.pbi : index encrypted.xcl
build fault_injection_hardening.pbi : index fault_injection_hardening.xcl
build fault_injection_hardening_delay_rng_mbedtls.pbi : index fault_injection_hardening_delay_rng_mbedtls.xcl
build image_ec.pbi : index image_ec.xcl
build image_ec256.pbi : index image_ec256.xcl
build image_ed25519.pbi : index image_ed25519.xcl
build image_rsa.pbi : index image_rsa.xcl
build image_validate.pbi : index image_validate.xcl
build loader.pbi : index loader.xcl
build swap_misc.pbi : index swap_misc.xcl
build swap_move.pbi : index swap_move.xcl
build swap_scratch.pbi : index swap_scratch.xcl
build tlv.pbi : index tlv.xcl
build aes_decrypt.pbi : index aes_decrypt.xcl
build aes_encrypt.pbi : index aes_encrypt.xcl
build cbc_mode.pbi : index cbc_mode.xcl
build ccm_mode.pbi : index ccm_mode.xcl
build cmac_mode.pbi : index cmac_mode.xcl
build ctr_mode.pbi : index ctr_mode.xcl
build ctr_prng.pbi : index ctr_prng.xcl
build ecc.pbi : index ecc.xcl
build ecc_dh.pbi : index ecc_dh.xcl
build ecc_dsa.pbi : index ecc_dsa.xcl
build ecc_platform_specific.pbi : index ecc_platform_specific.xcl
build hmac.pbi : index hmac.xcl
build hmac_prng.pbi : index hmac_prng.xcl
build sha256.pbi : index sha256.xcl
build utils.pbi : index utils.xcl
build sha512.pbi : index sha512.xcl
build DataShare.pbi : index DataShare.xcl
build flash_map_backend.pbi : index flash_map_backend.xcl
build secondary_bd.pbi : index secondary_bd.xcl
build app_enc_keys.pbi : index app_enc_keys.xcl
build mcuboot_main.pbi : index mcuboot_main.xcl
build rego1s_bldr_mb_part0.pbi : link AnalogIn.pbi BufferedSerial.pbi CAN.pbi DigitalIn.pbi DigitalInOut.pbi DigitalOut.pbi FlashIAP.pbi I2C.pbi InterruptIn.pbi PwmOut.pbi SerialBase.pbi SPI.pbi Ticker.pbi
build rego1s_bldr_mb_part1.pbi : link TimerEvent.pbi LowPowerTickerWrapper.pbi mbed_compat.pbi mbed_critical_section_api.pbi mbed_flash_api.pbi mbed_gpio.pbi mbed_gpio_irq.pbi mbed_itm_api.pbi mbed_lp_ticker_api.pbi mbed_lp_ticker_wrapper.pbi mbed_pinmap_common.pbi mbed_pinmap_default.pbi mbed_ticker_api.pbi
build rego1s_bldr_mb_part2.pbi : link mbed_us_ticker_api.pbi static_pinmap.pbi ATCmdParser.pbi CriticalSectionLock.pbi CThunkBase.pbi DeepSleepLock.pbi FileBase.pbi FileHandle.pbi FilePath.pbi FileSystemHandle.pbi LocalFileSystem.pbi mbed_alloc_wrappers.pbi mbed_application.pbi
build rego1s_bldr_mb_part3.pbi : link mbed_assert.pbi mbed_atomic_impl.pbi mbed_board.pbi mbed_critical.pbi mbed_error.pbi mbed_error_hist.pbi mbed_interface.pbi mbed_mem_trace.pbi mbed_mktime.pbi mbed_mpu_mgmt.pbi mbed_os_timer.pbi mbed_poll.pbi mbed_power_mgmt.pbi
build rego1s_bldr_mb_part4.pbi : link mbed_retarget.pbi mbed_rtc_time.pbi mbed_sdk_boot.pbi mbed_semihost_api.pbi mbed_stats.pbi mbed_thread.pbi mbed_wait_api_no_rtos.pbi newlib_nano_malloc_workaround.pbi Stream.pbi SysTimer.pbi FlashIAPBlockDevice.pbi BufferedBlockDevice.pbi ChainingBlockDevice.pbi
build rego1s_bldr_mb_part5.pbi : link ExhaustibleBlockDevice.pbi FlashSimBlockDevice.pbi HeapBlockDevice.pbi MBRBlockDevice.pbi ObservingBlockDevice.pbi ProfilingBlockDevice.pbi ReadOnlyBlockDevice.pbi SFDP.pbi SlicingBlockDevice.pbi analogin_api.pbi analogout_api.pbi can_api.pbi flash_api.pbi
build rego1s_bldr_mb_part6.pbi : link gpio_api.pbi gpio_irq_api.pbi i2c_api.pbi mbed_overrides.pbi pinmap.pbi port_api.pbi pwmout_api.pbi rtc_api.pbi serial_api.pbi sleep.pbi spi_api.pbi trng_api.pbi us_ticker.pbi
build rego1s_bldr_mb_part7.pbi : link system_gd32f4xx.pbi gd32f4xx_adc.pbi gd32f4xx_can.pbi gd32f4xx_crc.pbi gd32f4xx_ctc.pbi gd32f4xx_dac.pbi gd32f4xx_dbg.pbi gd32f4xx_dci.pbi gd32f4xx_dma.pbi gd32f4xx_enet.pbi gd32f4xx_exmc.pbi gd32f4xx_exti.pbi gd32f4xx_fmc.pbi
build rego1s_bldr_mb_part8.pbi : link gd32f4xx_fwdgt.pbi gd32f4xx_gpio.pbi gd32f4xx_i2c.pbi gd32f4xx_ipa.pbi gd32f4xx_iref.pbi gd32f4xx_misc.pbi gd32f4xx_pmu.pbi gd32f4xx_rcu.pbi gd32f4xx_rtc.pbi gd32f4xx_sdio.pbi gd32f4xx_spi.pbi gd32f4xx_syscfg.pbi gd32f4xx_timer.pbi
build rego1s_bldr_mb_part9.pbi : link gd32f4xx_tli.pbi gd32f4xx_trng.pbi gd32f4xx_usart.pbi gd32f4xx_wwdgt.pbi PeripheralPins.pbi boot_record.pbi bootutil_misc.pbi bootutil_public.pbi caps.pbi encrypted.pbi fault_injection_hardening.pbi fault_injection_hardening_delay_rng_mbedtls.pbi image_ec.pbi
build rego1s_bldr_mb_part10.pbi : link image_ec256.pbi image_ed25519.pbi image_rsa.pbi image_validate.pbi loader.pbi swap_misc.pbi swap_move.pbi swap_scratch.pbi tlv.pbi aes_decrypt.pbi aes_encrypt.pbi cbc_mode.pbi ccm_mode.pbi
build rego1s_bldr_mb_part11.pbi : link cmac_mode.pbi ctr_mode.pbi ctr_prng.pbi ecc.pbi ecc_dh.pbi ecc_dsa.pbi ecc_platform_specific.pbi hmac.pbi hmac_prng.pbi sha256.pbi utils.pbi sha512.pbi DataShare.pbi
build rego1s_bldr_mb_part12.pbi : link flash_map_backend.pbi secondary_bd.pbi app_enc_keys.pbi mcuboot_main.pbi
build rego1s_bldr_mb.pbd : link rego1s_bldr_mb_part0.pbi rego1s_bldr_mb_part1.pbi rego1s_bldr_mb_part2.pbi rego1s_bldr_mb_part3.pbi rego1s_bldr_mb_part4.pbi rego1s_bldr_mb_part5.pbi rego1s_bldr_mb_part6.pbi rego1s_bldr_mb_part7.pbi rego1s_bldr_mb_part8.pbi rego1s_bldr_mb_part9.pbi rego1s_bldr_mb_part10.pbi rego1s_bldr_mb_part11.pbi rego1s_bldr_mb_part12.pbi
build rego1s_bldr_mb.pbw : browsedata rego1s_bldr_mb.pbd


