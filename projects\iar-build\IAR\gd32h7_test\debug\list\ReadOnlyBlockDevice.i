














 

























































































































































































































































 

 
 
















 

 
 

















 

 
 


 
 

  #pragma system_include

 
 

 

  #pragma system_include














 


 
 


  #pragma system_include

 



 

 

 

 
#pragma rtmodel = "__dlib_version", "6"

 


 



























 


  #pragma system_include

 
 
 


  #pragma system_include

 

 

 

 

   

 
 


   #pragma system_include






 




 


 


 


 

 


 

 

 

 

 

 

 

 

 















 



















 











 























 





 



 










 














 













 








 













 













 















 











 








 








 






 





 












 





 













 






 


   


  







 







 




 






 




 




 













 

   




 







 







 







 










 





 

















 


 


 













 

   


 


 
  
 

   






  namespace std {
    typedef bool _Bool;
  }



 

 

 
  typedef wchar_t _Wchart;
  typedef wchar_t _Wintt;

 

 
typedef unsigned int     _Sizet;

 
typedef signed char   __int8_t;
typedef unsigned char  __uint8_t;
typedef signed short int   __int16_t;
typedef unsigned short int  __uint16_t;
typedef signed int   __int32_t;
typedef unsigned int  __uint32_t;
   typedef signed long long int   __int64_t;
   typedef unsigned long long int  __uint64_t;
typedef signed int   __intptr_t;
typedef unsigned int  __uintptr_t;

 
typedef struct _Mbstatet
{  
    unsigned int _Wchar;   
    unsigned int _State;   

    _Mbstatet()
      : _Wchar(0), _State(0)
    {	
    }

    _Mbstatet(const _Mbstatet& _Right)
      : _Wchar(_Right._Wchar), _State(_Right._State)
    {	
    }

    _Mbstatet& operator=(const _Mbstatet& _Right)
    {	
      _Wchar = _Right._Wchar;
      _State = _Right._State;
      return (*this);
    }

    _Mbstatet(int i)
      : _Wchar(i), _State(0)
    {	
    }
} _Mbstatet;

 

 
  typedef struct __va_list __Va_list;

  namespace std {
    typedef ::__Va_list va_list;
  }

    typedef struct __FILE _Filet;

 
typedef struct
{
    long long _Off;     
  _Mbstatet _Wstate;
} _Fpost;


 

 
  extern "C" {
   
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_StaticGuard(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_StaticGuard(void);

      _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockfilelock(_Filet *);
      _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockfilelock(_Filet *);

  typedef void *__iar_Rmtx;

  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Initdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Dstdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockdynamiclock(__iar_Rmtx *);

  }

  namespace std {
    class __iar_Lockit_Malloc
    { 
    public:
      explicit __iar_Lockit_Malloc() 
      {
        __iar_Locksyslock_Malloc();
      }
      ~__iar_Lockit_Malloc()         
      {
        __iar_Unlocksyslock_Malloc();
      }
    private:
      __iar_Lockit_Malloc(const __iar_Lockit_Malloc&);            
      __iar_Lockit_Malloc& operator=(const __iar_Lockit_Malloc&); 
    };
    class __iar_Lockit_Debug
    { 
    public:
      explicit __iar_Lockit_Debug() 
      {
        __iar_Locksyslock_Debug();
      }
      ~__iar_Lockit_Debug()         
      {
        __iar_Unlocksyslock_Debug();
      }
    private:
      __iar_Lockit_Debug(const __iar_Lockit_Debug&);            
      __iar_Lockit_Debug& operator=(const __iar_Lockit_Debug&); 
    };

    enum _Uninitialized
    { 
      _Noinit
    };
  }  





 


 
  typedef signed char          int8_t;
  typedef unsigned char        uint8_t;

  typedef signed short int         int16_t;
  typedef unsigned short int       uint16_t;

  typedef signed int         int32_t;
  typedef unsigned int       uint32_t;

  typedef signed long long int         int64_t;
  typedef unsigned long long int       uint64_t;


 
typedef signed char      int_least8_t;
typedef unsigned char    uint_least8_t;

typedef signed short int     int_least16_t;
typedef unsigned short int   uint_least16_t;

typedef signed int     int_least32_t;
typedef unsigned int   uint_least32_t;

 
  typedef signed long long int   int_least64_t;
  typedef unsigned long long int uint_least64_t;



 
typedef signed int       int_fast8_t;
typedef unsigned int     uint_fast8_t;

typedef signed int      int_fast16_t;
typedef unsigned int    uint_fast16_t;

typedef signed int      int_fast32_t;
typedef unsigned int    uint_fast32_t;

  typedef signed long long int    int_fast64_t;
  typedef unsigned long long int  uint_fast64_t;

 
typedef signed long long int          intmax_t;
typedef unsigned long long int        uintmax_t;


 
typedef signed int          intptr_t;
typedef unsigned int        uintptr_t;

 
typedef int __data_intptr_t; typedef unsigned int __data_uintptr_t;

 






















 











 

namespace mbed {




 
enum bd_error {
    BD_ERROR_OK                 = 0,      
    BD_ERROR_DEVICE_ERROR       = -4001,  
};


 
typedef uint64_t bd_addr_t;


 
typedef uint64_t bd_size_t;



 
class BlockDevice {
public:

    








 
    static BlockDevice *get_default_instance();

    
 
    virtual ~BlockDevice() {};

    


 
    virtual int init() = 0;

    


 
    virtual int deinit() = 0;

    


 
    virtual int sync()
    {
        return 0;
    }

    







 
    virtual int read(void *buffer, bd_addr_t addr, bd_size_t size) = 0;

    









 
    virtual int program(const void *buffer, bd_addr_t addr, bd_size_t size) = 0;

    







 
    virtual int erase(bd_addr_t addr, bd_size_t size)
    {
        return 0;
    }

    









 
    virtual int trim(bd_addr_t addr, bd_size_t size)
    {
        return 0;
    }

    


 
    virtual bd_size_t get_read_size() const = 0;

    



 
    virtual bd_size_t get_program_size() const = 0;

    



 
    virtual bd_size_t get_erase_size() const
    {
        return get_program_size();
    }

    




 
    virtual bd_size_t get_erase_size(bd_addr_t addr) const
    {
        return get_erase_size();
    }

    







 
    virtual int get_erase_value() const
    {
        return -1;
    }

    


 
    virtual bd_size_t size() const = 0;

    




 
    virtual bool is_valid_read(bd_addr_t addr, bd_size_t size) const
    {
        return (
                   addr % get_read_size() == 0 &&
                   size % get_read_size() == 0 &&
                   addr + size <= this->size());
    }

    




 
    virtual bool is_valid_program(bd_addr_t addr, bd_size_t size) const
    {
        return (
                   addr % get_program_size() == 0 &&
                   size % get_program_size() == 0 &&
                   addr + size <= this->size());
    }

    




 
    virtual bool is_valid_erase(bd_addr_t addr, bd_size_t size) const
    {
        return (
                   addr % get_erase_size(addr) == 0 &&
                   (addr + size) % get_erase_size(addr + size - 1) == 0 &&
                   addr + size <= this->size());
    }

    


 
    virtual const char *get_type() const = 0;
};

} 


using mbed::BlockDevice;
using mbed::bd_addr_t;
using mbed::bd_size_t;
using mbed::BD_ERROR_OK;
using mbed::BD_ERROR_DEVICE_ERROR;


 















 















 


namespace mbed {

 
 




 



































































































































 
template<typename T>
class NonCopyable {
protected:
    

 
    NonCopyable() = default;
    

 
    ~NonCopyable() = default;

public:
    


 
    NonCopyable(const NonCopyable &) = delete;

    


 
    NonCopyable &operator=(const NonCopyable &) = delete;
};

 

 

} 


 
 



 









 


class PlatformMutex: private mbed::NonCopyable<PlatformMutex> {
public:
    


 
    PlatformMutex()
    {
    }

    


 
    ~PlatformMutex()
    {
    }

    




 
    void lock()
    {
    }

    




 
    void unlock()
    {
    }
};



 

 

namespace mbed {

class ReadOnlyBlockDevice : public BlockDevice {
public:

    


 
    ReadOnlyBlockDevice(BlockDevice *bd);
    virtual ~ReadOnlyBlockDevice();

    


 
    virtual int init();

    


 
    virtual int deinit();

    


 
    virtual int sync();

    





 
    virtual int read(void *buffer, bd_addr_t addr, bd_size_t size);

    







 
    virtual int program(const void *buffer, bd_addr_t addr, bd_size_t size);

    







 
    virtual int erase(bd_addr_t addr, bd_size_t size);

    


 
    virtual bd_size_t get_read_size() const;

    


 
    virtual bd_size_t get_program_size() const;

    


 
    virtual bd_size_t get_erase_size() const;

    




 
    virtual bd_size_t get_erase_size(bd_addr_t addr) const;

    







 
    virtual int get_erase_value() const;

    


 
    virtual bd_size_t size() const;

    


 
    virtual const char *get_type() const;

private:
    BlockDevice *_bd;
};

} 


using mbed::ReadOnlyBlockDevice;


 















 

 
 


  #pragma system_include








 

















 





  #pragma system_include

 
 

  #pragma system_include

 
 

 

  #pragma system_include














 


 
 


  #pragma system_include

 
 

 

  #pragma system_include














 



 
  typedef _Sizet size_t;

typedef unsigned int __data_size_t;



 
#pragma rtmodel="__dlib_file_descriptor","1"

 

  typedef _Filet FILE;


#pragma language = save
#pragma language = extended

      extern "C" {
         extern FILE __iar_Stdin;
         extern FILE __iar_Stdout;
         extern FILE __iar_Stderr;
      }

#pragma language=restore



 
typedef _Fpost fpos_t;


 


   
  extern "C" {
    __intrinsic __nounwind    void   clearerr(FILE *);
    __intrinsic __nounwind    int    fclose(FILE *);
    __intrinsic __nounwind    int    feof(FILE *);
    __intrinsic __nounwind    int    ferror(FILE *);
    __intrinsic __nounwind    int    fflush(FILE *);
    __intrinsic __nounwind    int    fgetc(FILE *);
    __intrinsic __nounwind    int    fgetpos(FILE *__restrict, fpos_t *__restrict);
    __intrinsic __nounwind    char * fgets(char *__restrict, int, FILE *__restrict);
     __intrinsic __nounwind  FILE * fopen(const char *__restrict, const char *__restrict);
     _Pragma("__printf_args") _Pragma("library_default_requirements _Printf = unknown") __intrinsic __nounwind int    fprintf(FILE *__restrict,
                                   const char *__restrict, ...);
    __intrinsic __nounwind    int    fputc(int, FILE *);
    __intrinsic __nounwind    int    fputs(const char *__restrict, FILE *__restrict);
    __intrinsic __nounwind    size_t fread(void *__restrict, size_t, size_t,
                                 FILE *__restrict);
     __intrinsic __nounwind  FILE * freopen(const char *__restrict,
                                   const char *__restrict, FILE *__restrict);
     _Pragma("__scanf_args") _Pragma("library_default_requirements _Scanf = unknown") __intrinsic __nounwind  int    fscanf(FILE *__restrict, const char *__restrict, ...);
    __intrinsic __nounwind    int    fseek(FILE *, long, int);
    __intrinsic __nounwind    int    fsetpos(FILE *, const fpos_t *);
    __intrinsic __nounwind    long   ftell(FILE *);
    __intrinsic __nounwind    size_t fwrite(const void *__restrict, size_t, size_t,
                                  FILE *__restrict);
    __intrinsic __nounwind    void   rewind(FILE *);
    __intrinsic __nounwind    void   setbuf(FILE *__restrict, char *__restrict);
    __intrinsic __nounwind    int    setvbuf(FILE *__restrict, char *__restrict,
                                   int, size_t);
     __intrinsic __nounwind  char * tmpnam(char *);
     __intrinsic __nounwind  FILE * tmpfile(void);
    __intrinsic __nounwind    int    ungetc(int, FILE *);
     _Pragma("__printf_args") _Pragma("library_default_requirements _Printf = unknown") __intrinsic __nounwind int    vfprintf(FILE *__restrict, const char *__restrict,
                                    __Va_list);
       _Pragma("__scanf_args") _Pragma("library_default_requirements _Scanf = unknown") __intrinsic __nounwind int   vfscanf(FILE *__restrict, const char *__restrict,
                                   __Va_list);
      __intrinsic __nounwind FILE *   fdopen(signed char, const char *);
      __intrinsic __nounwind signed char fileno(FILE *);
      __intrinsic __nounwind int      getw(FILE *);
      __intrinsic __nounwind int      putw(int, FILE *);
    __intrinsic __nounwind int        getc(FILE *);
    __intrinsic __nounwind int        putc(int, FILE *);
  }

extern "C" {
  _Pragma("function_effects = no_write(1), always_returns")    __intrinsic __nounwind    void   perror(const char *);
  _Pragma("function_effects = no_write(1), always_returns")     _Pragma("__printf_args") _Pragma("library_default_requirements _Printf = unknown") __intrinsic __nounwind int    printf(const char *__restrict, ...);
  _Pragma("function_effects = no_write(1), always_returns")    __intrinsic __nounwind    int    puts(const char *);
  _Pragma("function_effects = no_write(1), always_returns")     _Pragma("__scanf_args") _Pragma("library_default_requirements _Scanf = unknown") __intrinsic __nounwind  int    scanf(const char *__restrict, ...);
  _Pragma("function_effects = no_read(1), no_write(2), always_returns")  _Pragma("__printf_args") _Pragma("library_default_requirements _Printf = unknown") __intrinsic __nounwind int    sprintf(char *__restrict,
                                              const char *__restrict, ...);
  _Pragma("function_effects = no_write(1,2), always_returns")  _Pragma("__scanf_args") _Pragma("library_default_requirements _Scanf = unknown") __intrinsic __nounwind  int    sscanf(const char *__restrict,
                                             const char *__restrict, ...);
                                       
  __intrinsic __nounwind                 int    __ungetchar(int);
  _Pragma("function_effects = no_write(1), always_returns")     _Pragma("__printf_args") _Pragma("library_default_requirements _Printf = unknown") __intrinsic __nounwind int    vprintf(const char *__restrict,
                                              __Va_list);
    _Pragma("function_effects = no_write(1), always_returns")     _Pragma("__scanf_args") _Pragma("library_default_requirements _Scanf = unknown") __intrinsic __nounwind int vscanf(const char *__restrict, __Va_list);
    _Pragma("function_effects = no_write(1,2), always_returns")  _Pragma("__scanf_args") _Pragma("library_default_requirements _Scanf = unknown") __intrinsic __nounwind int vsscanf(const char *__restrict,
                                            const char *__restrict, __Va_list);
  _Pragma("function_effects = no_read(1), no_write(2), always_returns")   _Pragma("__printf_args") _Pragma("library_default_requirements _Printf = unknown") __intrinsic __nounwind int vsprintf(char *__restrict,
                                             const char *__restrict, __Va_list);
                                 
  _Pragma("function_effects = no_write(1), always_returns") __intrinsic __nounwind size_t   __write_array(const void *, size_t, size_t);
    _Pragma("function_effects = no_read(1), no_write(3), always_returns")  _Pragma("__printf_args") _Pragma("library_default_requirements _Printf = unknown") __intrinsic __nounwind int snprintf(char *__restrict, size_t,
                                              const char *__restrict, ...);
    _Pragma("function_effects = no_read(1), no_write(3), always_returns")  _Pragma("__printf_args") _Pragma("library_default_requirements _Printf = unknown") __intrinsic __nounwind int vsnprintf(char *__restrict, size_t,
                                               const char *__restrict,
                                               __Va_list);

  __intrinsic __nounwind int                getchar(void);
  __intrinsic __nounwind int                putchar(int);
  
 
  __intrinsic __nounwind int                remove(const char *);
  __intrinsic __nounwind int                rename(const char *, const char *);
}






 

namespace std {
  using ::size_t; using ::fpos_t;

  
  
  using ::getchar; using ::perror;
  using ::putchar; using ::printf; using ::puts;
  using ::remove; using ::rename; using ::scanf;
  using ::sprintf; using ::sscanf;
  using ::vprintf; using ::vsprintf;
  using ::__ungetchar;
    using ::snprintf; using ::vsnprintf;
    using ::vscanf; using ::vsscanf;
    using ::FILE;
    using ::clearerr; using ::fclose; using ::feof;
    using ::ferror; using ::fflush; using ::fgetc;
    using ::fgetpos; using ::fgets; using ::fopen;
    using ::fprintf; using ::fputc; using ::fputs;
    using ::fread; using ::freopen; using ::fscanf;
    using ::fseek; using ::fsetpos; using ::ftell;
    using ::fwrite; using ::getc; using ::putc;
    using ::rewind; using ::setbuf; using ::setvbuf;
    using ::tmpfile;  using ::tmpnam;
    using ::ungetc; using ::vfprintf;
      using ::fdopen; using ::fileno;
      using ::getw; using ::putw;
      using ::vfscanf;
        using ::__iar_Stdin; using ::__iar_Stdout;
        using ::__iar_Stderr;
  using ::__data_size_t;
}  




 
 
 

  #pragma system_include

 
 

 

  #pragma system_include














 



 


 
  typedef   signed int ptrdiff_t;


  typedef decltype(nullptr) nullptr_t;

    typedef union
    {
      long long _ll;
      long double _ld;
      void *_vp;
    } _Max_align_t;
    typedef _Max_align_t max_align_t;






 




 
 
 

  #pragma system_include

 
 

 

  #pragma system_include














 



   


 
extern "C" {
  __intrinsic __nounwind int volatile *__aeabi_errno_addr(void);

}





 



#pragma GCC diagnostic ignored "-Wreturn-type"



 
typedef signed   int  ssize_t;  
typedef signed   long off_t;    
typedef unsigned int  nfds_t;   
typedef unsigned long long fsblkcnt_t;  
typedef unsigned int  mode_t;   
typedef unsigned int  dev_t;    
typedef unsigned long ino_t;    
typedef unsigned int  nlink_t;  
typedef unsigned int  uid_t;    
typedef unsigned int  gid_t;    



 



 
 

  #pragma system_include

 
 

 

  #pragma system_include














 



 

 

 
 
 

  #pragma system_include

 
 

 

  #pragma system_include














 




    typedef unsigned int   __time32_t;
    typedef unsigned int   clock_t;

typedef __time32_t         time_t;


struct tm
{        
  int tm_sec;
  int tm_min;
  int tm_hour;
  int tm_mday;
  int tm_mon;
  int tm_year;
  int tm_wday;
  int tm_yday;
  int tm_isdst;
    int __BSD_bug_filler1;
    int __BSD_bug_filler2;
};

struct __timespec32
{
  __time32_t tv_sec;
  long tv_nsec;
};

struct timespec
{
  time_t tv_sec;
  long tv_nsec;
};



extern "C" {
   
  __intrinsic __nounwind time_t time(time_t *);

   
   __intrinsic __nounwind char *          asctime(const struct tm *);
  __intrinsic __nounwind   clock_t         clock(void);
   __intrinsic __nounwind char *          ctime(const time_t *);
  _Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double   difftime(time_t, time_t);
   __intrinsic __nounwind struct tm *     gmtime(const time_t *);
   __intrinsic __nounwind struct tm *     localtime(const time_t *);
  __intrinsic __nounwind   time_t          mktime(struct tm *);
  __intrinsic __nounwind   int             timespec_get(struct timespec *, int);

    __intrinsic __nounwind __time32_t      __time32(__time32_t *);
    __intrinsic __nounwind char *          __ctime32(const __time32_t *);
    _Pragma("function_effects = no_state, always_returns") __intrinsic __nounwind double __difftime32(__time32_t, __time32_t);
    __intrinsic __nounwind struct tm *     __gmtime32(const __time32_t *);
    __intrinsic __nounwind struct tm *     __localtime32(const __time32_t *);
    __intrinsic __nounwind __time32_t      __mktime32(struct tm *);
    __intrinsic __nounwind int             __timespec_get32(struct __timespec32 *, int);
  __intrinsic __nounwind size_t              strftime(char *__restrict, size_t,
                                            const char *__restrict,
                                            const struct tm *__restrict);
}







 





 

 
 









 








 






 
struct stat {
    dev_t     st_dev;     
    ino_t     st_ino;     
    mode_t    st_mode;    
    nlink_t   st_nlink;   

    uid_t     st_uid;     
    gid_t     st_gid;     

    off_t     st_size;    

    time_t    st_atime;   
    time_t    st_mtime;   
    time_t    st_ctime;   
};


struct statvfs {
    unsigned long  f_bsize;    
    unsigned long  f_frsize;   

    fsblkcnt_t     f_blocks;   
    fsblkcnt_t     f_bfree;    
    fsblkcnt_t     f_bavail;   

    unsigned long  f_fsid;     

    unsigned long  f_namemax;  
};



 
struct dirent {
    char d_name[255 + 1]; 
    uint8_t d_type;          
};

enum {
    DT_UNKNOWN, 
    DT_FIFO,    
    DT_CHR,     
    DT_DIR,     
    DT_BLK,     
    DT_REG,     
    DT_LNK,     
    DT_SOCK,    
};

 

struct pollfd {
    int fd;
    short events;
    short revents;
};

 
extern "C" {
    ssize_t write(int fildes, const void *buf, size_t nbyte);
    ssize_t read(int fildes, void *buf, size_t nbyte);
    int fsync(int fildes);
    int isatty(int fildes);
} 

namespace mbed {










 
int minimal_console_putc(int c);










 
int minimal_console_getc();

} 


 

 
















 
















 

 
 




 











 











 











 











 


 
 

 



 

 
 
 

  #pragma system_include

 
 

 

  #pragma system_include














 











 


  #pragma system_include


  





 


  




 




  


 
extern "C" {

  #pragma inline=forced_no_body
  _Pragma("function_effects = no_state, no_read(1), no_write(2), returns 1, always_returns") __intrinsic __nounwind void * memcpy(void * _D, const void * _S, size_t _N)
  {
    __aeabi_memcpy(_D, _S, _N);
    return _D;
  }

  #pragma inline=forced_no_body
  _Pragma("function_effects = no_state, no_read(1), no_write(2), returns 1, always_returns") __intrinsic __nounwind void * memmove(void * _D, const void * _S, size_t _N)
  {
    __aeabi_memmove(_D, _S, _N);
    return _D;
  }

  #pragma inline=forced_no_body
  _Pragma("function_effects = no_state, no_read(1), returns 1, always_returns") __intrinsic __nounwind void * memset(void * _D, int _C, size_t _N)
  {
    __aeabi_memset(_D, _N, _C);
    return _D;
  }

}


 

 

 
extern "C" {
  _Pragma("function_effects = no_state, no_write(1,2), always_returns")   __intrinsic __nounwind   int       memcmp(const void *, const void *,
                                                   size_t);
  _Pragma("function_effects = no_state, no_read(1), no_write(2), returns 1, always_returns")  __intrinsic __nounwind void *    memcpy(void *__restrict,
                                                   const void *__restrict,
                                                   size_t);
  _Pragma("function_effects = no_state, no_read(1), no_write(2), returns 1, always_returns")  __intrinsic __nounwind void *    memmove(void *, const void *,
                                                    size_t);
  _Pragma("function_effects = no_state, no_read(1), returns 1, always_returns")     __intrinsic __nounwind void *    memset(void *, int, size_t);
  _Pragma("function_effects = no_state, no_write(2), returns 1, always_returns")     __intrinsic __nounwind char *    strcat(char *__restrict,
                                                   const char *__restrict);
  _Pragma("function_effects = no_state, no_write(1,2), always_returns")   __intrinsic __nounwind   int       strcmp(const char *, const char *);
  _Pragma("function_effects = no_write(1,2), always_returns")     __intrinsic __nounwind   int       strcoll(const char *, const char *);
  _Pragma("function_effects = no_state, no_read(1), no_write(2), returns 1, always_returns")  __intrinsic __nounwind char *    strcpy(char *__restrict,
                                                   const char *__restrict);
  _Pragma("function_effects = no_state, no_write(1,2), always_returns")   __intrinsic __nounwind   size_t    strcspn(const char *, const char *);
                    __intrinsic __nounwind char *    strerror(int);
  _Pragma("function_effects = no_state, no_write(1), always_returns")      __intrinsic __nounwind   size_t    strlen(const char *);
  _Pragma("function_effects = no_state, no_write(2), returns 1, always_returns")     __intrinsic __nounwind char *    strncat(char *__restrict,
                                                    const char *__restrict,
                                                    size_t);
  _Pragma("function_effects = no_state, no_write(1,2), always_returns")   __intrinsic __nounwind   int       strncmp(const char *, const char *,
                                                    size_t);
  _Pragma("function_effects = no_state, no_read(1), no_write(2), returns 1, always_returns")  __intrinsic __nounwind char *    strncpy(char *__restrict,
                                                    const char *__restrict,
                                                    size_t);
  _Pragma("function_effects = no_state, no_write(1,2), always_returns")   __intrinsic __nounwind   size_t    strspn(const char *, const char *);
  _Pragma("function_effects = no_write(2), always_returns")         __intrinsic __nounwind char *    strtok(char *__restrict,
                                                   const char *__restrict);
  _Pragma("function_effects = no_write(2), always_returns")        __intrinsic __nounwind   size_t    strxfrm(char *__restrict,
                                                    const char *__restrict,
                                                    size_t);
    _Pragma("function_effects = no_write(1), always_returns")      __intrinsic __nounwind   char *    strdup(const char *);
    _Pragma("function_effects = no_write(1,2), always_returns")   __intrinsic __nounwind   int       strcasecmp(const char *,
                                                       const char *);
    _Pragma("function_effects = no_write(1,2), always_returns")   __intrinsic __nounwind   int       strncasecmp(const char *,
                                                        const char *, size_t);
    _Pragma("function_effects = no_state, no_write(2), always_returns")    __intrinsic __nounwind   char *    strtok_r(char *, const char *,
                                                     char **);
    _Pragma("function_effects = no_state, no_write(1), always_returns")     __intrinsic __nounwind size_t    strnlen(char const *, size_t);
}

  extern "C" {
    _Pragma("function_effects = no_state, no_write(1), always_returns")    __intrinsic __nounwind void *      __iar_Memchr(const void *, int,
                                                         size_t);
    _Pragma("function_effects = no_state, no_write(1), always_returns")    __intrinsic __nounwind char *      __iar_Strchr(const char *, int);
    _Pragma("function_effects = no_state, no_write(1,2), always_returns") __intrinsic __nounwind char *      __iar_Strpbrk(const char *,
                                                          const char *);
    _Pragma("function_effects = no_state, no_write(1), always_returns")    __intrinsic __nounwind char *      __iar_Strrchr(const char *, int);
    _Pragma("function_effects = no_state, no_write(1,2), always_returns") __intrinsic __nounwind char *      __iar_Strstr(const char *,
                                                         const char *);
  }
  extern "C++" {
    _Pragma("function_effects = no_state, no_write(1), always_returns")    __intrinsic __nounwind const void *memchr(const void *_S, int _C,
                                                   size_t _N);
    _Pragma("function_effects = no_state, no_write(1), always_returns")    __intrinsic __nounwind const char *strchr(const char *_S, int _C);
    _Pragma("function_effects = no_state, no_write(1,2), always_returns") __intrinsic __nounwind const char *strpbrk(const char *_S,
                                                    const char *_P);
    _Pragma("function_effects = no_state, no_write(1), always_returns")    __intrinsic __nounwind const char *strrchr(const char *_S, int _C);
    _Pragma("function_effects = no_state, no_write(1,2), always_returns") __intrinsic __nounwind const char *strstr(const char *_S,
                                                   const char *_P);
    _Pragma("function_effects = no_state, no_write(1), always_returns")    __intrinsic __nounwind void *      memchr(void *_S, int _C, size_t _N);
    _Pragma("function_effects = no_state, no_write(1), always_returns")    __intrinsic __nounwind char *      strchr(char *_S, int _C);
    _Pragma("function_effects = no_state, no_write(1,2), always_returns") __intrinsic __nounwind char *      strpbrk(char *_S, const char *_P);
    _Pragma("function_effects = no_state, no_write(1), always_returns")    __intrinsic __nounwind char *      strrchr(char *_S, int _C);
    _Pragma("function_effects = no_state, no_write(1,2), always_returns") __intrinsic __nounwind char *      strstr(char *_S, const char *_P);

     
     
    #pragma inline=forced
    const void *memchr(const void *_S, int _C, size_t _N)
    {
      return __iar_Memchr(_S, _C, _N);
    }

    #pragma inline=forced
    const char *strchr(const char *_S, int _C)
    {
      return __iar_Strchr(_S, _C);
    }

    #pragma inline=forced
    const char *strpbrk(const char *_S, const char *_P)
    {
      return __iar_Strpbrk(_S, _P);
    }

    #pragma inline=forced
    const char *strrchr(const char *_S, int _C)
    {
      return __iar_Strrchr(_S, _C);
    }

    #pragma inline=forced
    const char *strstr(const char *_S, const char *_P)
    {
      return __iar_Strstr(_S, _P);
    }

     
    #pragma inline=forced
    void *memchr(void *_S, int _C, size_t _N)
    {
      return __iar_Memchr(_S, _C, _N);
    }

    #pragma inline=forced
    char *strchr(char *_S, int _C)
    {
      return __iar_Strchr(_S, _C);
    }

    #pragma inline=forced
    char *strpbrk(char *_S, const char *_P)
    {
      return __iar_Strpbrk(_S, _P);
    }

    #pragma inline=forced
    char *strrchr(char *_S, int _C)
    {
      return __iar_Strrchr(_S, _C);
    }

    #pragma inline=forced
    char *strstr(char *_S, const char *_P)
    {
      return __iar_Strstr(_S, _P);
    }
  }  






 



 
 




 














 












 











 










 
























 
































 





























 











 











 












 












 


















 




















 












 













 














 











 










typedef int FILEHANDLE;















 


 
 

extern "C" {

 
 




 



 



 





 






























 
typedef int mbed_error_status_t;






 






 






 


















 


















 










 
typedef enum _mbed_error_type_t {
    MBED_ERROR_TYPE_SYSTEM = 0,
    MBED_ERROR_TYPE_CUSTOM = 1,
    
    
    
    MBED_ERROR_TYPE_POSIX = 3
} mbed_error_type_t;















































 
typedef enum _mbed_module_type {
    MBED_MODULE_APPLICATION = 0,
    MBED_MODULE_PLATFORM,
    MBED_MODULE_KERNEL,
    MBED_MODULE_NETWORK_STACK,
    MBED_MODULE_HAL,
    MBED_MODULE_MEMORY_SUBSYSTEM,
    MBED_MODULE_FILESYSTEM,
    MBED_MODULE_BLOCK_DEVICE,
    MBED_MODULE_DRIVER,
    MBED_MODULE_DRIVER_SERIAL,
    MBED_MODULE_DRIVER_RTC,
    MBED_MODULE_DRIVER_I2C,
    MBED_MODULE_DRIVER_SPI,
    MBED_MODULE_DRIVER_GPIO,
    MBED_MODULE_DRIVER_ANALOG,
    MBED_MODULE_DRIVER_DIGITAL,
    MBED_MODULE_DRIVER_CAN,
    MBED_MODULE_DRIVER_ETHERNET,
    MBED_MODULE_DRIVER_CRC,
    MBED_MODULE_DRIVER_PWM,
    MBED_MODULE_DRIVER_QSPI,
    MBED_MODULE_DRIVER_USB,
    MBED_MODULE_DRIVER_WATCHDOG,
    MBED_MODULE_TARGET_SDK,
    MBED_MODULE_BLE,
    MBED_MODULE_NETWORK_STATS,
     

    MBED_MODULE_UNKNOWN = 255,
    MBED_MODULE_MAX = MBED_MODULE_UNKNOWN
} mbed_module_type_t;


























































































































































































































































































 

typedef enum _mbed_error_code {
    
    
    
    
    MBED_ERROR_CODE_EPERM = 1, MBED_ERROR_EPERM = -(0 + 1),                               
    MBED_ERROR_CODE_ENOENT = 2, MBED_ERROR_ENOENT = -(0 + 2),                             
    MBED_ERROR_CODE_ESRCH = 3, MBED_ERROR_ESRCH = -(0 + 3),                               
    MBED_ERROR_CODE_EINTR = 4, MBED_ERROR_EINTR = -(0 + 4),                               
    MBED_ERROR_CODE_EIO = 5, MBED_ERROR_EIO = -(0 + 5),                                   
    MBED_ERROR_CODE_ENXIO = 6, MBED_ERROR_ENXIO = -(0 + 6),                               
    MBED_ERROR_CODE_E2BIG = 7, MBED_ERROR_E2BIG = -(0 + 7),                               
    MBED_ERROR_CODE_ENOEXEC = 8, MBED_ERROR_ENOEXEC = -(0 + 8),                           
    MBED_ERROR_CODE_EBADF = 9, MBED_ERROR_EBADF = -(0 + 9),                               
    MBED_ERROR_CODE_ECHILD = 10, MBED_ERROR_ECHILD = -(0 + 10),                             
    MBED_ERROR_CODE_EAGAIN = 11, MBED_ERROR_EAGAIN = -(0 + 11),                             
    MBED_ERROR_CODE_ENOMEM = 12, MBED_ERROR_ENOMEM = -(0 + 12),                             
    MBED_ERROR_CODE_EACCES = 13, MBED_ERROR_EACCES = -(0 + 13),                             
    MBED_ERROR_CODE_EFAULT = 14, MBED_ERROR_EFAULT = -(0 + 14),                             
    MBED_ERROR_CODE_ENOTBLK = 15, MBED_ERROR_ENOTBLK = -(0 + 15),                           
    MBED_ERROR_CODE_EBUSY = 16, MBED_ERROR_EBUSY = -(0 + 16),                               
    MBED_ERROR_CODE_EEXIST = 17, MBED_ERROR_EEXIST = -(0 + 17),                             
    MBED_ERROR_CODE_EXDEV = 18, MBED_ERROR_EXDEV = -(0 + 18),                               
    MBED_ERROR_CODE_ENODEV = 19, MBED_ERROR_ENODEV = -(0 + 19),                             
    MBED_ERROR_CODE_ENOTDIR = 20, MBED_ERROR_ENOTDIR = -(0 + 20),                           
    MBED_ERROR_CODE_EISDIR = 21, MBED_ERROR_EISDIR = -(0 + 21),                             
    MBED_ERROR_CODE_EINVAL = 22, MBED_ERROR_EINVAL = -(0 + 22),                             
    MBED_ERROR_CODE_ENFILE = 23, MBED_ERROR_ENFILE = -(0 + 23),                             
    MBED_ERROR_CODE_EMFILE = 24, MBED_ERROR_EMFILE = -(0 + 24),                             
    MBED_ERROR_CODE_ENOTTY = 25, MBED_ERROR_ENOTTY = -(0 + 25),                             
    MBED_ERROR_CODE_ETXTBSY = 26, MBED_ERROR_ETXTBSY = -(0 + 26),                           
    MBED_ERROR_CODE_EFBIG = 27, MBED_ERROR_EFBIG = -(0 + 27),                               
    MBED_ERROR_CODE_ENOSPC = 28, MBED_ERROR_ENOSPC = -(0 + 28),                             
    MBED_ERROR_CODE_ESPIPE = 29, MBED_ERROR_ESPIPE = -(0 + 29),                             
    MBED_ERROR_CODE_EROFS = 30, MBED_ERROR_EROFS = -(0 + 30),                               
    MBED_ERROR_CODE_EMLINK = 31, MBED_ERROR_EMLINK = -(0 + 31),                             
    MBED_ERROR_CODE_EPIPE = 32, MBED_ERROR_EPIPE = -(0 + 32),                               
    MBED_ERROR_CODE_EDOM = 33, MBED_ERROR_EDOM = -(0 + 33),                                 
    MBED_ERROR_CODE_ERANGE = 34, MBED_ERROR_ERANGE = -(0 + 34),                             
    MBED_ERROR_CODE_EDEADLK = 35, MBED_ERROR_EDEADLK = -(0 + 35),                           
    MBED_ERROR_CODE_ENAMETOOLONG = 36, MBED_ERROR_ENAMETOOLONG = -(0 + 36),                 
    MBED_ERROR_CODE_ENOLCK = 37, MBED_ERROR_ENOLCK = -(0 + 37),                             
    MBED_ERROR_CODE_ENOSYS = 38, MBED_ERROR_ENOSYS = -(0 + 38),                             
    MBED_ERROR_CODE_ENOTEMPTY = 39, MBED_ERROR_ENOTEMPTY = -(0 + 39),                       
    MBED_ERROR_CODE_ELOOP = 40, MBED_ERROR_ELOOP = -(0 + 40),                               
    MBED_ERROR_CODE_EWOULDBLOCK = 11, MBED_ERROR_EWOULDBLOCK = -(0 + 11),                        
    MBED_ERROR_CODE_ENOMSG = 42, MBED_ERROR_ENOMSG = -(0 + 42),                             
    MBED_ERROR_CODE_EIDRM = 43, MBED_ERROR_EIDRM = -(0 + 43),                               
    MBED_ERROR_CODE_ECHRNG = 44, MBED_ERROR_ECHRNG = -(0 + 44),                             
    MBED_ERROR_CODE_EL2NSYNC = 245, MBED_ERROR_EL2NSYNC = -(0 + 245),                         
    MBED_ERROR_CODE_EL3HLT = 246, MBED_ERROR_EL3HLT = -(0 + 246),                             
    MBED_ERROR_CODE_EL3RST = 47, MBED_ERROR_EL3RST = -(0 + 47),                             
    MBED_ERROR_CODE_ELNRNG = 48, MBED_ERROR_ELNRNG = -(0 + 48),                             
    MBED_ERROR_CODE_EUNATCH = 49, MBED_ERROR_EUNATCH = -(0 + 49),                           
    MBED_ERROR_CODE_ENOCSI = 50, MBED_ERROR_ENOCSI = -(0 + 50),                             
    MBED_ERROR_CODE_EL2HLT = 51, MBED_ERROR_EL2HLT = -(0 + 51),                             
    MBED_ERROR_CODE_EBADE = 52, MBED_ERROR_EBADE = -(0 + 52),                               
    MBED_ERROR_CODE_EBADR = 53, MBED_ERROR_EBADR = -(0 + 53),                               
    MBED_ERROR_CODE_EXFULL = 54, MBED_ERROR_EXFULL = -(0 + 54),                             
    MBED_ERROR_CODE_ENOANO = 55, MBED_ERROR_ENOANO = -(0 + 55),                             
    MBED_ERROR_CODE_EBADRQC = 56, MBED_ERROR_EBADRQC = -(0 + 56),                           
    MBED_ERROR_CODE_EBADSLT = 57, MBED_ERROR_EBADSLT = -(0 + 57),                           
    MBED_ERROR_CODE_EDEADLOCK = 35, MBED_ERROR_EDEADLOCK = -(0 + 35),                         
    MBED_ERROR_CODE_EBFONT = 59, MBED_ERROR_EBFONT = -(0 + 59),                             
    MBED_ERROR_CODE_ENOSTR = 60, MBED_ERROR_ENOSTR = -(0 + 60),                             
    MBED_ERROR_CODE_ENODATA = 61, MBED_ERROR_ENODATA = -(0 + 61),                           
    MBED_ERROR_CODE_ETIME = 62, MBED_ERROR_ETIME = -(0 + 62),                               
    MBED_ERROR_CODE_ENOSR = 63, MBED_ERROR_ENOSR = -(0 + 63),                               
    MBED_ERROR_CODE_ENONET = 64, MBED_ERROR_ENONET = -(0 + 64),                             
    MBED_ERROR_CODE_ENOPKG = 65, MBED_ERROR_ENOPKG = -(0 + 65),                             
    MBED_ERROR_CODE_EREMOTE = 66, MBED_ERROR_EREMOTE = -(0 + 66),                           
    MBED_ERROR_CODE_ENOLINK = 67, MBED_ERROR_ENOLINK = -(0 + 67),                           
    MBED_ERROR_CODE_EADV = 68, MBED_ERROR_EADV = -(0 + 68),                                 
    MBED_ERROR_CODE_ESRMNT = 69, MBED_ERROR_ESRMNT = -(0 + 69),                             
    MBED_ERROR_CODE_ECOMM = 70, MBED_ERROR_ECOMM = -(0 + 70),                               
    MBED_ERROR_CODE_EPROTO = 71, MBED_ERROR_EPROTO = -(0 + 71),                             
    MBED_ERROR_CODE_EMULTIHOP = 72, MBED_ERROR_EMULTIHOP = -(0 + 72),                       
    MBED_ERROR_CODE_EDOTDOT = 73, MBED_ERROR_EDOTDOT = -(0 + 73),                           
    MBED_ERROR_CODE_EBADMSG = 74, MBED_ERROR_EBADMSG = -(0 + 74),                           
    MBED_ERROR_CODE_EOVERFLOW = 75, MBED_ERROR_EOVERFLOW = -(0 + 75),                       
    MBED_ERROR_CODE_ENOTUNIQ = 76, MBED_ERROR_ENOTUNIQ = -(0 + 76),                         
    MBED_ERROR_CODE_EBADFD = 277, MBED_ERROR_EBADFD = -(0 + 277),                             
    MBED_ERROR_CODE_EREMCHG = 78, MBED_ERROR_EREMCHG = -(0 + 78),                           
    MBED_ERROR_CODE_ELIBACC = 79, MBED_ERROR_ELIBACC = -(0 + 79),                           
    MBED_ERROR_CODE_ELIBBAD = 80, MBED_ERROR_ELIBBAD = -(0 + 80),                           
    MBED_ERROR_CODE_ELIBSCN = 81, MBED_ERROR_ELIBSCN = -(0 + 81),                           
    MBED_ERROR_CODE_ELIBMAX = 82, MBED_ERROR_ELIBMAX = -(0 + 82),                           
    MBED_ERROR_CODE_ELIBEXEC = 83, MBED_ERROR_ELIBEXEC = -(0 + 83),                         
    MBED_ERROR_CODE_EILSEQ = 84, MBED_ERROR_EILSEQ = -(0 + 84),                             
    MBED_ERROR_CODE_ERESTART = 285, MBED_ERROR_ERESTART = -(0 + 285),                         
    MBED_ERROR_CODE_ESTRPIPE = 86, MBED_ERROR_ESTRPIPE = -(0 + 86),                         
    MBED_ERROR_CODE_EUSERS = 87, MBED_ERROR_EUSERS = -(0 + 87),                             
    MBED_ERROR_CODE_ENOTSOCK = 88, MBED_ERROR_ENOTSOCK = -(0 + 88),                         
    MBED_ERROR_CODE_EDESTADDRREQ = 89, MBED_ERROR_EDESTADDRREQ = -(0 + 89),                 
    MBED_ERROR_CODE_EMSGSIZE = 90, MBED_ERROR_EMSGSIZE = -(0 + 90),                         
    MBED_ERROR_CODE_EPROTOTYPE = 91, MBED_ERROR_EPROTOTYPE = -(0 + 91),                     
    MBED_ERROR_CODE_ENOPROTOOPT = 92, MBED_ERROR_ENOPROTOOPT = -(0 + 92),                   
    MBED_ERROR_CODE_EPROTONOSUPPORT = 93, MBED_ERROR_EPROTONOSUPPORT = -(0 + 93),           
    MBED_ERROR_CODE_ESOCKTNOSUPPORT = 94, MBED_ERROR_ESOCKTNOSUPPORT = -(0 + 94),           
    MBED_ERROR_CODE_EOPNOTSUPP = 95, MBED_ERROR_EOPNOTSUPP = -(0 + 95),                     
    MBED_ERROR_CODE_EPFNOSUPPORT = 96, MBED_ERROR_EPFNOSUPPORT = -(0 + 96),                 
    MBED_ERROR_CODE_EAFNOSUPPORT = 97, MBED_ERROR_EAFNOSUPPORT = -(0 + 97),                 
    MBED_ERROR_CODE_EADDRINUSE = 98, MBED_ERROR_EADDRINUSE = -(0 + 98),                     
    MBED_ERROR_CODE_EADDRNOTAVAIL = 99, MBED_ERROR_EADDRNOTAVAIL = -(0 + 99),               
    MBED_ERROR_CODE_ENETDOWN = 100, MBED_ERROR_ENETDOWN = -(0 + 100),                         
    MBED_ERROR_CODE_ENETUNREACH = 101, MBED_ERROR_ENETUNREACH = -(0 + 101),                   
    MBED_ERROR_CODE_ENETRESET = 102, MBED_ERROR_ENETRESET = -(0 + 102),                       
    MBED_ERROR_CODE_ECONNABORTED = 103, MBED_ERROR_ECONNABORTED = -(0 + 103),                 
    MBED_ERROR_CODE_ECONNRESET = 104, MBED_ERROR_ECONNRESET = -(0 + 104),                     
    MBED_ERROR_CODE_ENOBUFS = 105, MBED_ERROR_ENOBUFS = -(0 + 105),                           
    MBED_ERROR_CODE_EISCONN = 106, MBED_ERROR_EISCONN = -(0 + 106),                           
    MBED_ERROR_CODE_ENOTCONN = 107, MBED_ERROR_ENOTCONN = -(0 + 107),                         
    MBED_ERROR_CODE_ESHUTDOWN = 208, MBED_ERROR_ESHUTDOWN = -(0 + 208),                       
    MBED_ERROR_CODE_ETOOMANYREFS = 109, MBED_ERROR_ETOOMANYREFS = -(0 + 109),                 
    MBED_ERROR_CODE_ETIMEDOUT = 110, MBED_ERROR_ETIMEDOUT = -(0 + 110),                       
    MBED_ERROR_CODE_ECONNREFUSED = 111, MBED_ERROR_ECONNREFUSED = -(0 + 111),                 
    MBED_ERROR_CODE_EHOSTDOWN = 112, MBED_ERROR_EHOSTDOWN = -(0 + 112),                       
    MBED_ERROR_CODE_EHOSTUNREACH = 113, MBED_ERROR_EHOSTUNREACH = -(0 + 113),                 
    MBED_ERROR_CODE_EALREADY = 114, MBED_ERROR_EALREADY = -(0 + 114),                         
    MBED_ERROR_CODE_EINPROGRESS = 115, MBED_ERROR_EINPROGRESS = -(0 + 115),                   
    MBED_ERROR_CODE_ESTALE = 116, MBED_ERROR_ESTALE = -(0 + 116),                             
    MBED_ERROR_CODE_EUCLEAN = 217, MBED_ERROR_EUCLEAN = -(0 + 217),                           
    MBED_ERROR_CODE_ENOTNAM = 218, MBED_ERROR_ENOTNAM = -(0 + 218),                           
    MBED_ERROR_CODE_ENAVAIL = 219, MBED_ERROR_ENAVAIL = -(0 + 219),                           
    MBED_ERROR_CODE_EISNAM = 220, MBED_ERROR_EISNAM = -(0 + 220),                             
    MBED_ERROR_CODE_EREMOTEIO = 221, MBED_ERROR_EREMOTEIO = -(0 + 221),                       
    MBED_ERROR_CODE_EDQUOT = 122, MBED_ERROR_EDQUOT = -(0 + 122),                             
    MBED_ERROR_CODE_ENOMEDIUM = 223, MBED_ERROR_ENOMEDIUM = -(0 + 223),                       
    MBED_ERROR_CODE_EMEDIUMTYPE = 224, MBED_ERROR_EMEDIUMTYPE = -(0 + 224),                   
    MBED_ERROR_CODE_ECANCELED = 125, MBED_ERROR_ECANCELED = -(0 + 125),                       
    MBED_ERROR_CODE_ENOKEY = 226, MBED_ERROR_ENOKEY = -(0 + 226),                             
    MBED_ERROR_CODE_EKEYEXPIRED = 227, MBED_ERROR_EKEYEXPIRED = -(0 + 227),                   
    MBED_ERROR_CODE_EKEYREVOKED = 228, MBED_ERROR_EKEYREVOKED = -(0 + 228),                   
    MBED_ERROR_CODE_EKEYREJECTED = 229, MBED_ERROR_EKEYREJECTED = -(0 + 229),                 
    MBED_ERROR_CODE_EOWNERDEAD = 130, MBED_ERROR_EOWNERDEAD = -(0 + 130),                     
    MBED_ERROR_CODE_ENOTRECOVERABLE = 131, MBED_ERROR_ENOTRECOVERABLE = -(0 + 131),           

    
    
    
    MBED_ERROR_CODE_UNKNOWN = 256 + 0, MBED_ERROR_UNKNOWN = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_UNKNOWN & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                                
    MBED_ERROR_CODE_INVALID_ARGUMENT = 256 + 1, MBED_ERROR_INVALID_ARGUMENT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_INVALID_ARGUMENT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                       
    MBED_ERROR_CODE_INVALID_DATA_DETECTED = 256 + 2, MBED_ERROR_INVALID_DATA_DETECTED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_INVALID_DATA_DETECTED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                  
    MBED_ERROR_CODE_INVALID_FORMAT = 256 + 3, MBED_ERROR_INVALID_FORMAT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_INVALID_FORMAT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                         
    MBED_ERROR_CODE_INVALID_INDEX = 256 + 4, MBED_ERROR_INVALID_INDEX = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_INVALID_INDEX & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                          
    MBED_ERROR_CODE_INVALID_SIZE = 256 + 5, MBED_ERROR_INVALID_SIZE = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_INVALID_SIZE & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                           
    MBED_ERROR_CODE_INVALID_OPERATION = 256 + 6, MBED_ERROR_INVALID_OPERATION = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_INVALID_OPERATION & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                      
    MBED_ERROR_CODE_ITEM_NOT_FOUND = 256 + 7, MBED_ERROR_ITEM_NOT_FOUND = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_ITEM_NOT_FOUND & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                         
    MBED_ERROR_CODE_ACCESS_DENIED = 256 + 8, MBED_ERROR_ACCESS_DENIED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_ACCESS_DENIED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                          
    MBED_ERROR_CODE_UNSUPPORTED = 256 + 9, MBED_ERROR_UNSUPPORTED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_UNSUPPORTED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                            
    MBED_ERROR_CODE_BUFFER_FULL = 256 + 10, MBED_ERROR_BUFFER_FULL = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_BUFFER_FULL & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                           
    MBED_ERROR_CODE_MEDIA_FULL = 256 + 11, MBED_ERROR_MEDIA_FULL = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_MEDIA_FULL & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                            
    MBED_ERROR_CODE_ALREADY_IN_USE = 256 + 12, MBED_ERROR_ALREADY_IN_USE = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_ALREADY_IN_USE & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                        
    MBED_ERROR_CODE_TIME_OUT = 256 + 13, MBED_ERROR_TIME_OUT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_TIME_OUT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                              
    MBED_ERROR_CODE_NOT_READY = 256 + 14, MBED_ERROR_NOT_READY = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_NOT_READY & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                             
    MBED_ERROR_CODE_FAILED_OPERATION = 256 + 15, MBED_ERROR_FAILED_OPERATION = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_FAILED_OPERATION & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                      
    MBED_ERROR_CODE_OPERATION_PROHIBITED = 256 + 16, MBED_ERROR_OPERATION_PROHIBITED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_OPERATION_PROHIBITED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                  
    MBED_ERROR_CODE_OPERATION_ABORTED = 256 + 17, MBED_ERROR_OPERATION_ABORTED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_OPERATION_ABORTED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                     
    MBED_ERROR_CODE_WRITE_PROTECTED = 256 + 18, MBED_ERROR_WRITE_PROTECTED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_WRITE_PROTECTED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                       
    MBED_ERROR_CODE_NO_RESPONSE = 256 + 19, MBED_ERROR_NO_RESPONSE = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_NO_RESPONSE & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                           
    MBED_ERROR_CODE_SEMAPHORE_LOCK_FAILED = 256 + 20, MBED_ERROR_SEMAPHORE_LOCK_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_SEMAPHORE_LOCK_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                 
    MBED_ERROR_CODE_MUTEX_LOCK_FAILED = 256 + 21, MBED_ERROR_MUTEX_LOCK_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_MUTEX_LOCK_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                     
    MBED_ERROR_CODE_SEMAPHORE_UNLOCK_FAILED = 256 + 22, MBED_ERROR_SEMAPHORE_UNLOCK_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_SEMAPHORE_UNLOCK_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),               
    MBED_ERROR_CODE_MUTEX_UNLOCK_FAILED = 256 + 23, MBED_ERROR_MUTEX_UNLOCK_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_MUTEX_UNLOCK_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                   
    MBED_ERROR_CODE_CRC_ERROR = 256 + 24, MBED_ERROR_CRC_ERROR = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_CRC_ERROR & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                             
    MBED_ERROR_CODE_OPEN_FAILED = 256 + 25, MBED_ERROR_OPEN_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_OPEN_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                           
    MBED_ERROR_CODE_CLOSE_FAILED = 256 + 26, MBED_ERROR_CLOSE_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_CLOSE_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                          
    MBED_ERROR_CODE_READ_FAILED = 256 + 27, MBED_ERROR_READ_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_READ_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                           
    MBED_ERROR_CODE_WRITE_FAILED = 256 + 28, MBED_ERROR_WRITE_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_WRITE_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                          
    MBED_ERROR_CODE_INITIALIZATION_FAILED = 256 + 29, MBED_ERROR_INITIALIZATION_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_INITIALIZATION_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                 
    MBED_ERROR_CODE_BOOT_FAILURE = 256 + 30, MBED_ERROR_BOOT_FAILURE = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_BOOT_FAILURE & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                          
    MBED_ERROR_CODE_OUT_OF_MEMORY = 256 + 31, MBED_ERROR_OUT_OF_MEMORY = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_OUT_OF_MEMORY & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                         
    MBED_ERROR_CODE_OUT_OF_RESOURCES = 256 + 32, MBED_ERROR_OUT_OF_RESOURCES = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_OUT_OF_RESOURCES & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                      
    MBED_ERROR_CODE_ALLOC_FAILED = 256 + 33, MBED_ERROR_ALLOC_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_ALLOC_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                          
    MBED_ERROR_CODE_FREE_FAILED = 256 + 34, MBED_ERROR_FREE_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_FREE_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                           
    MBED_ERROR_CODE_OVERFLOW = 256 + 35, MBED_ERROR_OVERFLOW = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_OVERFLOW & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                              
    MBED_ERROR_CODE_UNDERFLOW = 256 + 36, MBED_ERROR_UNDERFLOW = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_UNDERFLOW & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                             
    MBED_ERROR_CODE_STACK_OVERFLOW = 256 + 37, MBED_ERROR_STACK_OVERFLOW = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_STACK_OVERFLOW & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                        
    MBED_ERROR_CODE_ISR_QUEUE_OVERFLOW = 256 + 38, MBED_ERROR_ISR_QUEUE_OVERFLOW = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_ISR_QUEUE_OVERFLOW & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                    
    MBED_ERROR_CODE_TIMER_QUEUE_OVERFLOW = 256 + 39, MBED_ERROR_TIMER_QUEUE_OVERFLOW = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_TIMER_QUEUE_OVERFLOW & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                  
    MBED_ERROR_CODE_CLIB_SPACE_UNAVAILABLE = 256 + 40, MBED_ERROR_CLIB_SPACE_UNAVAILABLE = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_CLIB_SPACE_UNAVAILABLE & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                
    MBED_ERROR_CODE_CLIB_EXCEPTION = 256 + 41, MBED_ERROR_CLIB_EXCEPTION = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_CLIB_EXCEPTION & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                        
    MBED_ERROR_CODE_CLIB_MUTEX_INIT_FAILURE = 256 + 42, MBED_ERROR_CLIB_MUTEX_INIT_FAILURE = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_CLIB_MUTEX_INIT_FAILURE & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),               
    MBED_ERROR_CODE_CREATE_FAILED = 256 + 43, MBED_ERROR_CREATE_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_CREATE_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                         
    MBED_ERROR_CODE_DELETE_FAILED = 256 + 44, MBED_ERROR_DELETE_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_DELETE_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                         
    MBED_ERROR_CODE_THREAD_CREATE_FAILED = 256 + 45, MBED_ERROR_THREAD_CREATE_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_THREAD_CREATE_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                  
    MBED_ERROR_CODE_THREAD_DELETE_FAILED = 256 + 46, MBED_ERROR_THREAD_DELETE_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_THREAD_DELETE_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                  
    MBED_ERROR_CODE_PROHIBITED_IN_ISR_CONTEXT = 256 + 47, MBED_ERROR_PROHIBITED_IN_ISR_CONTEXT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_PROHIBITED_IN_ISR_CONTEXT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),             
    MBED_ERROR_CODE_PINMAP_INVALID = 256 + 48, MBED_ERROR_PINMAP_INVALID = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_PINMAP_INVALID & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                        
    MBED_ERROR_CODE_RTOS_EVENT = 256 + 49, MBED_ERROR_RTOS_EVENT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_RTOS_EVENT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                            
    MBED_ERROR_CODE_RTOS_THREAD_EVENT = 256 + 50, MBED_ERROR_RTOS_THREAD_EVENT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_RTOS_THREAD_EVENT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                     
    MBED_ERROR_CODE_RTOS_MUTEX_EVENT = 256 + 51, MBED_ERROR_RTOS_MUTEX_EVENT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_RTOS_MUTEX_EVENT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                      
    MBED_ERROR_CODE_RTOS_SEMAPHORE_EVENT = 256 + 52, MBED_ERROR_RTOS_SEMAPHORE_EVENT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_RTOS_SEMAPHORE_EVENT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                  
    MBED_ERROR_CODE_RTOS_MEMORY_POOL_EVENT = 256 + 53, MBED_ERROR_RTOS_MEMORY_POOL_EVENT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_RTOS_MEMORY_POOL_EVENT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                
    MBED_ERROR_CODE_RTOS_TIMER_EVENT = 256 + 54, MBED_ERROR_RTOS_TIMER_EVENT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_RTOS_TIMER_EVENT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                      
    MBED_ERROR_CODE_RTOS_EVENT_FLAGS_EVENT = 256 + 55, MBED_ERROR_RTOS_EVENT_FLAGS_EVENT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_RTOS_EVENT_FLAGS_EVENT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                
    MBED_ERROR_CODE_RTOS_MESSAGE_QUEUE_EVENT = 256 + 56, MBED_ERROR_RTOS_MESSAGE_QUEUE_EVENT = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_RTOS_MESSAGE_QUEUE_EVENT & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),              
    MBED_ERROR_CODE_DEVICE_BUSY = 256 + 57, MBED_ERROR_DEVICE_BUSY = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_DEVICE_BUSY & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                           
    MBED_ERROR_CODE_CONFIG_UNSUPPORTED = 256 + 58, MBED_ERROR_CONFIG_UNSUPPORTED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_CONFIG_UNSUPPORTED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                    
    MBED_ERROR_CODE_CONFIG_MISMATCH = 256 + 59, MBED_ERROR_CONFIG_MISMATCH = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_CONFIG_MISMATCH & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                       
    MBED_ERROR_CODE_ALREADY_INITIALIZED = 256 + 60, MBED_ERROR_ALREADY_INITIALIZED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_ALREADY_INITIALIZED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                   
    MBED_ERROR_CODE_HARDFAULT_EXCEPTION = 256 + 61, MBED_ERROR_HARDFAULT_EXCEPTION = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_HARDFAULT_EXCEPTION & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                   
    MBED_ERROR_CODE_MEMMANAGE_EXCEPTION = 256 + 62, MBED_ERROR_MEMMANAGE_EXCEPTION = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_MEMMANAGE_EXCEPTION & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                   
    MBED_ERROR_CODE_BUSFAULT_EXCEPTION = 256 + 63, MBED_ERROR_BUSFAULT_EXCEPTION = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_BUSFAULT_EXCEPTION & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                    
    MBED_ERROR_CODE_USAGEFAULT_EXCEPTION = 256 + 64, MBED_ERROR_USAGEFAULT_EXCEPTION = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_USAGEFAULT_EXCEPTION & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                  
    MBED_ERROR_CODE_BLE_NO_FRAME_INITIALIZED = 256 + 65, MBED_ERROR_BLE_NO_FRAME_INITIALIZED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_BLE_NO_FRAME_INITIALIZED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),              
    MBED_ERROR_CODE_BLE_BACKEND_CREATION_FAILED = 256 + 66, MBED_ERROR_BLE_BACKEND_CREATION_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_BLE_BACKEND_CREATION_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),           
    MBED_ERROR_CODE_BLE_BACKEND_NOT_INITIALIZED = 256 + 67, MBED_ERROR_BLE_BACKEND_NOT_INITIALIZED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_BLE_BACKEND_NOT_INITIALIZED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),           
    MBED_ERROR_CODE_ASSERTION_FAILED = 256 + 68, MBED_ERROR_ASSERTION_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_ASSERTION_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                      
    MBED_ERROR_CODE_AUTHENTICATION_FAILED = 256 + 69, MBED_ERROR_AUTHENTICATION_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_AUTHENTICATION_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                 
    MBED_ERROR_CODE_RBP_AUTHENTICATION_FAILED = 256 + 70, MBED_ERROR_RBP_AUTHENTICATION_FAILED = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_RBP_AUTHENTICATION_FAILED & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),             
    MBED_ERROR_CODE_BLE_USE_INCOMPATIBLE_API = 256 + 71, MBED_ERROR_BLE_USE_INCOMPATIBLE_API = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_BLE_USE_INCOMPATIBLE_API & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),              
    MBED_ERROR_CODE_BLE_ILLEGAL_STATE = 256 + 72, MBED_ERROR_BLE_ILLEGAL_STATE = (mbed_error_status_t) ((0x80000000) | ((mbed_error_status_t) (MBED_ERROR_CODE_BLE_ILLEGAL_STATE & (0x0000FFFF)) << (0)) | ((mbed_error_status_t) (MBED_MODULE_UNKNOWN & (0x000000FF)) << (16)) | ((mbed_error_status_t) (MBED_ERROR_TYPE_SYSTEM & (0x00000003)) << (29))),                     

    
    
    

    
     
    

} mbed_error_code_t;




















 
typedef struct _mbed_error_ctx {
    mbed_error_status_t error_status;
    uint32_t error_address;
    uint32_t error_value;
    uint32_t thread_id;
    uint32_t thread_entry_address;
    uint32_t thread_stack_size;
    uint32_t thread_stack_mem;
    uint32_t thread_current_sp;
    char error_filename[16];
    uint32_t error_line_number;
} mbed_error_ctx;









































 

[[noreturn]] void error(const char *format, ...) ;













 













 













 








 
typedef void (*mbed_error_hook_t)(const mbed_error_ctx *error_ctx);








 
void mbed_error_hook(const mbed_error_ctx *error_context);













 
void mbed_error_reboot_callback(mbed_error_ctx *error_context);







 

mbed_error_status_t mbed_error_initialize(void);








 
mbed_error_status_t mbed_get_reboot_error_info(mbed_error_ctx *error_info);





 
mbed_error_status_t mbed_reset_reboot_error_info(void);






 
mbed_error_status_t mbed_reset_reboot_count(void);



















 
mbed_error_status_t mbed_warning(mbed_error_status_t error_status, const char *error_msg, unsigned int error_value, const char *filename, int line_number);





 
mbed_error_status_t mbed_get_first_error(void);





 
mbed_error_status_t mbed_get_last_error(void);





 
int mbed_get_error_count(void);





 
bool mbed_get_error_in_progress(void);


















 
[[noreturn]] mbed_error_status_t mbed_error(mbed_error_status_t error_status, const char *error_msg, unsigned int error_value, const char *filename, int line_number);






















 
mbed_error_status_t mbed_set_error_hook(mbed_error_hook_t custom_error_hook);







 
mbed_error_status_t mbed_get_first_error_info(mbed_error_ctx *error_info);







 
mbed_error_status_t mbed_get_last_error_info(mbed_error_ctx *error_info);





 
mbed_error_status_t mbed_clear_all_errors(void);








 
mbed_error_status_t mbed_make_error(mbed_error_type_t error_type, mbed_module_type_t module, mbed_error_code_t error_code);





 
int mbed_get_error_hist_count(void);











 
mbed_error_status_t mbed_get_error_hist_info(int index, mbed_error_ctx *error_info);











 
mbed_error_status_t mbed_save_error_hist(const char *path);

}


 
 



namespace mbed {

ReadOnlyBlockDevice::ReadOnlyBlockDevice(BlockDevice *bd)
    : _bd(bd)
{
    
}

ReadOnlyBlockDevice::~ReadOnlyBlockDevice()
{
    
}

int ReadOnlyBlockDevice::init()
{
    return _bd->init();
}

int ReadOnlyBlockDevice::deinit()
{
    return _bd->deinit();
}

int ReadOnlyBlockDevice::sync()
{
    return _bd->sync();
}

int ReadOnlyBlockDevice::read(void *buffer, bd_addr_t addr, bd_size_t size)
{
    return _bd->read(buffer, addr, size);
}

int ReadOnlyBlockDevice::program(const void *buffer, bd_addr_t addr, bd_size_t size)
{
    return MBED_ERROR_WRITE_PROTECTED;
}

int ReadOnlyBlockDevice::erase(bd_addr_t addr, bd_size_t size)
{
    return MBED_ERROR_WRITE_PROTECTED;
}

bd_size_t ReadOnlyBlockDevice::get_read_size() const
{
    return _bd->get_read_size();
}

bd_size_t ReadOnlyBlockDevice::get_program_size() const
{
    return _bd->get_program_size();
}

bd_size_t ReadOnlyBlockDevice::get_erase_size() const
{
    return _bd->get_erase_size();
}

bd_size_t ReadOnlyBlockDevice::get_erase_size(bd_addr_t addr) const
{
    return _bd->get_erase_size(addr);
}

int ReadOnlyBlockDevice::get_erase_value() const
{
    return _bd->get_erase_value();
}

bd_size_t ReadOnlyBlockDevice::size() const
{
    return _bd->size();
}

const char *ReadOnlyBlockDevice::get_type() const
{
    if (_bd != 0) {
        return _bd->get_type();
    }

    return 0;
}

} 

 
