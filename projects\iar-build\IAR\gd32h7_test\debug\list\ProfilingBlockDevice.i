














 

























































































































































































































































 
















 

 
 

















 

 
 


 
 

  #pragma system_include

 
 

 

  #pragma system_include














 


 
 


  #pragma system_include

 



 

 

 

 
#pragma rtmodel = "__dlib_version", "6"

 


 



























 


  #pragma system_include

 
 
 


  #pragma system_include

 

 

 

 

   

 
 


   #pragma system_include






 




 


 


 


 

 


 

 

 

 

 

 

 

 

 















 



















 











 























 





 



 










 














 













 








 













 













 















 











 








 








 






 





 












 





 













 






 


   


  







 







 




 






 




 




 













 

   




 







 







 







 










 





 

















 


 


 













 

   


 


 
  
 

   






  namespace std {
    typedef bool _Bool;
  }



 

 

 
  typedef wchar_t _Wchart;
  typedef wchar_t _Wintt;

 

 
typedef unsigned int     _Sizet;

 
typedef signed char   __int8_t;
typedef unsigned char  __uint8_t;
typedef signed short int   __int16_t;
typedef unsigned short int  __uint16_t;
typedef signed int   __int32_t;
typedef unsigned int  __uint32_t;
   typedef signed long long int   __int64_t;
   typedef unsigned long long int  __uint64_t;
typedef signed int   __intptr_t;
typedef unsigned int  __uintptr_t;

 
typedef struct _Mbstatet
{  
    unsigned int _Wchar;   
    unsigned int _State;   

    _Mbstatet()
      : _Wchar(0), _State(0)
    {	
    }

    _Mbstatet(const _Mbstatet& _Right)
      : _Wchar(_Right._Wchar), _State(_Right._State)
    {	
    }

    _Mbstatet& operator=(const _Mbstatet& _Right)
    {	
      _Wchar = _Right._Wchar;
      _State = _Right._State;
      return (*this);
    }

    _Mbstatet(int i)
      : _Wchar(i), _State(0)
    {	
    }
} _Mbstatet;

 

 
  typedef struct __va_list __Va_list;

  namespace std {
    typedef ::__Va_list va_list;
  }

    typedef struct __FILE _Filet;

 
typedef struct
{
    long long _Off;     
  _Mbstatet _Wstate;
} _Fpost;


 

 
  extern "C" {
   
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_StaticGuard(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_StaticGuard(void);

      _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockfilelock(_Filet *);
      _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockfilelock(_Filet *);

  typedef void *__iar_Rmtx;

  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Initdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Dstdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockdynamiclock(__iar_Rmtx *);

  }

  namespace std {
    class __iar_Lockit_Malloc
    { 
    public:
      explicit __iar_Lockit_Malloc() 
      {
        __iar_Locksyslock_Malloc();
      }
      ~__iar_Lockit_Malloc()         
      {
        __iar_Unlocksyslock_Malloc();
      }
    private:
      __iar_Lockit_Malloc(const __iar_Lockit_Malloc&);            
      __iar_Lockit_Malloc& operator=(const __iar_Lockit_Malloc&); 
    };
    class __iar_Lockit_Debug
    { 
    public:
      explicit __iar_Lockit_Debug() 
      {
        __iar_Locksyslock_Debug();
      }
      ~__iar_Lockit_Debug()         
      {
        __iar_Unlocksyslock_Debug();
      }
    private:
      __iar_Lockit_Debug(const __iar_Lockit_Debug&);            
      __iar_Lockit_Debug& operator=(const __iar_Lockit_Debug&); 
    };

    enum _Uninitialized
    { 
      _Noinit
    };
  }  





 


 
  typedef signed char          int8_t;
  typedef unsigned char        uint8_t;

  typedef signed short int         int16_t;
  typedef unsigned short int       uint16_t;

  typedef signed int         int32_t;
  typedef unsigned int       uint32_t;

  typedef signed long long int         int64_t;
  typedef unsigned long long int       uint64_t;


 
typedef signed char      int_least8_t;
typedef unsigned char    uint_least8_t;

typedef signed short int     int_least16_t;
typedef unsigned short int   uint_least16_t;

typedef signed int     int_least32_t;
typedef unsigned int   uint_least32_t;

 
  typedef signed long long int   int_least64_t;
  typedef unsigned long long int uint_least64_t;



 
typedef signed int       int_fast8_t;
typedef unsigned int     uint_fast8_t;

typedef signed int      int_fast16_t;
typedef unsigned int    uint_fast16_t;

typedef signed int      int_fast32_t;
typedef unsigned int    uint_fast32_t;

  typedef signed long long int    int_fast64_t;
  typedef unsigned long long int  uint_fast64_t;

 
typedef signed long long int          intmax_t;
typedef unsigned long long int        uintmax_t;


 
typedef signed int          intptr_t;
typedef unsigned int        uintptr_t;

 
typedef int __data_intptr_t; typedef unsigned int __data_uintptr_t;

 






















 











 

namespace mbed {




 
enum bd_error {
    BD_ERROR_OK                 = 0,      
    BD_ERROR_DEVICE_ERROR       = -4001,  
};


 
typedef uint64_t bd_addr_t;


 
typedef uint64_t bd_size_t;



 
class BlockDevice {
public:

    








 
    static BlockDevice *get_default_instance();

    
 
    virtual ~BlockDevice() {};

    


 
    virtual int init() = 0;

    


 
    virtual int deinit() = 0;

    


 
    virtual int sync()
    {
        return 0;
    }

    







 
    virtual int read(void *buffer, bd_addr_t addr, bd_size_t size) = 0;

    









 
    virtual int program(const void *buffer, bd_addr_t addr, bd_size_t size) = 0;

    







 
    virtual int erase(bd_addr_t addr, bd_size_t size)
    {
        return 0;
    }

    









 
    virtual int trim(bd_addr_t addr, bd_size_t size)
    {
        return 0;
    }

    


 
    virtual bd_size_t get_read_size() const = 0;

    



 
    virtual bd_size_t get_program_size() const = 0;

    



 
    virtual bd_size_t get_erase_size() const
    {
        return get_program_size();
    }

    




 
    virtual bd_size_t get_erase_size(bd_addr_t addr) const
    {
        return get_erase_size();
    }

    







 
    virtual int get_erase_value() const
    {
        return -1;
    }

    


 
    virtual bd_size_t size() const = 0;

    




 
    virtual bool is_valid_read(bd_addr_t addr, bd_size_t size) const
    {
        return (
                   addr % get_read_size() == 0 &&
                   size % get_read_size() == 0 &&
                   addr + size <= this->size());
    }

    




 
    virtual bool is_valid_program(bd_addr_t addr, bd_size_t size) const
    {
        return (
                   addr % get_program_size() == 0 &&
                   size % get_program_size() == 0 &&
                   addr + size <= this->size());
    }

    




 
    virtual bool is_valid_erase(bd_addr_t addr, bd_size_t size) const
    {
        return (
                   addr % get_erase_size(addr) == 0 &&
                   (addr + size) % get_erase_size(addr + size - 1) == 0 &&
                   addr + size <= this->size());
    }

    


 
    virtual const char *get_type() const = 0;
};

} 


using mbed::BlockDevice;
using mbed::bd_addr_t;
using mbed::bd_size_t;
using mbed::BD_ERROR_OK;
using mbed::BD_ERROR_DEVICE_ERROR;


 

namespace mbed {



 
class ProfilingBlockDevice : public BlockDevice {
public:
    


 
    ProfilingBlockDevice(BlockDevice *bd);

    
 
    virtual ~ProfilingBlockDevice() {};

    



 
    virtual int init();

    



 
    virtual int deinit();

    


 
    virtual int sync();

    





 
    virtual int read(void *buffer, bd_addr_t addr, bd_size_t size);

    







 
    virtual int program(const void *buffer, bd_addr_t addr, bd_size_t size);

    







 
    virtual int erase(bd_addr_t addr, bd_size_t size);

    


 
    virtual bd_size_t get_read_size() const;

    



 
    virtual bd_size_t get_program_size() const;

    



 
    virtual bd_size_t get_erase_size() const;

    




 
    virtual bd_size_t get_erase_size(bd_addr_t addr) const;

    







 
    virtual int get_erase_value() const;

    


 
    virtual bd_size_t size() const;

    
 
    void reset();

    


 
    bd_size_t get_read_count() const;

    


 
    bd_size_t get_program_count() const;

    


 
    bd_size_t get_erase_count() const;

    


 
    virtual const char *get_type() const;

private:
    BlockDevice *_bd;
    bd_size_t _read_count;
    bd_size_t _program_count;
    bd_size_t _erase_count;
};

} 


using mbed::ProfilingBlockDevice;


 
 
 

  #pragma system_include

 
 

 

  #pragma system_include














 


 
 


  #pragma system_include

 
 

 

  #pragma system_include














 



 
  typedef _Sizet size_t;

typedef unsigned int __data_size_t;



 


 
  typedef   signed int ptrdiff_t;


  typedef decltype(nullptr) nullptr_t;

    typedef union
    {
      long long _ll;
      long double _ld;
      void *_vp;
    } _Max_align_t;
    typedef _Max_align_t max_align_t;






 

namespace mbed {

ProfilingBlockDevice::ProfilingBlockDevice(BlockDevice *bd)
    : _bd(bd)
    , _read_count(0)
    , _program_count(0)
    , _erase_count(0)
{
}

int ProfilingBlockDevice::init()
{
    return _bd->init();
}

int ProfilingBlockDevice::deinit()
{
    return _bd->deinit();
}

int ProfilingBlockDevice::sync()
{
    return _bd->sync();
}

int ProfilingBlockDevice::read(void *b, bd_addr_t addr, bd_size_t size)
{
    int err = _bd->read(b, addr, size);
    if (!err) {
        _read_count += size;
    }
    return err;
}

int ProfilingBlockDevice::program(const void *b, bd_addr_t addr, bd_size_t size)
{
    int err = _bd->program(b, addr, size);
    if (!err) {
        _program_count += size;
    }
    return err;
}

int ProfilingBlockDevice::erase(bd_addr_t addr, bd_size_t size)
{
    int err = _bd->erase(addr, size);
    if (!err) {
        _erase_count += size;
    }
    return err;
}

bd_size_t ProfilingBlockDevice::get_read_size() const
{
    return _bd->get_read_size();
}

bd_size_t ProfilingBlockDevice::get_program_size() const
{
    return _bd->get_program_size();
}

bd_size_t ProfilingBlockDevice::get_erase_size() const
{
    return _bd->get_erase_size();
}

bd_size_t ProfilingBlockDevice::get_erase_size(bd_addr_t addr) const
{
    return _bd->get_erase_size(addr);
}

int ProfilingBlockDevice::get_erase_value() const
{
    return _bd->get_erase_value();
}

bd_size_t ProfilingBlockDevice::size() const
{
    return _bd->size();
}

void ProfilingBlockDevice::reset()
{
    _read_count = 0;
    _program_count = 0;
    _erase_count = 0;
}

bd_size_t ProfilingBlockDevice::get_read_count() const
{
    return _read_count;
}

bd_size_t ProfilingBlockDevice::get_program_count() const
{
    return _program_count;
}

bd_size_t ProfilingBlockDevice::get_erase_count() const
{
    return _erase_count;
}

const char *ProfilingBlockDevice::get_type() const
{
    if (_bd != 0) {
        return _bd->get_type();
    }

    return 0;
}

} 
