###############################################################################
#
# IAR ELF Linker V8.50.4.261/W32 for ARM                  17/Jun/2025  11:12:57
# Copyright 2007-2020 IAR Systems AB.
#
#    Output file  =
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\main_board.elf
#    Map file     =
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\list\main_board.map
#    Command line =
#        -f C:\Users\<USER>\AppData\Local\Temp\EWBA99.tmp
#        (G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\AnalogIn.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\analogin_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\AnalogOut.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\analogout_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\ATCmdParser.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\BufferedBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\BufferedSerial.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\CAN.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\can_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\ChainingBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\CriticalSectionLock.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\CThunkBase.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\DeepSleepLock.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\DigitalIn.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\DigitalInOut.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\DigitalOut.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\ExhaustibleBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\FileBase.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\FileHandle.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\FilePath.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\FileSystemHandle.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\flash_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\FlashIAP.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\FlashIAPBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\FlashSimBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_adc.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_can.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_cau.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_cau_aes.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_cau_des.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_cau_tdes.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_cmp.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_cpdm.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_crc.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_ctc.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_dac.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_dbg.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_dci.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_dma.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_edout.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_efuse.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_enet.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_exmc.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_exti.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_fac.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_fmc.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_fwdgt.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_gpio.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_hau.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_hau_sha_md5.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_hpdf.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_hwsem.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_i2c.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_ipa.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_lpdts.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_mdio.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_mdma.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_misc.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_ospi.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_ospim.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_pmu.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_rameccmu.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_rcu.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_rspdif.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_rtc.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_rtdec.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_sai.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_sdio.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_spi.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_syscfg.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_timer.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_tli.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_tmu.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_trigsel.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_trng.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_usart.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_vref.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gd32h7xx_wwdgt.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gpio_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\gpio_irq_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\HeapBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\I2C.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\i2c_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\input_capture_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\InterruptIn.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\LocalFileSystem.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\LowPowerTickerWrapper.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\main.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_alloc_wrappers.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_application.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_assert.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_atomic_impl.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_board.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_compat.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_critical.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_critical_section_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_error.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_error_hist.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_flash_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_gpio.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_gpio_irq.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_interface.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_itm_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_lp_ticker_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_lp_ticker_wrapper.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_mem_trace.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_mktime.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_mpu_mgmt.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_os_timer.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_overrides.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_pinmap_common.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_pinmap_default.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_poll.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_power_mgmt.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_retarget.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_rtc_time.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_sdk_boot.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_semihost_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_stats.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_thread.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_ticker_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_us_ticker_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\mbed_wait_api_no_rtos.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\MBRBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\newlib_nano_malloc_workaround.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\ObservingBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\PeripheralPins.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\pinmap.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\port_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\ProfilingBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\PwmOut.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\ReadOnlyBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\rtc_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\serial_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\SerialBase.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\SFDP.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\sleep.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\SlicingBlockDevice.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\SPI.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\spi_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\startup_gd32h7xx.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\static_pinmap.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\Stream.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\system_gd32h7xx.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\SysTimer.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\TimerEvent.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\trng_api.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\us_ticker.o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj\watchdog_api.o
#        --no_out_extension -o
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\main_board.elf
#        --map
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\list\main_board.map
#        --config
#        G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\..\..\..\mbed-os\targets\TARGET_GigaDevice\TARGET_GD32F4XX\device\TOOLCHAIN_IAR\GD32F450xK-0x8000000-0x807FFFF.icf
#        --semihosting --entry __iar_program_start --vfe --enable_stack_usage
#        --text_out locale)
#
###############################################################################

*******************************************************************************
*** MESSAGES
***

Warning[Ls016]: [stack usage analysis] the program contains at least one
          indirect call. Example: from "handle_error" in mbed_error.o [1]. A
          complete list of such functions is in the map file.
Warning[Ls017]: [stack usage analysis] the program contains at least one
          instance of recursion for which stack usage analysis has not been
          able to calculate a maximum stack depth. One function involved is
          "mbed_assert_internal". A complete list of all recursion nests is in
          the map file.


*******************************************************************************
*** RUNTIME MODEL ATTRIBUTES
***

CppFlavor                     = *
__CPP_Exceptions              = Disabled
__CPP_Language                = C++14
__Heap_Handler                = DLMalloc
__SystemLibrary               = DLib
__dlib_dynamic_initialization = normal
__dlib_version                = 6


*******************************************************************************
*** HEAP SELECTION
***

The advanced heap was selected because the application calls memory
allocation functions outside of system library functions, and there
are calls to deallocation functions in the application.


*******************************************************************************
*** PLACEMENT SUMMARY
***

"A0":  place at address 0x800'0000 { ro section .intvec };
"P1":  place in [from 0x800'0000 to 0x807'ffff] { ro };
define block CSTACK with size = 16K, alignment = 8 { };
define block HEAP with size = 16K, alignment = 8 { };
"P2":  place in [from 0x2400'0000 to 0x2407'ffff] {
          rw, block CSTACK, block HEAP };
initialize by copy { rw };

No sections matched the following patterns:

  section .sram  in "P3"


  Section                Kind         Address    Size  Object
  -------                ----         -------    ----  ------
"A0":                                           0x3a4
  .intvec                ro code   0x800'0000   0x3a4  startup_gd32h7xx.o [1]
                                 - 0x800'03a4   0x3a4

"P1":                                          0x6d0c
  .text                  ro code   0x800'03a4   0xfc2  xprintffull_nomb.o [2]
  .text                  ro code   0x800'1366    0x3a  zero_init3.o [5]
  .text                  ro code   0x800'13a0    0x16  strchr.o [5]
  .text                  ro code   0x800'13b6    0x2e  copy_init3.o [5]
  .text                  ro code   0x800'13e4    0x40  xfail_s.o [2]
  .text                  ro code   0x800'1424    0x36  strlen.o [5]
  .text                  ro code   0x800'145a    0x1a  xsnprout.o [2]
  .text                  ro code   0x800'1474    0x58  memchr.o [5]
  .text                  ro code   0x800'14cc    0xa6  ABImemcpy.o [5]
  .rodata                const     0x800'1572     0x2  xlocale_c.o [2]
  .text                  ro code   0x800'1574    0x70  frexp.o [4]
  .text                  ro code   0x800'15e4    0x2e  DblCmpLe.o [4]
  .text                  ro code   0x800'1612     0x2  main.o [1]
  .text                  ro code   0x800'1614    0x2e  DblCmpGe.o [4]
  .rodata                const     0x800'1642     0x1  unwind_debug.o [6]
  .rodata                const     0x800'1643     0x1  xlocale_c.o [2]
  .text                  ro code   0x800'1644   0x104  ldexp.o [4]
  .text                  ro code   0x800'1748     0xe  DblToS32.o [4]
  .text                  ro code   0x800'1756     0xe  S32ToDbl.o [4]
  .text                  ro code   0x800'1764    0x12  DblSub.o [4]
  .text                  ro code   0x800'1776    0x12  DblDiv.o [4]
  .text                  ro code   0x800'1788     0xe  DblToU32.o [4]
  .text                  ro code   0x800'1796     0xe  U32ToDbl.o [4]
  .text                  ro code   0x800'17a4    0x12  DblMul.o [4]
  .text                  ro code   0x800'17b8     0x8  xlocale_c.o [2]
  .text                  ro code   0x800'17c0   0x230  I64DivMod.o [5]
  .text                  ro code   0x800'19f0     0x6  abort.o [2]
  .text                  ro code   0x800'19f8    0x28  errno.o [2]
  .text                  ro code   0x800'1a20     0x2  I64DivZer.o [5]
  .text                  ro code   0x800'1a24    0x14  exit.o [6]
  .text                  ro code   0x800'1a38     0x8  mbed_retarget.o [1]
  .text                  ro code   0x800'1a40   0xa8a  gd32h7xx_can.o [1]
  .text                  ro code   0x800'24cc   0x150  gd32h7xx_rcu.o [1]
  .text                  ro code   0x800'261c   0x56c  can_api.o [1]
  .text                  ro code   0x800'2b88   0x174  mbed_pinmap_common.o [1]
  .text                  ro code   0x800'2cfc    0x34  strrchr.o [2]
  .text                  ro code   0x800'2d30    0x1c  mbed_assert.o [1]
  .text                  ro code   0x800'2d4c   0x250  pinmap.o [1]
  .text                  ro code   0x800'2f9c    0x60  mbed_error.o [1]
  .text                  ro code   0x800'2ffc   0x20c  gpio_api.o [1]
  .text                  ro code   0x800'3208   0x13e  gd32h7xx_gpio.o [1]
  .text                  ro code   0x800'3348    0x1c  mbed_atomic_impl.o [1]
  .text                  ro code   0x800'3364   0x29c  mbed_error.o [1]
  .text                  ro code   0x800'3600    0x66  ABImemset.o [5]
  .text                  ro code   0x800'3668    0x80  mbed_critical.o [1]
  .text                  ro code   0x800'36e8     0x2  mbed_error.o [1]
  .text                  ro code   0x800'36ec    0x84  mbed_board.o [1]
  .text                  ro code   0x800'3770    0x34  mbed_critical_section_api.o [1]
  .text                  ro code   0x800'37a4    0x68  mbed_critical_section_api.o [1]
  .text                  ro code   0x800'380c    0x3c  vsnprint.o [2]
  .text                  ro code   0x800'3848    0x64  mbed_retarget.o [1]
  .text                  ro code   0x800'38ac     0x2  mbed_retarget.o [1]
  .text                  ro code   0x800'38ae     0x2  mbed_board.o [1]
  .text                  ro code   0x800'38b0   0x54c  gd32h7xx_adc.o [1]
  .text                  ro code   0x800'3dfc   0x4a4  gd32h7xx_timer.o [1]
  .text                  ro code   0x800'42a0   0x476  gpio_irq_api.o [1]
  .text                  ro code   0x800'4718   0x23c  gd32h7xx_exti.o [1]
  .text                  ro code   0x800'4954    0xa4  gd32h7xx_syscfg.o [1]
  .text                  ro code   0x800'49f8   0x324  analogin_api.o [1]
  .text                  ro code   0x800'4d1c   0x300  system_gd32h7xx.o [1]
  .text                  ro code   0x800'501c    0xe8  gd32h7xx_misc.o [1]
  .text                  ro code   0x800'5104   0x2b6  main.o [1]
  .text                  ro code   0x800'53ba    0x26  main.o [1]
  .text                  ro code   0x800'53e0    0x58  cxxabi.o [3]
  .text                  ro code   0x800'5438    0x24  main.o [1]
  .text                  ro code   0x800'545c    0x64  AnalogIn.o [1]
  .text                  ro code   0x800'54c0    0x1c  main.o [1]
  .text                  ro code   0x800'54dc   0x1e6  CAN.o [1]
  .text                  ro code   0x800'56c4   0x124  InterruptIn.o [1]
  .text                  ro code   0x800'57e8    0x34  main.o [1]
  .text                  ro code   0x800'581c    0x30  main.o [1]
  .text                  ro code   0x800'584c     0xe  main.o [1]
  .text                  ro code   0x800'585a     0xc  main.o [1]
  .text                  ro code   0x800'5866    0x12  main.o [1]
  .text                  ro code   0x800'5878    0x2c  AnalogOut.o [1]
  .text                  ro code   0x800'58a4    0xe0  us_ticker.o [1]
  .text                  ro code   0x800'5984    0x9a  mbed_gpio.o [1]
  .text                  ro code   0x800'5a20    0xa0  cppinit.o [2]
  .text                  ro code   0x800'5ac0    0x14  main.o [1]
  .text                  ro code   0x800'5ad4    0x14  main.o [1]
  .text                  ro code   0x800'5ae8   0x254  analogout_api.o [1]
  .text                  ro code   0x800'5d3c     0xe  CAN.o [1]
  .text                  ro code   0x800'5d4a    0x1a  InterruptIn.o [1]
  .text                  ro code   0x800'5d64     0x4  mbed_power_mgmt.o [1]
  .text                  ro code   0x800'5d68    0x1c  InterruptIn.o [1]
  .text                  ro code   0x800'5d84    0x14  InterruptIn.o [1]
  .text                  ro code   0x800'5d98    0x70  InterruptIn.o [1]
  .text                  ro code   0x800'5e08     0x2  mbed_rtc_time.o [1]
  .text                  ro code   0x800'5e0a     0x2  mbed_rtc_time.o [1]
  .text                  ro code   0x800'5e0c    0x10  InterruptIn.o [1]
  .text                  ro code   0x800'5e1c     0xc  InterruptIn.o [1]
  .text                  ro code   0x800'5e28    0x10  main.o [1]
  .text                  ro code   0x800'5e38     0x2  main.o [1]
  .text                  ro code   0x800'5e3c    0x44  main.o [1]
  .text                  ro code   0x800'5e80    0x14  ObservingBlockDevice.o [1]
  .text                  ro code   0x800'5e94     0xc  main.o [1]
  .text                  ro code   0x800'5ea0    0x10  main.o [1]
  .text                  ro code   0x800'5eb0     0xc  mbed_rtc_time.o [1]
  .text                  ro code   0x800'5ebc   0x164  gd32h7xx_dac.o [1]
  .text                  ro code   0x800'6020     0x4  ObservingBlockDevice.o [1]
  .text                  ro code   0x800'6024    0x1c  ObservingBlockDevice.o [1]
  .text                  ro code   0x800'6040     0x6  ObservingBlockDevice.o [1]
  .text                  ro code   0x800'6046     0xe  ObservingBlockDevice.o [1]
  .text                  ro code   0x800'6054     0x4  main.o [1]
  .text                  ro code   0x800'6058     0x4  mbed_rtc_time.o [1]
  .text                  ro code   0x800'605c    0x90  mbed_rtc_time.o [1]
  .text                  ro code   0x800'60ec     0x6  ObservingBlockDevice.o [1]
  .text                  ro code   0x800'60f4     0x8  mbed_rtc_time.o [1]
  .text                  ro code   0x800'60fc     0x4  mbed_rtc_time.o [1]
  .text                  ro code   0x800'6100     0x8  mbed_rtc_time.o [1]
  .rodata                const     0x800'6108   0x204  PeripheralPins.o [1]
  .rodata                const     0x800'630c    0x70  mbed_rtc_time.o [1]
  .rodata                const     0x800'637c    0x6c  InterruptIn.o [1]
  .rodata                const     0x800'63e8    0x6c  PeripheralPins.o [1]
  .rodata                const     0x800'6454    0x6c  PeripheralPins.o [1]
  .rodata                const     0x800'64c0    0x60  analogout_api.o [1]
  .rodata                const     0x800'6520    0x5c  analogin_api.o [1]
  .rodata                const     0x800'657c    0x58  can_api.o [1]
  .rodata                const     0x800'65d4    0x58  gpio_api.o [1]
  .rodata                const     0x800'662c    0x58  pinmap.o [1]
  .rodata                const     0x800'6684    0x54  mbed_error.o [1]
  .rodata                const     0x800'66d8    0x50  mbed_error.o [1]
  .rodata                const     0x800'6728    0x4c  gpio_irq_api.o [1]
  .rodata                const     0x800'6774    0x4c  mbed_critical_section_api.o [1]
  .rodata                const     0x800'67c0    0x44  mbed_critical.o [1]
  .rodata                const     0x800'6804    0x40  PeripheralPins.o [1]
  Initializer bytes      const     0x800'6844    0x3c  <for P2-1>
  .rodata                const     0x800'6880    0x34  mbed_critical.o [1]
  .rodata                const     0x800'68b4    0x28  gpio_irq_api.o [1]
  .text                  ro code   0x800'68dc    0x28  data_init.o [5]
  .rodata                const     0x800'6904    0x24  mbed_rtc_time.o [1]
  .rodata                const     0x800'6928    0x24  PeripheralPins.o [1]
  .text                  ro code   0x800'694c    0x24  main.o [1]
  .text                  ro code   0x800'6970     0x2  mbed_compat.o [1]
  .text                  ro code   0x800'6974    0x24  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6998    0x22  fpinit_M.o [4]
  .iar.init_table        const     0x800'69bc    0x28  - Linker created -
  .rodata                const     0x800'69e4    0x20  analogin_api.o [1]
  .rodata                const     0x800'6a04    0x20  mbed_pinmap_common.o [1]
  .rodata                const     0x800'6a24    0x20  mbed_pinmap_common.o [1]
  .text                  ro code   0x800'6a44    0x20  cmain_call_ctors.o [5]
  .text                  ro code   0x800'6a64    0x1e  cmain.o [5]
  .text                  ro code   0x800'6a82     0x4  low_level_init.o [2]
  .rodata                const     0x800'6a88    0x1c  analogin_api.o [1]
  .rodata                const     0x800'6aa4    0x1c  analogout_api.o [1]
  .rodata                const     0x800'6ac0    0x1c  analogout_api.o [1]
  .rodata                const     0x800'6adc    0x1c  mbed_critical_section_api.o [1]
  .rodata                const     0x800'6af8    0x1c  mbed_error.o [1]
  .text                  ro code   0x800'6b14    0x1c  main.o [1]
  .rodata                const     0x800'6b30    0x18  analogin_api.o [1]
  .rodata                const     0x800'6b48    0x18  analogout_api.o [1]
  .rodata                const     0x800'6b60    0x18  CAN.o [1]
  .rodata                const     0x800'6b78    0x18  gpio_api.o [1]
  .rodata                const     0x800'6b90    0x18  main.o [1]
  .rodata                const     0x800'6ba8    0x18  main.o [1]
  .rodata                const     0x800'6bc0    0x18  mbed_error.o [1]
  .rodata                const     0x800'6bd8    0x18  mbed_error.o [1]
  .rodata                const     0x800'6bf0    0x18  mbed_error.o [1]
  .text                  ro code   0x800'6c08    0x18  main.o [1]
  .rodata                const     0x800'6c20    0x14  can_api.o [1]
  .rodata                const     0x800'6c34    0x14  gpio_api.o [1]
  .rodata                const     0x800'6c48    0x14  mbed_error.o [1]
  .rodata                const     0x800'6c5c    0x14  mbed_error.o [1]
  .rodata                const     0x800'6c70    0x14  mbed_error.o [1]
  .rodata                const     0x800'6c84    0x14  mbed_error.o [1]
  .rodata                const     0x800'6c98    0x14  mbed_error.o [1]
  .rodata                const     0x800'6cac    0x14  mbed_pinmap_common.o [1]
  .rodata                const     0x800'6cc0    0x14  mbed_pinmap_common.o [1]
  .rodata                const     0x800'6cd4    0x14  pinmap.o [1]
  .rodata                const     0x800'6ce8    0x10  gd32h7xx_can.o [1]
  .rodata                const     0x800'6cf8    0x10  InterruptIn.o [1]
  .rodata                const     0x800'6d08    0x10  mbed_error.o [1]
  .rodata                const     0x800'6d18    0x10  mbed_error.o [1]
  .rodata                const     0x800'6d28    0x10  mbed_error.o [1]
  .rodata                const     0x800'6d38    0x10  mbed_error.o [1]
  .rodata                const     0x800'6d48    0x10  PeripheralPins.o [1]
  .rodata                const     0x800'6d58    0x10  PeripheralPins.o [1]
  .text                  ro code   0x800'6d68    0x10  main.o [1]
  .text                  ro code   0x800'6d78     0x2  main.o [1]
  .text                  ro code   0x800'6d7a    0x10  main.o [1]
  .text                  ro code   0x800'6d8a    0x10  main.o [1]
  .text                  ro code   0x800'6d9a     0xe  main.o [1]
  .text                  ro code   0x800'6da8     0x2  mbed_compat.o [1]
  .text                  ro code   0x800'6daa     0xe  main.o [1]
  .text                  ro code   0x800'6db8     0xe  main.o [1]
  .text                  ro code   0x800'6dc6     0xe  main.o [1]
  .rodata                const     0x800'6dd4     0xc  InterruptIn.o [1]
  .rodata                const     0x800'6de0     0xc  main.o [1]
  .rodata                const     0x800'6dec     0xc  PeripheralPins.o [1]
  .text                  ro code   0x800'6df8     0xc  cstartup_M.o [5]
  .rodata                const     0x800'6e04     0x8  mbed_board.o [1]
  .rodata                const     0x800'6e0c     0x8  mbed_error.o [1]
  .rodata                const     0x800'6e14     0x8  PeripheralPins.o [1]
  .rodata                const     0x800'6e1c     0x8  xprintffull_nomb.o [2]
  .text                  ro code   0x800'6e24     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e28     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e2c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e30     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e34     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e38     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e3c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e40     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e44     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e48     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e4c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e50     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e54     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e58     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e5c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e60     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e64     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e68     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e6c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e70     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e74     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e78     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e7c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e80     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e84     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e88     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e8c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e90     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e94     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e98     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6e9c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ea0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ea4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ea8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6eac     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6eb0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6eb4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6eb8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ebc     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ec0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ec4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ec8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ecc     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ed0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ed4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ed8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6edc     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ee0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ee4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ee8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6eec     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ef0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ef4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ef8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6efc     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f00     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f04     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f08     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f0c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f10     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f14     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f18     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f1c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f20     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f24     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f28     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f2c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f30     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f34     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f38     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f3c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f40     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f44     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f48     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f4c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f50     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f54     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f58     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f5c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f60     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f64     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f68     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f6c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f70     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f74     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f78     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f7c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f80     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f84     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f88     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f8c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f90     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f94     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f98     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6f9c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fa0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fa4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fa8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fac     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fb0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fb4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fb8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fbc     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fc0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fc4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fc8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fcc     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fd0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fd4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fd8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fdc     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fe0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fe4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fe8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6fec     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ff0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ff4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ff8     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'6ffc     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7000     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7004     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7008     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'700c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7010     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7014     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7018     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'701c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7020     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7024     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7028     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'702c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7030     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7034     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7038     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'703c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7040     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7044     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7048     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'704c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7050     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7054     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7058     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'705c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7060     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7064     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7068     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'706c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7070     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7074     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7078     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'707c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7080     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7084     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7088     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'708c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7090     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7094     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'7098     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'709c     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'70a0     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'70a4     0x4  startup_gd32h7xx.o [1]
  .text                  ro code   0x800'70a8     0x4  cexit.o [5]
  SHT$$PREINIT_ARRAY               0x800'70ac     0x0  <Block>
  SHT$$INIT_ARRAY                  0x800'70ac     0x4  <Block>
    .init_array          const     0x800'70ac     0x4  main.o [1]
  __iar_tls$$INIT_ARRAY            0x800'70b0     0x0  <Block>
  .text                  ro code   0x800'70b0     0x0  cstart_call_dtors.o [5]
  .rodata                const     0x800'70b0     0x0  zero_init3.o [5]
  .rodata                const     0x800'70b0     0x0  copy_init3.o [5]
                                 - 0x800'70b0  0x6d0c

"P2", part 1 of 3:                               0x3c
  P2-1                            0x2400'0000    0x3c  <Init block>
    .data                inited   0x2400'0000     0x4  system_gd32h7xx.o [1]
    .data                inited   0x2400'0004    0x38  xlocale_c.o [2]
                                - 0x2400'003c    0x3c

"P2", part 2 of 3:                              0x2d4
  .bss                   zero     0x2400'003c    0xc0  gpio_irq_api.o [1]
  .bss                   zero     0x2400'00fc    0xa0  main.o [1]
  .bss                   zero     0x2400'019c    0x40  main.o [1]
  .bss                   zero     0x2400'01dc    0x34  mbed_error.o [1]
  .bss                   zero     0x2400'0210    0x34  mbed_error.o [1]
  .bss                   zero     0x2400'0244    0x28  gpio_irq_api.o [1]
  .bss                   zero     0x2400'026c    0x14  main.o [1]
  .bss                   zero     0x2400'0280    0x10  main.o [1]
  .bss                   zero     0x2400'0290     0xc  can_api.o [1]
  .bss                   zero     0x2400'029c     0xc  main.o [1]
  .bss                   zero     0x2400'02a8     0xc  main.o [1]
  .bss                   zero     0x2400'02b4     0xc  main.o [1]
  .bss                   zero     0x2400'02c0     0xc  main.o [1]
  .bss                   zero     0x2400'02cc     0xc  main.o [1]
  .bss                   zero     0x2400'02d8     0x8  AnalogIn.o [1]
  .bss                   zero     0x2400'02e0     0x4  can_api.o [1]
  .bss                   zero     0x2400'02e4     0x4  gpio_irq_api.o [1]
  .bss                   zero     0x2400'02e8     0x4  mbed_critical.o [1]
  .bss                   zero     0x2400'02ec     0x4  mbed_error.o [1]
  .bss                   zero     0x2400'02f0     0x4  mbed_error.o [1]
  .bss                   zero     0x2400'02f4     0x4  us_ticker.o [1]
  .bss                   zero     0x2400'02f8     0x4  cppinit.o [2]
  .bss                   zero     0x2400'02fc     0x4  cppinit.o [2]
  .bss                   zero     0x2400'0300     0x4  xfail_s.o [2]
  .tbss                  zero     0x2400'0304     0x4  errno.o [2]
  .bss                   zero     0x2400'0308     0x1  analogin_api.o [1]
  .bss                   zero     0x2400'0309     0x1  mbed_critical_section_api.o [1]
  .bss                   zero     0x2400'030a     0x1  mbed_critical_section_api.o [1]
  .bss                   zero     0x2400'030b     0x1  mbed_error.o [1]
  .bss                   zero     0x2400'030c     0x1  us_ticker.o [1]
                                - 0x2400'030d   0x2d1

"P2", part 3 of 3:                             0x41c0
  .iar.dynexit                    0x2400'0310   0x1bc  <Block>
    .iar.dynexit         uninit   0x2400'0310     0xc  cppinit.o [2]
    .iar.dynexit         uninit   0x2400'031c   0x1b0  <Block tail>
  CSTACK                          0x2400'04d0  0x4000  <Block>
    CSTACK               uninit   0x2400'04d0  0x4000  <Block tail>
                                - 0x2400'44d0  0x41c0

Unused ranges:

         From           To      Size
         ----           --      ----
   0x800'70b0   0x807'ffff  0x7'8f50
  0x2400'44d0  0x2407'ffff  0x7'bb30


*******************************************************************************
*** INIT TABLE
***

          Address      Size
          -------      ----
Zero (__iar_zero_init3)
    1 destination range, total size 0x2d1:
          0x2400'003c  0x2d1

Copy (__iar_copy_init3)
    1 source range, total size 0x3c:
           0x800'6844   0x3c
    1 destination range, total size 0x3c:
          0x2400'0000   0x3c

Extra (__iar_cstart_call_ctors)


*******************************************************************************
*** STACK USAGE
***

  Call Graph Root Category  Max Use  Total Use
  ------------------------  -------  ---------
  Program entry                812        812
  Uncalled function            660      1'580


Program entry
  "__iar_program_start": 0x800'6df9

  Maximum call chain                            *?* 812  bytes
    (** call graph contains recursive functions (example: "mbed_assert_internal")
                            indirect calls (example: "handle_error" in mbed_error.o [1]) **)

    "__iar_program_start"                             0
    "__cmain"                                         0
    "__iar_data_init3"                                8
    "__iar_cstart_call_ctors"                         8
    "__call_ctors"                                   16
    "__sti__routine" in main.o [1]                    8
    "mbed::CAN::CAN(PinName, PinName, int)"          24
    "can_init_freq"                                  24
    "can_init"                                      104
    "pinmap_pinout"                                  16
    "pin_function"                                   48
    "gpio_clock_enable"                              16
    "error"                                          32
    "print_error_report" in mbed_error.o [1]         32
    "mbed_error_printf"                              24
    "mbed_error_vprintf"                            160
    "vsnprintf"                                      24
    "_PrintfFullNoMb"                               232
    "_LitobFullNoMb" in xprintffull_nomb.o [2]       36
    "__aeabi_uldivmod"                                0
    "__aeabi_ldiv0"                                   0

Uncalled function
  "mbed::CAN::_irq_handler(unsigned int, CanIrqType)": 0x800'567d

  Maximum call chain                            *?* 216  bytes
    (** call graph contains recursive functions (example: "mbed_assert_internal")
                            indirect calls (example: "mbed::Callback<void ()>::call() const") **)

    "mbed::CAN::_irq_handler(unsigned int, CanIrqType)"
                                                     16
    "mbed::Callback<void ()>::call() const"          24
    "mbed_assert_internal"                            0

Uncalled function
  "CAN0_Message_IRQHandler": 0x800'2a4d

  Maximum call chain                            *?* 16  bytes
    (** call graph contains indirect calls (example: "CAN0_Message_IRQHandler") **)

    "CAN0_Message_IRQHandler"                       16

Uncalled function
  "CAN1_Message_IRQHandler": 0x800'2aa7

  Maximum call chain                            *?* 16  bytes
    (** call graph contains indirect calls (example: "CAN1_Message_IRQHandler") **)

    "CAN1_Message_IRQHandler"                       16

Uncalled function
  "CAN2_Message_IRQHandler": 0x800'2b01

  Maximum call chain                            *?* 16  bytes
    (** call graph contains indirect calls (example: "CAN2_Message_IRQHandler") **)

    "CAN2_Message_IRQHandler"                       16

Uncalled function
  "EXTI0_IRQHandler": 0x800'4365

  Maximum call chain                            *?* 24  bytes
    (** call graph contains indirect calls (example: "exti_handle_interrupt" in gpio_irq_api.o [1]) **)

    "EXTI0_IRQHandler"                               8
    "exti_handle_interrupt" in gpio_irq_api.o [1]
                                                    16

Uncalled function
  "EXTI10_15_IRQHandler": 0x800'43f3

  Maximum call chain                            *?* 24  bytes
    (** call graph contains indirect calls (example: "exti_handle_interrupt" in gpio_irq_api.o [1]) **)

    "EXTI10_15_IRQHandler"                           8
    "exti_handle_interrupt" in gpio_irq_api.o [1]
                                                    16

Uncalled function
  "EXTI1_IRQHandler": 0x800'436f

  Maximum call chain                            *?* 24  bytes
    (** call graph contains indirect calls (example: "exti_handle_interrupt" in gpio_irq_api.o [1]) **)

    "EXTI1_IRQHandler"                               8
    "exti_handle_interrupt" in gpio_irq_api.o [1]
                                                    16

Uncalled function
  "EXTI2_IRQHandler": 0x800'4379

  Maximum call chain                            *?* 24  bytes
    (** call graph contains indirect calls (example: "exti_handle_interrupt" in gpio_irq_api.o [1]) **)

    "EXTI2_IRQHandler"                               8
    "exti_handle_interrupt" in gpio_irq_api.o [1]
                                                    16

Uncalled function
  "EXTI3_IRQHandler": 0x800'4383

  Maximum call chain                            *?* 24  bytes
    (** call graph contains indirect calls (example: "exti_handle_interrupt" in gpio_irq_api.o [1]) **)

    "EXTI3_IRQHandler"                               8
    "exti_handle_interrupt" in gpio_irq_api.o [1]
                                                    16

Uncalled function
  "EXTI4_IRQHandler": 0x800'438d

  Maximum call chain                            *?* 24  bytes
    (** call graph contains indirect calls (example: "exti_handle_interrupt" in gpio_irq_api.o [1]) **)

    "EXTI4_IRQHandler"                               8
    "exti_handle_interrupt" in gpio_irq_api.o [1]
                                                    16

Uncalled function
  "EXTI5_9_IRQHandler": 0x800'4397

  Maximum call chain                            *?* 24  bytes
    (** call graph contains indirect calls (example: "exti_handle_interrupt" in gpio_irq_api.o [1]) **)

    "EXTI5_9_IRQHandler"                             8
    "exti_handle_interrupt" in gpio_irq_api.o [1]
                                                    16

Uncalled function
  "mbed::InterruptIn::_irq_handler(unsigned int, gpio_irq_event)": 0x800'57a5

  Maximum call chain                            *?* 224  bytes
    (** call graph contains recursive functions (example: "mbed_assert_internal")
                            indirect calls (example: "mbed::Callback<void ()>::call() const") **)

    "mbed::InterruptIn::_irq_handler(unsigned int, gpio_irq_event)"
                                                     16
    "mbed::Callback<void ()>::operator ()() const"
                                                      8
    "mbed::Callback<void ()>::call() const"          24
    "mbed_assert_internal"                            0

Uncalled function
  "[local to main_cpp]::canProcess()" in main.o [1]: 0x800'5335

  Maximum call chain                              148  bytes

    "[local to main_cpp]::canProcess()" in main.o [1]
                                                   24
    "mbed::CAN::read(mbed::CANMessage &, int)"     24
    "can_read"                                     40
    "can_mailbox_config"                           40
    "can_data_to_big_endian_swap" in gd32h7xx_can.o [1]
                                                   20

Uncalled function
  "[local to main_cpp]::gpioInterruptCallBack()" in main.o [1]: 0x800'53b5

  Maximum call chain                                0  bytes

    "[local to main_cpp]::gpioInterruptCallBack()" in main.o [1]
                                                    0

Uncalled function
  "mbed::detail::CallbackBase::trivial_target_copy(mbed::detail::CallbackBase::Store &, mbed::detail::CallbackBase::Store const &)": 0x800'6b15

  Maximum call chain                               32  bytes

    "mbed::detail::CallbackBase::trivial_target_copy(mbed::detail::CallbackBase::Store &, mbed::detail::CallbackBase::Store const &)"
                                                   24
    "__aeabi_memcpy"                                0
    "__aeabi_memcpy4"                               8

Uncalled function
  "mbed::detail::CallbackBase::trivial_target_dtor(mbed::detail::CallbackBase::Store &)": 0x800'1613

  Maximum call chain                                0  bytes

    "mbed::detail::CallbackBase::trivial_target_dtor(mbed::detail::CallbackBase::Store &)"
                                                    0

Uncalled function
  "void mbed::Callback<void ()>::target_call<void (*)()>(mbed::detail::CallbackBase const *)": 0x800'6dc7

  Maximum call chain                            *?* 40  bytes
    (** call graph contains indirect calls (example: "std::enable_if<((!std::is_member_pointer<std::decay<void (*&)()>::type>::value)||(std::is_member_object_pointer<std::decay<void (*&)()>::type>::value&&(sizeof...(param#2)!=(int)1))), decltype((std::forward<void (*&)()>(param#1)(std::forward<>(param#2)...)))>::type mstd::impl::INVOKE<void (*&)(), >(void (*&)() &&,  &&...)") **)

    "void mbed::Callback<void ()>::target_call<void (*)()>(mbed::detail::CallbackBase const *)"
                                                    16
    "void mbed::detail::invoke_r<void, void (*&)(), , (int)0>(void (*&)() &&,  &&...)"
                                                     8
    "mstd::invoke_result<void (*&)(), ...>::type mstd::invoke<void (*&)(), >(void (*&)() &&,  &&...)"
                                                     8
    "std::enable_if<((!std::is_member_pointer<std::decay<void (*&)()>::type>::value)||(std::is_member_object_pointer<std::decay<void (*&)()>::type>::value&&(sizeof...(param#2)!=(int)1))), decltype((std::forward<void (*&)()>(param#1)(std::forward<>(param#2)...)))>::type mstd::impl::INVOKE<void (*&)(), >(void (*&)() &&,  &&...)"
                                                     8

Uncalled function
  "SystemInit": 0x800'4d1d

  Maximum call chain                               36  bytes

    "SystemInit"                                   16
    "system_clock_config" in system_gd32h7xx.o [1]
                                                    8
    "system_clock_600m_hxtal" in system_gd32h7xx.o [1]
                                                   12

Uncalled function
  "TIMER6_IRQHandler": 0x800'58a5

  Maximum call chain                                8  bytes

    "TIMER6_IRQHandler"                             8
    "timer_interrupt_flag_clear"                    0

Uncalled function
  "__call_dtors": 0x800'5a6f

  Maximum call chain                            *?* 660  bytes
    (** call graph contains recursive functions (example: "mbed_assert_internal")
                            indirect calls (example: "handle_error" in mbed_error.o [1]) **)

    "__call_dtors"                                    8
    "__call_dtors0"                                  24
    "mbed::AnalogOut::~AnalogOut()"                   8
    "analogout_free"                                 16
    "pin_function"                                   48
    "gpio_clock_enable"                              16
    "error"                                          32
    "print_error_report" in mbed_error.o [1]         32
    "mbed_error_printf"                              24
    "mbed_error_vprintf"                            160
    "vsnprintf"                                      24
    "_PrintfFullNoMb"                               232
    "_LitobFullNoMb" in xprintffull_nomb.o [2]       36
    "__aeabi_uldivmod"                                0
    "__aeabi_ldiv0"                                   0

The following functions make up recursion nest 0, which has no maximum recursion depth specified:

  "core_util_critical_section_enter": 0x800'3669
  "core_util_critical_section_exit": 0x800'36bd
  "hal_critical_section_exit": 0x800'37a5
  "handle_error" in mbed_error.o [1]: 0x800'3389
  "mbed_assert_internal": 0x800'2d31
  "mbed_error": 0x800'2f9d

The following functions perform unknown indirect calls:

  "CAN0_Message_IRQHandler": 0x800'2a4d
  "CAN1_Message_IRQHandler": 0x800'2aa7
  "CAN2_Message_IRQHandler": 0x800'2b01
  "exti_handle_interrupt" in gpio_irq_api.o [1]: 0x800'431f
  "handle_error" in mbed_error.o [1]: 0x800'3389
  "mbed::Callback<void ()>::call() const": 0x800'5d99
  "mbed::detail::CallbackBase::copy(const mbed::detail::CallbackBase&)": 0x800'6025
  "mbed::detail::CallbackBase::destroy()": 0x800'5e81
  "std::enable_if<((!std::is_member_pointer<std::decay<void (*&)()>::type>::value)||(std::is_member_object_pointer<std::decay<void (*&)()>::type>::value&&(sizeof...(param#2)!=(int)1))), decltype((std::forward<void (*&)()>(param#1)(std::forward<>(param#2)...)))>::type mstd::impl::INVOKE<void (*&)(), >(void (*&)() &&,  &&...)": 0x800'6d69


*******************************************************************************
*** MODULE SUMMARY
***

    Module                       ro code  ro data  rw data
    ------                       -------  -------  -------
command line/config:
    ------------------------------------------------------
    Total:

G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj: [1]
    AnalogIn.o                       100                 8
    AnalogOut.o                       44
    CAN.o                            500       24
    InterruptIn.o                    506      136
    ObservingBlockDevice.o            78
    PeripheralPins.o                          884
    analogin_api.o                   804      176        1
    analogout_api.o                  596      176
    can_api.o                      1'388      108       16
    gd32h7xx_adc.o                 1'356
    gd32h7xx_can.o                 2'698       16
    gd32h7xx_dac.o                   356
    gd32h7xx_exti.o                  572
    gd32h7xx_gpio.o                  318
    gd32h7xx_misc.o                  232
    gd32h7xx_rcu.o                   336
    gd32h7xx_syscfg.o                164
    gd32h7xx_timer.o               1'188
    gpio_api.o                       524      132
    gpio_irq_api.o                 1'142      116      236
    main.o                         1'294       64      320
    mbed_assert.o                     28
    mbed_atomic_impl.o                28
    mbed_board.o                     134        8
    mbed_compat.o                      4
    mbed_critical.o                  128      120        4
    mbed_critical_section_api.o      156      104        2
    mbed_error.o                     766      436      113
    mbed_gpio.o                      154
    mbed_pinmap_common.o             372      104
    mbed_power_mgmt.o                  4
    mbed_retarget.o                  110
    mbed_rtc_time.o                  184      148
    pinmap.o                         592      108
    startup_gd32h7xx.o             1'612
    system_gd32h7xx.o                768        4        4
    us_ticker.o                      224                 5
    ------------------------------------------------------
    Total:                        19'460    2'864      709

dl7M_tlf.a: [2]
    abort.o                            6
    cppinit.o                        160                20
    errno.o                           40                 4
    low_level_init.o                   4
    strrchr.o                         52
    vsnprint.o                        60
    xfail_s.o                         64                 4
    xlocale_c.o                        8       59       56
    xprintffull_nomb.o             4'034        8
    xsnprout.o                        26
    ------------------------------------------------------
    Total:                         4'454       67       84

dlpp7M_tl_fc.a: [3]
    cxxabi.o                          88
    ------------------------------------------------------
    Total:                            88

m7M_tlv.a: [4]
    DblCmpGe.o                        46
    DblCmpLe.o                        46
    DblDiv.o                          18
    DblMul.o                          18
    DblSub.o                          18
    DblToS32.o                        14
    DblToU32.o                        14
    S32ToDbl.o                        14
    U32ToDbl.o                        14
    fpinit_M.o                        34
    frexp.o                          112
    ldexp.o                          260
    ------------------------------------------------------
    Total:                           608

rt7M_tl.a: [5]
    ABImemcpy.o                      166
    ABImemset.o                      102
    I64DivMod.o                      560
    I64DivZer.o                        2
    cexit.o                            4
    cmain.o                           30
    cmain_call_ctors.o                32
    copy_init3.o                      46
    cstart_call_dtors.o
    cstartup_M.o                      12
    data_init.o                       40
    memchr.o                          88
    strchr.o                          22
    strlen.o                          54
    zero_init3.o                      58
    ------------------------------------------------------
    Total:                         1'216

shb_l.a: [6]
    exit.o                            20
    unwind_debug.o                              1
    ------------------------------------------------------
    Total:                            20        1

    Gaps                              26        4
    Linker created                             40   16'816
----------------------------------------------------------
    Grand Total:                  25'872    2'976   17'609


*******************************************************************************
*** ENTRY LIST
***

Entry                       Address   Size  Type      Object
-----                       -------   ----  ----      ------
.iar.dynexit$$Base      0x2400'0310          --   Gb  - Linker created -
.iar.dynexit$$Limit     0x2400'04cc          --   Gb  - Linker created -
.iar.init_table$$Base    0x800'69bc          --   Gb  - Linker created -
.iar.init_table$$Limit   0x800'69e4          --   Gb  - Linker created -
?main                    0x800'6a65         Code  Gb  cmain.o [5]
CAN0_Message_IRQHandler
                         0x800'2a4d   0x5a  Code  Gb  can_api.o [1]
CAN1_Message_IRQHandler
                         0x800'2aa7   0x5a  Code  Gb  can_api.o [1]
CAN2_Message_IRQHandler
                         0x800'2b01   0x5a  Code  Gb  can_api.o [1]
CSTACK$$Base            0x2400'04d0          --   Gb  - Linker created -
CSTACK$$Limit           0x2400'44d0          --   Gb  - Linker created -
DtorRecArray            0x2400'0310    0xc  Data  Lc  cppinit.o [2]
EXIT_LINE                0x800'6728   0x4c  Data  Lc  gpio_irq_api.o [1]
EXTI0_IRQHandler         0x800'4365    0xa  Code  Gb  gpio_irq_api.o [1]
EXTI10_15_IRQHandler     0x800'43f3   0x6e  Code  Gb  gpio_irq_api.o [1]
EXTI1_IRQHandler         0x800'436f    0xa  Code  Gb  gpio_irq_api.o [1]
EXTI2_IRQHandler         0x800'4379    0xa  Code  Gb  gpio_irq_api.o [1]
EXTI3_IRQHandler         0x800'4383    0xa  Code  Gb  gpio_irq_api.o [1]
EXTI4_IRQHandler         0x800'438d    0xa  Code  Gb  gpio_irq_api.o [1]
EXTI5_9_IRQHandler       0x800'4397   0x5c  Code  Gb  gpio_irq_api.o [1]
GD_GPIO_AF               0x800'6804   0x40  Data  Gb  PeripheralPins.o [1]
GD_GPIO_MODE             0x800'6d48   0x10  Data  Gb  PeripheralPins.o [1]
GD_GPIO_OUTPUT_MODE      0x800'6e14    0x8  Data  Gb  PeripheralPins.o [1]
GD_GPIO_PULL_UP_DOWN     0x800'6dec    0xc  Data  Gb  PeripheralPins.o [1]
GD_GPIO_SPEED            0x800'6d58   0x10  Data  Gb  PeripheralPins.o [1]
PinMap_ADC               0x800'6108  0x204  Data  Gb  PeripheralPins.o [1]
PinMap_CAN_RD            0x800'63e8   0x6c  Data  Gb  PeripheralPins.o [1]
PinMap_CAN_TD            0x800'6454   0x6c  Data  Gb  PeripheralPins.o [1]
PinMap_DAC               0x800'6928   0x24  Data  Gb  PeripheralPins.o [1]
PlatformMutex * core_util_atomic_load<PlatformMutex>(PlatformMutex *const *)
                         0x800'60f5    0x8  Code  Gb  mbed_rtc_time.o [1]
PlatformMutex::lock()    0x800'5e09    0x2  Code  Gb  mbed_rtc_time.o [1]
PlatformMutex::unlock()
                         0x800'5e0b    0x2  Code  Gb  mbed_rtc_time.o [1]
Region$$Table$$Base      0x800'69bc          --   Gb  - Linker created -
Region$$Table$$Limit     0x800'69e4          --   Gb  - Linker created -
SHT$$INIT_ARRAY$$Base    0x800'70ac          --   Gb  - Linker created -
SHT$$INIT_ARRAY$$Limit   0x800'70b0          --   Gb  - Linker created -
SHT$$PREINIT_ARRAY$$Base
                         0x800'70ac          --   Gb  - Linker created -
SHT$$PREINIT_ARRAY$$Limit
                         0x800'70ac          --   Gb  - Linker created -
SingletonPtr<PlatformMutex>::get() const
                         0x800'605d   0x90  Code  Gb  mbed_rtc_time.o [1]
SingletonPtr<PlatformMutex>::get() const::string
                         0x800'6904   0x24  Data  Gb  mbed_rtc_time.o [1]
SingletonPtr<PlatformMutex>::get() const::string (instance 2)
                         0x800'630c   0x70  Data  Gb  mbed_rtc_time.o [1]
SingletonPtr<PlatformMutex>::operator ->() const
                         0x800'5eb1    0xc  Code  Gb  mbed_rtc_time.o [1]
SystemCoreClock         0x2400'0000    0x4  Data  Gb  system_gd32h7xx.o [1]
SystemInit               0x800'4d1d  0x17c  Code  Gb  system_gd32h7xx.o [1]
TIMER6_IRQHandler        0x800'58a5   0x22  Code  Gb  us_ticker.o [1]
Virtual function table for mbed::AnalogIn
                         0x800'6b90   0x18  Data  Gb  main.o [1]
Virtual function table for mbed::AnalogOut
                         0x800'6ba8   0x18  Data  Gb  main.o [1]
Virtual function table for mbed::CAN
                         0x800'6b60   0x18  Data  Gb  CAN.o [1]
Virtual function table for mbed::InterruptIn
                         0x800'6cf8   0x10  Data  Gb  InterruptIn.o [1]
[local to main_cpp]::canProcess()
                         0x800'5335   0x16  Code  Lc  main.o [1]
[local to main_cpp]::gpioInterruptCallBack()
                         0x800'53b5    0x6  Code  Lc  main.o [1]
[local to main_cpp]::init()
                         0x800'52f9    0xc  Code  Lc  main.o [1]
[local to main_cpp]::millis()
                         0x800'5305    0x8  Code  Lc  main.o [1]
[local to main_cpp]::sendCan(mbed::CANMessage)
                         0x800'530d   0x26  Code  Lc  main.o [1]
[local to mbed_rtc_time_cpp]::singleton_lock()
                         0x800'60fd    0x2  Code  Lc  mbed_rtc_time.o [1]
[local to mbed_rtc_time_cpp]::singleton_unlock()
                         0x800'60ff    0x2  Code  Lc  mbed_rtc_time.o [1]
_LitobFullNoMb           0x800'1161  0x10a  Code  Lc  xprintffull_nomb.o [2]
_Locale_lconv           0x2400'0004   0x38  Data  Lc  xlocale_c.o [2]
_PrintfFullNoMb          0x800'03a5  0xd98  Code  Gb  xprintffull_nomb.o [2]
_PutcharFullNoMb         0x800'12d1   0x20  Code  Lc  xprintffull_nomb.o [2]
_PutcharsFullNoMb        0x800'12f1   0x2e  Code  Lc  xprintffull_nomb.o [2]
_SNProut                 0x800'145b   0x1a  Code  Gb  xsnprout.o [2]
__NVIC_ClearPendingIRQ   0x800'42e9   0x20  Code  Lc  gpio_irq_api.o [1]
__NVIC_DisableIRQ        0x800'42c1   0x28  Code  Lc  gpio_irq_api.o [1]
__NVIC_EnableIRQ         0x800'261d   0x20  Code  Lc  can_api.o [1]
__NVIC_EnableIRQ         0x800'42a1   0x20  Code  Lc  gpio_irq_api.o [1]
__NVIC_SetVector         0x800'4309   0x16  Code  Lc  gpio_irq_api.o [1]
__aeabi_atexit           0x800'542f    0xa  Code  Gb  cxxabi.o [3]
__aeabi_cdcmple          0x800'15e5         Code  Gb  DblCmpLe.o [4]
__aeabi_cdrcmple         0x800'1615         Code  Gb  DblCmpGe.o [4]
__aeabi_d2iz             0x800'1749    0xe  Code  Gb  DblToS32.o [4]
__aeabi_d2uiz            0x800'1789    0xe  Code  Gb  DblToU32.o [4]
__aeabi_ddiv             0x800'1777   0x12  Code  Gb  DblDiv.o [4]
__aeabi_dmul             0x800'17a5   0x12  Code  Gb  DblMul.o [4]
__aeabi_dsub             0x800'1765   0x12  Code  Gb  DblSub.o [4]
__aeabi_errno_addr       0x800'1a0b    0xc  Code  Gb  errno.o [2]
__aeabi_i2d              0x800'1757    0xe  Code  Gb  S32ToDbl.o [4]
__aeabi_ldiv0            0x800'1a21         Code  Gb  I64DivZer.o [5]
__aeabi_memcpy           0x800'14cd         Code  Gb  ABImemcpy.o [5]
__aeabi_memcpy4          0x800'14ed         Code  Gb  ABImemcpy.o [5]
__aeabi_memcpy8          0x800'14ed         Code  Gb  ABImemcpy.o [5]
__aeabi_memset           0x800'3601         Code  Gb  ABImemset.o [5]
__aeabi_read_tp          0x800'1a39    0x8  Code  Wk  mbed_retarget.o [1]
__aeabi_ui2d             0x800'1797    0xe  Code  Gb  U32ToDbl.o [4]
__aeabi_uldivmod         0x800'17c1         Code  Gb  I64DivMod.o [5]
__aeabi_vec_ctor_nocookie_nodtor
                         0x800'53e1   0x28  Code  Gb  cxxabi.o [3]
__aeabi_vec_dtor         0x800'5409   0x26  Code  Gb  cxxabi.o [3]
__call_ctors             0x800'5a21   0x18  Code  Gb  cppinit.o [2]
__call_dtors             0x800'5a6f    0xc  Code  Gb  cppinit.o [2]
__call_dtors0            0x800'5a7b   0x36  Code  Gb  cppinit.o [2]
__cexit_call_dtors       0x800'70a9         Code  Gb  cexit.o [5]
__cmain                  0x800'6a65         Code  Gb  cmain.o [5]
__cstart_call_dtors      0x800'70b0         Data  Gb  cstart_call_dtors.o [5]
__cxa_atexit             0x800'5a39   0x36  Code  Gb  cppinit.o [2]
__dso_handle            0x2400'02fc    0x4  Data  Gb  cppinit.o [2]
__exit                   0x800'1a25   0x14  Code  Gb  exit.o [6]
__iar_Errno             0x2400'0304    0x4  Tls   Gb  errno.o [2]
__iar_Fail_s             0x800'13e5   0x1c  Code  Gb  xfail_s.o [2]
__iar_Memchr             0x800'1475         Code  Gb  memchr.o [5]
__iar_Memset             0x800'3601         Code  Gb  ABImemset.o [5]
__iar_Memset_word        0x800'3609         Code  Gb  ABImemset.o [5]
__iar_Strchr             0x800'13a1         Code  Gb  strchr.o [5]
__iar_Strrchr            0x800'2cfd   0x1a  Code  Gb  strrchr.o [2]
__iar_copy_init3         0x800'13b7   0x2e  Code  Gb  copy_init3.o [5]
__iar_cstart_call_ctors
                         0x800'6a45   0x20  Code  Gb  cmain_call_ctors.o [5]
__iar_data_init3         0x800'68dd   0x28  Code  Gb  data_init.o [5]
__iar_debug_exceptions   0x800'1642    0x1  Data  Gb  unwind_debug.o [6]
__iar_errno_set_ERANGE   0x800'19f9    0xc  Code  Gb  errno.o [2]
__iar_frexp              0x800'1581         Code  Gb  frexp.o [4]
__iar_frexpl             0x800'1581         Code  Gb  frexp.o [4]
__iar_init_vfp           0x800'6999         Code  Gb  fpinit_M.o [4]
__iar_ldexp64            0x800'1659         Code  Gb  ldexp.o [4]
__iar_program_start      0x800'6df9         Code  Gb  cstartup_M.o [5]
__iar_scalbln64          0x800'1659         Code  Gb  ldexp.o [4]
__iar_scalbn64           0x800'1659         Code  Gb  ldexp.o [4]
__iar_softfp___iar_frexp64
                         0x800'1575         Code  Gb  frexp.o [4]
__iar_softfp___iar_ldexp64
                         0x800'1645         Code  Gb  ldexp.o [4]
__iar_softfp___iar_scalbln64
                         0x800'1645         Code  Gb  ldexp.o [4]
__iar_softfp___iar_scalbn64
                         0x800'1645         Code  Gb  ldexp.o [4]
__iar_softfp_frexp       0x800'1575         Code  Gb  frexp.o [4]
__iar_softfp_frexpl      0x800'1575         Code  Gb  frexp.o [4]
__iar_softfp_ldexp       0x800'1645         Code  Gb  ldexp.o [4]
__iar_softfp_ldexpl      0x800'1645         Code  Gb  ldexp.o [4]
__iar_softfp_scalbln     0x800'1645         Code  Gb  ldexp.o [4]
__iar_softfp_scalblnl    0x800'1645         Code  Gb  ldexp.o [4]
__iar_softfp_scalbn      0x800'1645         Code  Gb  ldexp.o [4]
__iar_softfp_scalbnl     0x800'1645         Code  Gb  ldexp.o [4]
__iar_tls$$DATA$$Base           0x0          --   Gb  - Linker created -
__iar_tls$$INIT_ARRAY$$Base
                         0x800'70b0          --   Gb  - Linker created -
__iar_tls$$INIT_ARRAY$$Limit
                         0x800'70b0          --   Gb  - Linker created -
__iar_zero_init3         0x800'1367   0x3a  Code  Gb  zero_init3.o [5]
__low_level_init         0x800'6a83    0x4  Code  Gb  low_level_init.o [2]
__sti__routine           0x800'5105   0x8a  Code  Lc  main.o [1]
__vector_table           0x800'0000         Data  Gb  startup_gd32h7xx.o [1]
_call_main               0x800'6a71         Code  Gb  cmain.o [5]
_delay                   0x800'49f9   0x14  Code  Lc  analogin_api.o [1]
_gpio_init_in            0x800'5985   0x30  Code  Lc  mbed_gpio.o [1]
_gpio_init_out           0x800'59b5   0x3a  Code  Lc  mbed_gpio.o [1]
_main                    0x800'6a7f         Code  Gb  cmain.o [5]
abort                    0x800'19f1    0x6  Code  Gb  abort.o [2]
adc_calibration_enable   0x800'39f1   0x36  Code  Gb  gd32h7xx_adc.o [1]
adc_calibration_mode_config
                         0x800'39a3   0x2e  Code  Gb  gd32h7xx_adc.o [1]
adc_calibration_number   0x800'39d1   0x20  Code  Gb  gd32h7xx_adc.o [1]
adc_channel_length_config
                         0x800'3a89   0x74  Code  Gb  gd32h7xx_adc.o [1]
adc_clock_config         0x800'38b1   0x30  Code  Gb  gd32h7xx_adc.o [1]
adc_data_alignment_config
                         0x800'3959   0x2e  Code  Gb  gd32h7xx_adc.o [1]
adc_enable               0x800'3987   0x1c  Code  Gb  gd32h7xx_adc.o [1]
adc_external_trigger_config
                         0x800'3d3b   0x58  Code  Gb  gd32h7xx_adc.o [1]
adc_flag_clear           0x800'3dd5    0x6  Code  Gb  gd32h7xx_adc.o [1]
adc_flag_get             0x800'3dc3   0x12  Code  Gb  gd32h7xx_adc.o [1]
adc_internal_channel_config
                         0x800'3a69   0x20  Code  Gb  gd32h7xx_adc.o [1]
adc_regular_channel_config
                         0x800'3afd  0x23e  Code  Gb  gd32h7xx_adc.o [1]
adc_regular_data_read    0x800'3dbd    0x6  Code  Gb  gd32h7xx_adc.o [1]
adc_resolution_config    0x800'3a27   0x42  Code  Gb  gd32h7xx_adc.o [1]
adc_software_trigger_enable
                         0x800'3d93   0x2a  Code  Gb  gd32h7xx_adc.o [1]
adc_special_function_config
                         0x800'38e1   0x78  Code  Gb  gd32h7xx_adc.o [1]
adc_sync_mode_config     0x800'3ddb   0x12  Code  Gb  gd32h7xx_adc.o [1]
analog_in               0x2400'0280   0x10  Data  Gb  main.o [1]
analog_out              0x2400'026c   0x14  Data  Gb  main.o [1]
analogin_free            0x800'6971    0x2  Code  Wk  mbed_compat.o [1]
analogin_init            0x800'4a0d  0x29a  Code  Gb  analogin_api.o [1]
analogin_read            0x800'4ca9   0x46  Code  Gb  analogin_api.o [1]
analogout_free           0x800'5c23   0x6c  Code  Gb  analogout_api.o [1]
analogout_init           0x800'5ae9  0x13a  Code  Gb  analogout_api.o [1]
analogout_write          0x800'5cb1   0x5a  Code  Gb  analogout_api.o [1]
analogout_write_u16      0x800'5d0b   0x14  Code  Gb  analogout_api.o [1]
are_interrupts_enabled   0x800'3771   0x10  Code  Lc  mbed_critical_section_api.o [1]
cache_enable()           0x800'5273   0x86  Code  Gb  main.o [1]
can0                    0x2400'00fc   0xa0  Data  Gb  main.o [1]
can_data_to_big_endian_swap
                         0x800'2425   0x7a  Code  Lc  gd32h7xx_can.o [1]
can_data_to_little_endian_swap
                         0x800'23ad   0x78  Code  Lc  gd32h7xx_can.o [1]
can_deinit               0x800'1a41   0x4e  Code  Gb  gd32h7xx_can.o [1]
can_dlc_value_compute    0x800'249f   0x2c  Code  Lc  gd32h7xx_can.o [1]
can_flag_clear           0x800'2223   0x28  Code  Gb  gd32h7xx_can.o [1]
can_flag_get             0x800'2209   0x1a  Code  Gb  gd32h7xx_can.o [1]
can_free                 0x800'2823   0x32  Code  Gb  can_api.o [1]
can_init                 0x800'263d  0x1b4  Code  Gb  can_api.o [1]
can_init_freq            0x800'27f1   0x1a  Code  Gb  can_api.o [1]
can_interrupt_disable    0x800'22c9   0x7a  Code  Gb  gd32h7xx_can.o [1]
can_interrupt_enable     0x800'2255   0x74  Code  Gb  gd32h7xx_can.o [1]
can_interrupt_flag_clear
                         0x800'235d   0x10  Code  Gb  gd32h7xx_can.o [1]
can_interrupt_flag_get   0x800'2343   0x1a  Code  Gb  gd32h7xx_can.o [1]
can_irq_free             0x800'2821    0x2  Code  Gb  can_api.o [1]
can_irq_ids             0x2400'0290    0xc  Data  Lc  can_api.o [1]
can_irq_init             0x800'280b   0x16  Code  Gb  can_api.o [1]
can_irq_set              0x800'297d   0xd0  Code  Gb  can_api.o [1]
can_mailbox_config       0x800'1ffd  0x12a  Code  Gb  gd32h7xx_can.o [1]
can_mailbox_receive_data_read
                         0x800'2135   0xcc  Code  Gb  gd32h7xx_can.o [1]
can_operation_mode_enter
                         0x800'1e4f  0x12a  Code  Gb  gd32h7xx_can.o [1]
can_operation_mode_get   0x800'1f79   0x58  Code  Gb  gd32h7xx_can.o [1]
can_para_init            0x800'1ab9  0x1b4  Code  Gb  gd32h7xx_can.o [1]
can_payload_size_compute
                         0x800'236d   0x34  Code  Lc  gd32h7xx_can.o [1]
can_ram_address_get      0x800'1fd1   0x2c  Code  Gb  gd32h7xx_can.o [1]
can_read                 0x800'2907   0x76  Code  Gb  can_api.o [1]
can_software_reset       0x800'1a8f   0x2a  Code  Gb  gd32h7xx_can.o [1]
can_struct_para_init     0x800'1c6d  0x1e2  Code  Gb  gd32h7xx_can.o [1]
can_write                0x800'2855   0xb2  Code  Gb  can_api.o [1]
core_util_atomic_exchange_u8
                         0x800'3349   0x1a  Code  Gb  mbed_atomic_impl.o [1]
core_util_critical_section_enter
                         0x800'3669   0x54  Code  Gb  mbed_critical.o [1]
core_util_critical_section_exit
                         0x800'36bd   0x1c  Code  Gb  mbed_critical.o [1]
critical_interrupts_enabled
                        0x2400'0309    0x1  Data  Lc  mbed_critical_section_api.o [1]
critical_section_reentrancy_counter
                        0x2400'02e8    0x4  Data  Lc  mbed_critical.o [1]
dac_data_set             0x800'5f4f   0x78  Code  Gb  gd32h7xx_dac.o [1]
dac_deinit               0x800'5ebd   0x20  Code  Gb  gd32h7xx_dac.o [1]
dac_enable               0x800'5edd   0x24  Code  Gb  gd32h7xx_dac.o [1]
dac_mode_config          0x800'5f01   0x4e  Code  Gb  gd32h7xx_dac.o [1]
dac_trigger_disable      0x800'5fc7   0x24  Code  Gb  gd32h7xx_dac.o [1]
dac_wave_mode_config     0x800'5feb   0x32  Code  Gb  gd32h7xx_dac.o [1]
dev_dac_data_set         0x800'5c8f   0x22  Code  Lc  analogout_api.o [1]
dlc_to_databytes         0x800'6ce8   0x10  Data  Lc  gd32h7xx_can.o [1]
error                    0x800'3591   0x70  Code  Wk  mbed_error.o [1]
error_count             0x2400'02ec    0x4  Data  Lc  mbed_error.o [1]
error_hook              0x2400'02f0    0x4  Data  Lc  mbed_error.o [1]
exit                     0x800'389f    0xe  Code  Gb  mbed_retarget.o [1]
exti_handle_interrupt    0x800'431f   0x46  Code  Lc  gpio_irq_api.o [1]
exti_info_array         0x2400'003c   0xc0  Data  Lc  gpio_irq_api.o [1]
exti_init                0x800'4719  0x1ac  Code  Gb  gd32h7xx_exti.o [1]
exti_interrupt_disable   0x800'48e9   0x24  Code  Gb  gd32h7xx_exti.o [1]
exti_interrupt_enable    0x800'48c5   0x24  Code  Gb  gd32h7xx_exti.o [1]
exti_interrupt_flag_clear
                         0x800'492d   0x16  Code  Gb  gd32h7xx_exti.o [1]
exti_interrupt_flag_get
                         0x800'490d   0x20  Code  Gb  gd32h7xx_exti.o [1]
first_error_ctx         0x2400'01dc   0x34  Data  Lc  mbed_error.o [1]
g_pit_initialized       0x2400'030c    0x1  Data  Lc  us_ticker.o [1]
g_systickCounter        0x2400'02f4    0x4  Data  Lc  us_ticker.o [1]
gpio_af_set              0x800'32c9   0x7e  Code  Gb  gd32h7xx_gpio.o [1]
gpio_clock_enable        0x800'2ffd   0xc8  Code  Gb  gpio_api.o [1]
gpio_dir                 0x800'31ab   0x24  Code  Gb  gpio_api.o [1]
gpio_free                0x800'6da9    0x2  Code  Wk  mbed_compat.o [1]
gpio_in_interrupt       0x2400'019c   0x40  Data  Gb  main.o [1]
gpio_init                0x800'3129   0x32  Code  Gb  gpio_api.o [1]
gpio_init_in_ex          0x800'59ef   0x18  Code  Gb  mbed_gpio.o [1]
gpio_init_out_ex         0x800'5a07   0x18  Code  Gb  mbed_gpio.o [1]
gpio_irq_disable         0x800'46ed   0x2a  Code  Gb  gpio_irq_api.o [1]
gpio_irq_enable          0x800'46bd   0x30  Code  Gb  gpio_irq_api.o [1]
gpio_irq_free            0x800'4609   0x24  Code  Gb  gpio_irq_api.o [1]
gpio_irq_init            0x800'4461  0x1a8  Code  Gb  gpio_irq_api.o [1]
gpio_irq_set             0x800'462d   0x4e  Code  Gb  gpio_irq_api.o [1]
gpio_mode                0x800'315b   0x14  Code  Gb  gpio_api.o [1]
gpio_mode_set            0x800'3209   0x62  Code  Gb  gd32h7xx_gpio.o [1]
gpio_out_test           0x2400'02a8    0xc  Data  Gb  main.o [1]
gpio_output_options_set
                         0x800'326b   0x5e  Code  Gb  gd32h7xx_gpio.o [1]
gpio_read                0x800'3197   0x14  Code  Gb  gpio_api.o [1]
gpio_set                 0x800'30c5   0x64  Code  Gb  gpio_api.o [1]
gpio_write               0x800'316f   0x28  Code  Gb  gpio_api.o [1]
hal_critical_section_enter
                         0x800'3781   0x24  Code  Wk  mbed_critical_section_api.o [1]
hal_critical_section_exit
                         0x800'37a5   0x68  Code  Wk  mbed_critical_section_api.o [1]
handle_error             0x800'3389   0xa4  Code  Lc  mbed_error.o [1]
irq_event               0x2400'0244   0x28  Data  Gb  gpio_irq_api.o [1]
irq_handler             0x2400'02e0    0x4  Data  Lc  can_api.o [1]
irq_handler             0x2400'02e4    0x4  Data  Lc  gpio_irq_api.o [1]
key_1                   0x2400'029c    0xc  Data  Gb  main.o [1]
last_error_ctx          0x2400'0210   0x34  Data  Lc  mbed_error.o [1]
ldexp                    0x800'1659         Code  Gb  ldexp.o [4]
ldexpl                   0x800'1659         Code  Gb  ldexp.o [4]
led_1                   0x2400'02cc    0xc  Data  Gb  main.o [1]
led_2                   0x2400'02c0    0xc  Data  Gb  main.o [1]
led_3                   0x2400'02b4    0xc  Data  Gb  main.o [1]
localeconv               0x800'17b9    0x4  Code  Gb  xlocale_c.o [2]
main                     0x800'5191   0xe2  Code  Gb  main.o [1]
mbed::AnalogIn::AnalogIn(PinName, float)
                         0x800'545d   0x34  Code  Gb  AnalogIn.o [1]
mbed::AnalogIn::_mutex  0x2400'02d8    0x8  Data  Gb  AnalogIn.o [1]
mbed::AnalogIn::lock()   0x800'5ac1   0x14  Code  Gb  main.o [1]
mbed::AnalogIn::read()   0x800'5495   0x2c  Code  Gb  AnalogIn.o [1]
mbed::AnalogIn::unlock()
                         0x800'5ad5   0x14  Code  Gb  main.o [1]
mbed::AnalogIn::~AnalogIn()
                         0x800'694d   0x24  Code  Gb  main.o [1]
mbed::AnalogOut::AnalogOut(PinName)
                         0x800'54c1   0x1c  Code  Gb  main.o [1]
mbed::AnalogOut::lock()
                         0x800'6dab    0xe  Code  Gb  main.o [1]
mbed::AnalogOut::unlock()
                         0x800'6db9    0xe  Code  Gb  main.o [1]
mbed::AnalogOut::write(float)
                         0x800'5879   0x2c  Code  Gb  AnalogOut.o [1]
mbed::AnalogOut::~AnalogOut()
                         0x800'6c09   0x18  Code  Gb  main.o [1]
mbed::CAN::CAN(PinName, PinName, int)
                         0x800'54dd   0x4e  Code  Gb  CAN.o [1]
mbed::CAN::_irq_handler(unsigned int, CanIrqType)
                         0x800'567d   0x2a  Code  Gb  CAN.o [1]
mbed::CAN::attach(mbed::Callback<void ()>, mbed::interface::can::IrqType)
                         0x800'55f3   0x8a  Code  Gb  CAN.o [1]
mbed::CAN::lock()        0x800'56a7    0xe  Code  Gb  CAN.o [1]
mbed::CAN::read(mbed::CANMessage &, int)
                         0x800'55cb   0x28  Code  Gb  CAN.o [1]
mbed::CAN::unlock()      0x800'56b5    0xe  Code  Gb  CAN.o [1]
mbed::CAN::write(mbed::CANMessage)
                         0x800'5589   0x42  Code  Gb  CAN.o [1]
mbed::CAN::~CAN()        0x800'552b   0x50  Code  Gb  CAN.o [1]
mbed::CANMessage::CANMessage()
                         0x800'57e9   0x34  Code  Gb  main.o [1]
mbed::Callback<void ()>::Callback()
                         0x800'5e0d   0x10  Code  Gb  InterruptIn.o [1]
mbed::Callback<void ()>::Callback(std::nullptr_t)
                         0x800'5d3d    0xe  Code  Gb  CAN.o [1]
mbed::Callback<void ()>::Callback<void (*)(), (int)0>(void (*)())
                         0x800'581d   0x30  Code  Gb  main.o [1]
mbed::Callback<void ()>::call() const
                         0x800'5d99   0x70  Code  Gb  InterruptIn.o [1]
mbed::Callback<void ()>::call() const::string
                         0x800'6dd4    0xc  Data  Gb  InterruptIn.o [1]
mbed::Callback<void ()>::call() const::string (instance 2)
                         0x800'637c   0x6c  Data  Gb  InterruptIn.o [1]
mbed::Callback<void ()>::operator ()() const
                         0x800'5e1d    0xc  Code  Gb  InterruptIn.o [1]
mbed::Callback<void ()>::operator =(const mbed::Callback<void ()>&)
                         0x800'5d69   0x1c  Code  Gb  InterruptIn.o [1]
mbed::Callback<void ()>::operator =(std::nullptr_t)
                         0x800'5d85   0x14  Code  Gb  InterruptIn.o [1]
mbed::Callback<void ()>::operator bool() const
                         0x800'5d4b   0x1a  Code  Gb  InterruptIn.o [1]
mbed::Callback<void ()>::~Callback()
                         0x800'584d    0xe  Code  Gb  main.o [1]
mbed::DigitalIn::DigitalIn(PinName, PinMode)
                         0x800'53bb   0x26  Code  Gb  main.o [1]
mbed::DigitalIn::operator int()
                         0x800'585b    0xc  Code  Gb  main.o [1]
mbed::DigitalIn::read()
                         0x800'5e95    0xc  Code  Gb  main.o [1]
mbed::DigitalIn::~DigitalIn()
                         0x800'6d9b    0xe  Code  Gb  main.o [1]
mbed::DigitalOut::DigitalOut(PinName, int)
                         0x800'5439   0x24  Code  Gb  main.o [1]
mbed::DigitalOut::operator =(int)
                         0x800'5867   0x12  Code  Gb  main.o [1]
mbed::DigitalOut::write(int)
                         0x800'5ea1   0x10  Code  Gb  main.o [1]
mbed::InterruptIn::InterruptIn(PinName, PinMode)
                         0x800'56c5   0x52  Code  Gb  InterruptIn.o [1]
mbed::InterruptIn::_irq_handler(unsigned int, gpio_irq_event)
                         0x800'57a5   0x44  Code  Gb  InterruptIn.o [1]
mbed::InterruptIn::irq_init(PinName)
                         0x800'5717   0x1a  Code  Gb  InterruptIn.o [1]
mbed::InterruptIn::rise(mbed::Callback<void ()>)
                         0x800'575d   0x48  Code  Gb  InterruptIn.o [1]
mbed::InterruptIn::~InterruptIn()
                         0x800'5731   0x24  Code  Gb  InterruptIn.o [1]
mbed::detail::CallbackBase::CallbackBase(std::nullptr_t)
                         0x800'60ed    0x6  Code  Gb  ObservingBlockDevice.o [1]
mbed::detail::CallbackBase::CallbackBase(std::nullptr_t) [subobject]
                         0x800'6047    0xe  Code  Gb  ObservingBlockDevice.o [1]
mbed::detail::CallbackBase::call_fn() const
                         0x800'6041    0x6  Code  Gb  ObservingBlockDevice.o [1]
mbed::detail::CallbackBase::clear()
                         0x800'5e29   0x10  Code  Gb  main.o [1]
mbed::detail::CallbackBase::control()
                         0x800'6055    0x4  Code  Gb  main.o [1]
mbed::detail::CallbackBase::control() const
                         0x800'6021    0x4  Code  Gb  ObservingBlockDevice.o [1]
mbed::detail::CallbackBase::copy(const mbed::detail::CallbackBase&)
                         0x800'6025   0x1c  Code  Gb  ObservingBlockDevice.o [1]
mbed::detail::CallbackBase::destroy()
                         0x800'5e81   0x14  Code  Gb  ObservingBlockDevice.o [1]
mbed::detail::CallbackBase::trivial_target_copy(mbed::detail::CallbackBase::Store &, mbed::detail::CallbackBase::Store const &)
                         0x800'6b15   0x1c  Code  Gb  main.o [1]
mbed::detail::CallbackBase::trivial_target_dtor(mbed::detail::CallbackBase::Store &)
                         0x800'1613    0x2  Code  Gb  main.o [1]
mbed_assert_internal     0x800'2d31   0x1c  Code  Gb  mbed_assert.o [1]
mbed_die                 0x800'38af    0x2  Code  Wk  mbed_board.o [1]
mbed_error               0x800'2f9d   0x60  Code  Wk  mbed_error.o [1]
mbed_error_in_progress  0x2400'030b    0x1  Data  Gb  mbed_error.o [1]
mbed_error_is_hw_fault   0x800'3367   0x22  Code  Lc  mbed_error.o [1]
mbed_error_printf        0x800'36ed   0x16  Code  Gb  mbed_board.o [1]
mbed_error_puts          0x800'3749   0x28  Code  Gb  mbed_board.o [1]
mbed_error_vprintf       0x800'3703   0x40  Code  Gb  mbed_board.o [1]
mbed_error_vprintf{2}{3}::ellipsis
                         0x800'6e04    0x8  Data  Lc  mbed_board.o [1]
mbed_halt_system         0x800'3365    0x2  Code  Lc  mbed_error.o [1]
memchr                   0x800'1475         Code  Gb  memchr.o [5]
ms_ticker_read           0x800'5965    0x6  Code  Gb  us_ticker.o [1]
mstd::invoke_result<void (*&)(), ...>::type mstd::invoke<void (*&)(), >(void (*&)() &&,  &&...)
                         0x800'6d7b   0x10  Code  Gb  main.o [1]
next_abi_dtor_rec       0x2400'02f8    0x4  Data  Lc  cppinit.o [2]
nvic_irq_enable          0x800'5027   0xb4  Code  Gb  gd32h7xx_misc.o [1]
nvic_priority_group_set
                         0x800'501d    0xa  Code  Gb  gd32h7xx_misc.o [1]
nvic_vector_table_set    0x800'50db   0x10  Code  Gb  gd32h7xx_misc.o [1]
operator new (unsigned int, void *)
                         0x800'6059    0x4  Code  Gb  mbed_rtc_time.o [1]
pin_function             0x800'2d4d   0xc4  Code  Gb  pinmap.o [1]
pin_mode                 0x800'2e11  0x170  Code  Gb  pinmap.o [1]
pinmap_find_function     0x800'2c7d   0x26  Code  Gb  mbed_pinmap_common.o [1]
pinmap_find_peripheral   0x800'2c13   0x26  Code  Gb  mbed_pinmap_common.o [1]
pinmap_function          0x800'2ca3   0x44  Code  Gb  mbed_pinmap_common.o [1]
pinmap_merge             0x800'2bdf   0x34  Code  Gb  mbed_pinmap_common.o [1]
pinmap_peripheral        0x800'2c39   0x44  Code  Gb  mbed_pinmap_common.o [1]
pinmap_pinout            0x800'2b89   0x56  Code  Gb  mbed_pinmap_common.o [1]
print_error_report       0x800'342d   0xfe  Code  Lc  mbed_error.o [1]
rcu_adc_clock_config     0x800'25d9   0x36  Code  Gb  gd32h7xx_rcu.o [1]
rcu_can_clock_config     0x800'258d   0x4c  Code  Gb  gd32h7xx_rcu.o [1]
rcu_periph_clock_disable
                         0x800'24f5   0x28  Code  Gb  gd32h7xx_rcu.o [1]
rcu_periph_clock_enable
                         0x800'24cd   0x28  Code  Gb  gd32h7xx_rcu.o [1]
rcu_periph_reset_disable
                         0x800'2545   0x28  Code  Gb  gd32h7xx_rcu.o [1]
rcu_periph_reset_enable
                         0x800'251d   0x28  Code  Gb  gd32h7xx_rcu.o [1]
rcu_timer_clock_prescaler_config
                         0x800'256d   0x20  Code  Gb  gd32h7xx_rcu.o [1]
scalbln                  0x800'1659         Code  Gb  ldexp.o [4]
scalblnl                 0x800'1659         Code  Gb  ldexp.o [4]
scalbn                   0x800'1659         Code  Gb  ldexp.o [4]
scalbnl                  0x800'1659         Code  Gb  ldexp.o [4]
scale                    0x800'126b   0x46  Code  Lc  xprintffull_nomb.o [2]
sec_hand                0x2400'0300    0x4  Data  Lc  xfail_s.o [2]
sleep_manager_lock_deep_sleep_internal
                         0x800'5d65    0x2  Code  Gb  mbed_power_mgmt.o [1]
sleep_manager_unlock_deep_sleep_internal
                         0x800'5d67    0x2  Code  Gb  mbed_power_mgmt.o [1]
state_saved             0x2400'030a    0x1  Data  Lc  mbed_critical_section_api.o [1]
std::enable_if<((!std::is_member_pointer<std::decay<void (*&)()>::type>::value)||(std::is_member_object_pointer<std::decay<void (*&)()>::type>::value&&(sizeof...(param#2)!=(int)1))), decltype((std::forward<void (*&)()>(param#1)(std::forward<>(param#2)...)))>::type mstd::impl::INVOKE<void (*&)(), >(void (*&)() &&,  &&...)
                         0x800'6d69   0x10  Code  Gb  main.o [1]
std::remove_reference<void (*&)()>::type && std::move<void (*&)()>(void (*&)() &&)
                         0x800'5e39    0x2  Code  Gb  main.o [1]
strchr                   0x800'13a1         Code  Gb  strchr.o [5]
strlen                   0x800'1425         Code  Gb  strlen.o [5]
strrchr                  0x800'2d17   0x1a  Code  Gb  strrchr.o [2]
syscfg_exti_line_config
                         0x800'4955   0x94  Code  Gb  gd32h7xx_syscfg.o [1]
system_clock_600m_hxtal
                         0x800'4ea1   0xd2  Code  Lc  system_gd32h7xx.o [1]
system_clock_config      0x800'4e99    0x8  Code  Lc  system_gd32h7xx.o [1]
temperature_sample_flag
                        0x2400'0308    0x1  Data  Gb  analogin_api.o [1]
timer_auto_reload_shadow_enable
                         0x800'41d5    0xa  Code  Gb  gd32h7xx_timer.o [1]
timer_deinit             0x800'3dfd  0x25c  Code  Gb  gd32h7xx_timer.o [1]
timer_enable             0x800'41cb    0xa  Code  Gb  gd32h7xx_timer.o [1]
timer_flag_clear         0x800'4239    0xa  Code  Gb  gd32h7xx_timer.o [1]
timer_init               0x800'4059  0x172  Code  Gb  gd32h7xx_timer.o [1]
timer_interrupt_enable   0x800'4243   0x10  Code  Gb  gd32h7xx_timer.o [1]
timer_interrupt_flag_clear
                         0x800'4297    0xa  Code  Gb  gd32h7xx_timer.o [1]
timer_interrupt_flag_get
                         0x800'4253   0x44  Code  Gb  gd32h7xx_timer.o [1]
us_ticker_init           0x800'58c7   0x9e  Code  Gb  us_ticker.o [1]
void (*&)() && std::forward<void (*&)()>(std::remove_reference<void (*&)()>::type &)
                         0x800'6d79    0x2  Code  Gb  main.o [1]
void core_util_atomic_store<PlatformMutex>(PlatformMutex **, PlatformMutex *)
                         0x800'6101    0x8  Code  Gb  mbed_rtc_time.o [1]
void mbed::Callback<void ()>::generate<void (*)(), void>(void (*)() &&)
                         0x800'5e3d   0x44  Code  Gb  main.o [1]
void mbed::Callback<void ()>::generate<void (*)(), void>(void (*)() &&)::ops
                         0x800'6de0    0xc  Data  Gb  main.o [1]
void mbed::Callback<void ()>::target_call<void (*)()>(mbed::detail::CallbackBase const *)
                         0x800'6dc7    0xe  Code  Gb  main.o [1]
void mbed::detail::invoke_r<void, void (*&)(), , (int)0>(void (*&)() &&,  &&...)
                         0x800'6d8b   0x10  Code  Gb  main.o [1]
vsnprintf                0x800'380d   0x3c  Code  Gb  vsnprint.o [2]
write                    0x800'3849   0x56  Code  Gb  mbed_retarget.o [1]


[1] = G:\work\GD32\mbed\projects\iar-build\IAR\gd32h7_test\debug\obj
[2] = dl7M_tlf.a
[3] = dlpp7M_tl_fc.a
[4] = m7M_tlv.a
[5] = rt7M_tl.a
[6] = shb_l.a

  25'872 bytes of readonly  code memory
   2'976 bytes of readonly  data memory
  17'609 bytes of readwrite data memory

Errors: none
Warnings: 2
